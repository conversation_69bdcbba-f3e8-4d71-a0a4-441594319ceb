<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ext.BRMMapperExt">
  <select id="getBRMUploadList"  resultType="com.chinamobile.iot.sc.pojo.dto.BRMUploadDTO">
      select atom.id,
             sku.id as skuId,
             atom.inventory_main_id as inId,
             sku.template_id            as cardTempleCode,
             sku.template_name          as cardTempleName,
             sku.cust_code              as cardVenderCode,
             sku.cust_name              as cardVenderName,
             if(didi.location is null, didi.be_id, didi.location) as region,
             boi.goods_id               as bossId,
             dimi.device_version        as xModelName,
             boi.iot_mall_offering_name as vasName,
             didi.reserve_quatity       as xPickNum,
      if(didi.current_inventory &lt; 0,0,didi.current_inventory)     as xStockNum,
      if(cimi.current_inventory &lt; 0,0,cimi.current_inventory)      as cardStockNum,
             cimi.reserve_quatity       as cardPickNum,
             if(s.saleNum is null,0,s.saleNum)                  as saleNum
      from
          sku_offering_info sku
              left join benefit_offering bo on sku.offering_code = bo.sku_offering_code
              left join benefit_offerings_info boi on bo.benefit_offerings_info_id = boi.id

              left join atom_offering_info atom on atom.sku_code = bo.sku_offering_code
              left join dkcardx_inventory_main_info dimi on dimi.id = atom.inventory_main_id
              inner join dkcardx_inventory_detail_info didi on didi.inventory_main_id = atom.inventory_main_id
              left join card_inventory_main_info cimi
                        on sku.template_id = cimi.template_id and sku.cust_code = cimi.cust_code
              LEFT JOIN (SELECT subquery.sku_offering_code,
                                COUNT(subquery.sku_quantity) AS saleNum
                         FROM (
                                  SELECT DISTINCT o2cai.order_id, o2cai.sku_offering_code,o2cai.sku_quantity
                                  FROM order_2c_atom_info o2cai
                                  WHERE o2cai.order_status = 7

                              ) AS subquery
                         GROUP BY subquery.sku_offering_code) s ON s.sku_offering_code = sku.offering_code
      where 1 = 1
        and boi.goods_id is not null
        and sku.offering_status = '1'
        and sku.delete_time is null
        and boi.iot_release_area_id in
            ('200', '7530', '7600', '7620', '7520', '6600', '7540', '7510', '7500', '6680', '7680', '2000', '7630', '7660',
             '6620', '7560', '7590', '7570', '7690', '7550', '7580', '6630')
        and atom.card_containing_terminal = '1'
        and sku.template_id is not null
  </select>

  <select id="getBRMDetailUploadList" resultType="com.chinamobile.iot.sc.pojo.dto.BRMDetailUploadDTO">
      select
          dimi.device_version as deviceModel,
          sku.template_id as cardTemplateCode,
          '中国移动' as brand,
          'IOT-BRM-ECSS' as dataSource,
          if(didi.location is null, didi.be_id, didi.location) as regionCode,
          didi.reserve_quatity as reserveQuantity,
          if(didi.current_inventory &lt; 0, 0, didi.current_inventory) as currentInventory,
          didi.total_inventory as totalInventory,
          -- 可用设备明细（IMEI列表，用&,隔开）
          GROUP_CONCAT(DISTINCT CASE WHEN dcdi.sell_status = '1' THEN dcdi.imei END SEPARATOR '&,') as availableDeviceDetails,
          -- 销量数
          if(s.saleNum is null, 0, s.saleNum) as salesQuantity,
          -- 销量设备明细（已销售的IMEI列表，用&,隔开）
          GROUP_CONCAT(DISTINCT CASE WHEN dcdi.sell_status = '3' THEN dcdi.imei END SEPARATOR '&,') as salesDeviceDetails,
          -- 关联规格商品（规格商品编码|昨日销量数|昨日销量数|规格商品名称，用&,隔开，字段用&#隔开）
          GROUP_CONCAT(DISTINCT CONCAT(sku.offering_code, '&#', if(s.saleNum is null, 0, s.saleNum), '&#', sku.offering_name) SEPARATOR '&,') as relatedSkuProducts,
          -- 可用设备明细条数
          COUNT(DISTINCT CASE WHEN dcdi.sell_status = '1' THEN dcdi.imei END) as availableDeviceCount,
          -- 销量设备明细条数
          COUNT(DISTINCT CASE WHEN dcdi.sell_status = '3' THEN dcdi.imei END) as salesDeviceCount
      from
          sku_offering_info sku
              left join benefit_offering bo on sku.offering_code = bo.sku_offering_code
              left join benefit_offerings_info boi on bo.benefit_offerings_info_id = boi.id
              left join atom_offering_info atom on atom.sku_code = bo.sku_offering_code
              left join dkcardx_inventory_main_info dimi on dimi.id = atom.inventory_main_id
              inner join dkcardx_inventory_detail_info didi on didi.inventory_main_id = atom.inventory_main_id
              left join dkcardx_card_detail_info dcdi on dcdi.inventory_detail_id = didi.id
              left join card_inventory_main_info cimi
                        on sku.template_id = cimi.template_id and sku.cust_code = cimi.cust_code
              LEFT JOIN (SELECT subquery.sku_offering_code,
                                COUNT(subquery.sku_quantity) AS saleNum
                         FROM (
                                  SELECT DISTINCT o2cai.order_id, o2cai.sku_offering_code, o2cai.sku_quantity
                                  FROM order_2c_atom_info o2cai
                                  WHERE o2cai.order_status = 7
                              ) AS subquery
                         GROUP BY subquery.sku_offering_code) s ON s.sku_offering_code = sku.offering_code
      where 1 = 1
        and boi.goods_id is not null
        and sku.offering_status = '1'
        and sku.delete_time is null
        and boi.iot_release_area_id in
            ('200', '7530', '7600', '7620', '7520', '6600', '7540', '7510', '7500', '6680', '7680', '2000', '7630', '7660',
             '6620', '7560', '7590', '7570', '7690', '7550', '7580', '6630')
        and atom.card_containing_terminal = '1'
        and sku.template_id is not null
      GROUP BY dimi.device_version, sku.template_id, didi.location, didi.be_id, didi.reserve_quatity,
               didi.current_inventory, didi.total_inventory
  </select>
</mapper>