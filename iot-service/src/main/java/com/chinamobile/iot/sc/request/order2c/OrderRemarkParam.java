package com.chinamobile.iot.sc.request.order2c;

import lombok.Data;

import javax.validation.constraints.NotEmpty;


/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date : 2022/5/5 10:55
 * @description: 订单备注请求参数
 **/
@Data
public class OrderRemarkParam {

    /**
     * 订单id
     */
    @NotEmpty(message = "订单流水号不能为空")
    private String orderId;

    /**
     * 订单备注
     */
    @NotEmpty(message = "订单备注不能为空")
    private String orderRemark;
}
