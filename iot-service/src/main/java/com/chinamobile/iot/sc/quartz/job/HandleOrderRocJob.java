package com.chinamobile.iot.sc.quartz.job;

import com.alibaba.fastjson.JSON;
import com.chinamobile.iot.sc.quartz.QuartzJobConf;
import com.chinamobile.iot.sc.service.IOrder2CService;
import lombok.extern.slf4j.Slf4j;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author: YSC
 * @Date: 2021/12/16 10:22
 * 72小时自动处理订单
 */
@Slf4j
@Component
@DisallowConcurrentExecution
public class HandleOrderRocJob implements Job {
    @Autowired
    private IOrder2CService iOrder2CService;

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        QuartzJobConf jobConf = JSON.parseObject(context.getJobDetail().getJobDataMap().getString("quartConf"), QuartzJobConf.class);
        iOrder2CService.autoAgreeOrderRoc(jobConf);
    }
}
