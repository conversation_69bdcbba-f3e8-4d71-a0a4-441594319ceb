package com.chinamobile.iot.sc.request.invoice;

import lombok.Data;

import java.util.List;

/**
 * @package: com.chinamobile.iot.sc.request.invoice
 * @ClassName: InvoiceReverseDTO
 * @description: 发票冲红IOT请求DTO
 * @author: zyj
 * @create: 2021/12/28 19:49
 * @Company: Copyright 2021-2099 CMIot Tech. Co. Ltd.  All Rights Reserved.
 **/
@Data
public class InvoiceReverseDTO {
    /**
     * 请求流水号, 发票冲红业务的请求流水（必填）
     */
    private String orderSeq;
    /**
     * 业务订单流水号, 销售业务的唯一标识（必填）
     */
    private String orderId;
    /**
     * 操作类型, 01：冲红、02：作废（必填）
     */
    private String operType;
    /**
     * 客户类型, 0：个人客户、1：集团客户
     */
    private String customerType;
    /**
     * 客户编码, 根据客户类型填写，CustomerType为（必填）
     * 0：个人号码MSISDN或个人客户编码
     * 1：集团客户在BBOSS的唯一标识
     */
    private String customerNumber;
    /**
     * 请求流水号, 发票冲红业务的请求流水（必填）
     */
    private List<VoucherInfoDTO> voucherInfo;

}
