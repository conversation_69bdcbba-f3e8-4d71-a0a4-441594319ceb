package com.chinamobile.iot.sc.feign;

import com.chinamobile.iot.sc.request.order2c.SoftServiceParam;
import com.chinamobile.iot.sc.response.SoftServiceResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;

/**
 * <AUTHOR> xie<PERSON><PERSON><PERSON>
 * @date : 2025/7/28 10:03
 * @description: 行车卫士业务平台feign客服端
 **/
@Service
@FeignClient(name = "xcws-soft-open-feign-client", url = "${softService.xcws.openUrl}")
public interface XCWSSoftOpenClient {

    /**
     * 行车卫士业务平台服务开通
     *
     * @param param
     * @return
     */
    @PostMapping("/pro/iotMall/order")
    SoftServiceResponse softServiceOpen(@SpringQueryMap Map<String, String> signParam, @RequestBody SoftServiceParam param);
}
