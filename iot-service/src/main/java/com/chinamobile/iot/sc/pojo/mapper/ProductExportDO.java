package com.chinamobile.iot.sc.pojo.mapper;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * created by l<PERSON><PERSON><PERSON> on 2023/2/7 11:24
 */
@Data
public class ProductExportDO {

    /**
     * 商品组/销售商品名称
     */
    private String spuOfferingName;

    /**
     * 商品组/销售商品编码
     */
    private String spuOfferingCode;
    /**
     * 商品类型
     */
    private String spuOfferingClass;
//    /**
//     * 销售对象
//     */
//    private String saleObject;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 销售商品状态
     */
    private String spuOfferingStatus;

    /**
     * 商品名称（规格）
     */
    private String skuOfferingName;

    /**
     *商品编码（规格）
     */
    private String skuOfferingCode;
    /**
     * 销售目录价（规格）
     */
    private Double price;
    /**
     * 规格商品状态
     */
    private String skuOfferingStatus;


    /**
     * 规格发布省
     */
    private String skuReleaseProvince;
    /**
     * 规格发布市
     */
    private String skuReleaseCity;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 原子商品名称
     */
    private String atomOfferingName;
    /**
     * 原子商品id
     */
    private String atomId;
    /**
     * 原子商品编码
     */
    private String atomOfferingCode;
    /**
     * 原子商品类型
     */
    private String atomOfferingClass;
    /**
     * 软件平台编码
     */
    private String extSoftOfferingCode;
    /**
     * 终端物料编码
     */
    private String extHardOfferingCode;
    /**
     * 数量（原子商品）
     */
    private Long atomQuantity;
    /**
     * 型号（原子商品）
     */
    private String model;
    /**
     * 颜色（原子商品）
     */
    private String color;
    /**
     * 销售价（原子商品）
     */
    @Excel(name = "销售价（原子商品）")
    private Double atomSalePrice;
    /**
     * 结算单价（原子商品）
     */
    private Double settlePrice;
    /**
     * 销售省/市区域（原子商品）
     */
    private String offeringSaleRegion;

    /**
     * 合作伙伴名称
     */
    private String partnerName;
    /**
     * 联系人姓名
     */
    private String cooperatorName;

    /**
     * 标准服务编码
     */
    private String standardServiceCode;
    /**
     * 标准服务名称
     */
    private String standardServiceName;
    /**
     * 实质产品名称
     */
    private String realProductName;
    /**
     * 产品部门
     */
    private String department;
    /**
     * 产品属性
     */
    private String property;

    private String remark1;

    private String remark2;

    /**
     * 销售商品版本号
     */
    private String spuOfferingVersion;

    /**
     * 规格商品版本号
     */
    private String skuOfferingVersion;

    /**
     * 原子商品版本号
     */
    private String atomOfferingVersion;

    /**
     * 一级导航目录名称
     */
    private String firstNavigationName;

    /**
     * 二级导航目录名称
     */
    private String secondNavigationName;

    /**
     * 三级导航目录名称
     */
    private String thirdNavigationName;

    /**
     * 商品关键字
     */
    private String productKeywords;

    /**
     * 是否隐秘上架,0：是 , 1：否
     */
    private String secretlyListed;

    /**
     * 商品简介
     */
    private String productDescription;

    /**
     * 主销售标签
     */
    private String mainSaleLabel;

    /**
     * 副销售标签
     */
    private String subSaleLabel;

    /**
     * 接单合作伙伴名称
     */
    private String receiveOrderName;

    /**
     * 接单合作伙伴电话
     */
    private String receiveOrderPhone;

    /**
     * 交付合作伙伴名称
     */
    private String deliverName;

    /**
     * 交付合作伙伴电话
     */
    private String deliverPhone;

    /**
     * 售后合作伙伴名称
     */
    private String aftermarketName;

    /**
     * 售后合作伙伴电话
     */
    private String aftermarketPhone;

    /**
     * 账目项名称
     */
    private String chargeCode;

    /**
     * 账目项编码
     */
    private String chargeId;
}
