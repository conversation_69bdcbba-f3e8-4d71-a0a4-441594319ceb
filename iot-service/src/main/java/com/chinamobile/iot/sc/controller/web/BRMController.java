package com.chinamobile.iot.sc.controller.web;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.pojo.param.IOPUploadParam;
import com.chinamobile.iot.sc.service.BRMService;
import com.chinamobile.iot.sc.util.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.util.Date;
import java.util.List;


/**
 * BRM数据管理控制器
 */
@RestController
@RequestMapping("/osweb/brm")
@Slf4j
public class BRMController {

    @Autowired
    private BRMService brmService;

    /**
     * 导出BRM数据
     * 
     * @param param 导出参数
     * @return 文件下载
     */
    @PostMapping("/export")
    public ResponseEntity<Resource> exportBRMData() {
        try {
            String filePath = brmService.exportBRMData();
            File file = new File(filePath);

            if (!file.exists()) {
                return ResponseEntity.notFound().build();
            }

            Resource resource = new FileSystemResource(file);

            return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + file.getName() + "\"")
                    .body(resource);
        } catch (Exception e) {
            log.error("BRM数据导出失败", e);
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 手动触发BRM数据上传
     *
     * @param startDate 开始日期（可选，格式：yyyy-MM-dd，默认为前一天）
     * @param endDate 结束日期（可选，格式：yyyy-MM-dd，默认为前一天）
     * @param type 类型（可选，a-增量，z-首次，默认为a）
     * @return 上传结果
     */
    @PostMapping("/trigger")
    public BaseAnswer<String> triggerBRMUpload(
            @RequestParam(value = "startDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam(value = "endDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate,
            @RequestParam(value = "type", required = false, defaultValue = "a") String type) {
        try {
            brmService.triggerBRMUpload(startDate, endDate, type);
            return BaseAnswer.success("BRM数据上传触发成功");
        } catch (Exception e) {
            log.error("手动触发BRM数据上传失败", e);
            return new BaseAnswer<>("500", "BRM数据上传触发失败：" + e.getMessage());
        }
    }

    /**
     * 生成BRM详细数据文本文件
     *
     * @param fileName 文件名前缀（可选，默认使用日期格式）
     * @return 生成结果
     */
    @PostMapping("/generateDetailFiles")
    public BaseAnswer<List<String>> generateDetailFiles(
            @RequestParam(value = "fileName", required = false) String fileName) {
        try {
            // 如果没有传入文件名，使用默认格式
            if (fileName == null || fileName.trim().isEmpty()) {
                String dateStr = DateTimeUtil.formatDate(new Date(), "yyyyMMdd");
                fileName = "BRM_DETAIL_" + dateStr;
            }

            List<String> generatedFiles = brmService.generateDetailTxtFiles(fileName, true);
            return BaseAnswer.success(generatedFiles);
        } catch (Exception e) {
            log.error("生成BRM详细数据文件失败", e);
            return new BaseAnswer<>("500", "生成BRM详细数据文件失败：" + e.getMessage());
        }
    }

}