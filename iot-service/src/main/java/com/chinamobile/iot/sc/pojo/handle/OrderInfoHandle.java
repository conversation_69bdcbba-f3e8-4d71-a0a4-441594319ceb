package com.chinamobile.iot.sc.pojo.handle;

import lombok.Data;

import java.util.Date;

@Data
public class OrderInfoHandle {

    /**
     * 订单ID
     */
    private String id;
    /**
     * 下单时间
     */
    private String createTime;
    /**
     * 订单号
     */
    private String orderId;

    /**
     * 商品组/销售商品名称
     */
    private String spuOfferingName;

    /**
     * 商品组/销售商品编码
     */
    private String spuOfferingCode;

    /**
     * 商品名称(规格)
     */
    private String skuOfferingName;

    /**
     * 商品规格编码
     */
    private String skuOfferingCode;

    /**
     * 原子商品名称
     */
    /*private String atomOfferingName;
    *//**
     * 原子商品编码
     *//*
    private String atomOfferingCode;
    *//**
     * 原子商品类型
     *//*
    private String atomOfferingClass;*/
    /**
     * 供应商名称
     */
    private String supplierName;
    /**
     * 型号
     */
    /*private String model;
    *//**
     * 颜色
     *//*
    private String color;
    *//**
     * 订购数量
     *//*
    private Integer quantity;
    *//**
     * 原子商品结算价 单价
     *//*
    private Long atomPrice;*/
    /**
     * 订购数量(规格)
     */
    private Integer skuQuantity;
    /**
     * 价格(规格)
     */
    private String skuPrice;
    /**
     * 合作伙伴ID
     */
    /*private String cooperatorId;
    *//**
     * 订单完结合作伙伴ID
     *//*
    private String finishCooperatorId;
    *//**
     * 订单完结合作伙伴名称
     *//*
    private String finishCooperatorName;
    *//**
     * 合作伙伴姓名
     *//*
    private String cooperatorName;*/
    /**
     * 合作伙伴名
     */
    private String partnerName;
    /**
     * 订单状态 0待发货 1待收货 2已完成
     */
    private Integer orderStatus;
    /**
     * SPU一级销售目录
     */
    private String spuOfferingClass;
    /**
     * 业务编码
     */
    private String businessCode;

    /**
     * 订单生成月数据的主键id
     */
    private String syncK3Id;

    /**
     * 是否经过特殊的售后处理标识  0--未经过  1--经过
     */
    private Integer specialAfterMarketHandle;

    /**
     * 特殊的售后状态：1-待退款 2-退款中 3-退款成功 4-退款取消
     */
    private String specialAfterStatus;

    /**
     * 特殊的售后截止时间
     */
    private String specialAfterLatestTime;
    /**
     * 特殊的售后数量
     */
    private String specialAfterRefundsNumber;

    /**
     * 订单类型  01-- 自主下单 00-- 代客下单
     */
    private String orderType;

    /**
     * 增加地址用来判断是否是湖南类合同履约
     */
    private String addr1;

    /**
     * 千里眼订单服务开通状态
     */
    private Integer qlyStatus;

    /**
     * 云视讯订单服务开通状态
     */
    private Integer ysxStatus;

    /**
     * 商品封面图url
     *
     */
    private String imgUrl;

    /**
     * 图片外部链接路径
     */
    private String url;

    /**
     * 行车卫士订购结果  0--开通失败  1--开通成功  2--退订失败 3--退订成功
     */
    private Integer carOpenStatus;

    /**
     * 平台软件编码
     */
    private String extSoftOfferingCode;

    /**
     * 卡+X订单退款状态 0--申请退款 1--同意退款  2--不同意退款 3--取消退款
     */
    private Integer kxRefundStatus;
    /**
     * 卡+X接单结果  1--接单 2--拒单
     */
    private Integer allowOrderStatus;
    /**
     * 订单总金额
     */
    private String totalPrice;
    /**
     * 软件服务开通状态
     */
    private Integer softServiceStatus;
    /**
     * 软件服务所有原子开通状态,目前业务只需要开通成功和开通失败  0-开通成功，1-开通失败
     */
    private Integer softServiceAllStatus;
    /**
    /**
     *
     * 软件服务同步Iot状态  0--默认值，未同步；1--开通成功同步iot商城失败;2使用中同步iot商城失败
     */
    private Integer syncIotFailStatus;

    /**
     * 软件服务使用中时间
     */
    private Date softServiceUseTime;

    /**
     * X产品类型,1:5G CPE,2:5G 快线,3:千里眼,
     * 4:合同履约,5:OneNET独立服务,6:标准产品(OneNET）,
     * 7:OnePark独立服务,8:标准产品（OnePark）
     */
    private String productType;

    /**
     * 订单卡+X类型
     */
    private String cardType;

    private Date updateTime;


    private String cardContainingTerminal;

    /**
     * 结算状态，0-销售草稿（锁定），1-销售草稿， 2-销售审批中， 3-销售已审批，
     * 4-计收完成， 5-订单取消 6--待发起 7--草稿单 8--审批中 9--已审批 10--取消中 11--已取消
     */
    private Integer settleStatus;

    /**
     * 在线结算采购订单结算状态 6--待发起 7--草稿单 8--审批中 9--已审批 10--取消中 11--已取消  12--同步失败
     */
    private Integer onlineSettleStatus;

    /**
     * SCM采购订单编号
     */
    private String scmOrderNum;

    /**
     * 待发货催单次数
     */
    private Integer reminderWaitSend;

    /**
     * 待接单催单次数
     */
    private Integer reminderValetTaking;

    /**
     * 待交付催单次数
     */
    private Integer reminderWaitDeliver;

    /**
     * 是否需要合作伙伴接单,1：是,2：否
     */
    private String receiveOrder;

}
