package com.chinamobile.iot.sc.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * created by l<PERSON><PERSON><PERSON> on 2023/4/17 17:56
 */
@Component
@Data
public class ServiceConfig {

    @Value("${iot.encodeKey}")
    private String encodeKey;

    @Value("${sms.newMessage:107024}")
    private String newMessageSms;

    private Integer orderExportExcelExpireDays = 3;

}
