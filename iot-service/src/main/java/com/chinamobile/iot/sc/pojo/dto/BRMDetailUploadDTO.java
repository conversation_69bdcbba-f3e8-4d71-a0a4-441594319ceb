package com.chinamobile.iot.sc.pojo.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * BRM详细数据上传DTO
 * 用于生成新的文本格式数据
 */
@Data
public class BRMDetailUploadDTO implements Serializable {
    
    /**
     * 设备型号
     */
    private String deviceModel;
    
    /**
     * 开卡模板编码
     */
    private String cardTemplateCode;
    
    /**
     * 品牌
     */
    private String brand;
    
    /**
     * 数据来源
     */
    private String dataSource;
    
    /**
     * 地区标识
     */
    private String regionCode;
    
    /**
     * 库存预占数
     */
    private Long reserveQuantity;
    
    /**
     * 当前库存数
     */
    private Long currentInventory;
    
    /**
     * 总库存数
     */
    private Long totalInventory;
    
    /**
     * 可用设备明细（多条数据用&,隔开）
     */
    private String availableDeviceDetails;
    
    /**
     * 销量数
     */
    private Long salesQuantity;
    
    /**
     * 销量设备明细（多条数据用&,隔开）
     */
    private String salesDeviceDetails;
    
    /**
     * 关联规格商品（多条数据用&,隔开，单条数据中不同字段用&#隔开）
     */
    private String relatedSkuProducts;
    
    /**
     * 可用设备明细条数
     */
    private Integer availableDeviceCount;
    
    /**
     * 销量设备明细条数
     */
    private Integer salesDeviceCount;
    
    /**
     * 获取总条数（可用设备明细条数 + 销量设备明细条数）
     */
    public Integer getTotalCount() {
        int availableCount = availableDeviceCount != null ? availableDeviceCount : 0;
        int salesCount = salesDeviceCount != null ? salesDeviceCount : 0;
        return availableCount + salesCount;
    }
}
