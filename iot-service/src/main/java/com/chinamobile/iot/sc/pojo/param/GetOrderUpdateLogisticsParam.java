package com.chinamobile.iot.sc.pojo.param;

import com.chinamobile.iot.sc.request.LogisticsInfoRequest;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/9
 * @description 接单更新物流参数
 */
@Data
public class GetOrderUpdateLogisticsParam {

    /**
     * 原子订单ID
     */
    @NotEmpty(message = "原子订单ID不能为空")
    private String id;

    /**
     * 订单ID
     */
    @NotEmpty(message = "订单ID不能为空")
    private String orderId;

    /**
     * 物流信息集合
     */
    @NotNull(message = "物流信息不能为空")
    private List<LogisticsInfoRequest.LogisticsMsg> logisticsMsgs;
}
