package com.chinamobile.iot.sc.pojo.param;

import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/8
 * @description 接单详情参数
 */
@Data
public class GetOrderDetailParam {

    /**
     * 原子订单ID
     */
    /*@NotEmpty(message = "原子订单ID不能为空")
    private String id;*/

    /**
     * 订单ID
     */
    @NotEmpty(message = "订单ID不能为空")
    private String orderId;
}
