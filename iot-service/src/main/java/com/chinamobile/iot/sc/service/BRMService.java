package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.pojo.param.IOPUploadParam;

import java.util.Date;
import java.util.List;

public interface BRMService {

    /**
     * 上传文件到brm
     */
    void sftpUploadBRM(IOPUploadParam param);

    /**
     * 导出BRM数据
     *
     * @param param 导出参数
     * @return 文件路径
     */
    String exportBRMData();

    /**
     * 手动触发BRM数据上传
     *
     * @param startDate 开始日期（可选，默认为前一天）
     * @param endDate 结束日期（可选，默认为前一天）
     * @param type 类型（可选，a-增量，z-首次，默认为a）
     */
    void triggerBRMUpload(Date startDate, Date endDate, String type);

    /**
     * 生成BRM详细数据文本文件
     *
     * @param fileName 文件名前缀
     * @param isWrite 是否写入文件
     * @return 生成的文件名列表
     */
    List<String> generateDetailTxtFiles(String fileName, Boolean isWrite);
}
