package com.chinamobile.install.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.asiainfo.openplatform.common.util.SecurityUtils;
import com.asiainfo.openplatform.common.util.SignUtil;
import com.chinamobile.install.config.ProvinceEncryptConfig;
import com.chinamobile.install.config.RestTemplateConfig;
import com.chinamobile.install.dao.*;
import com.chinamobile.install.dao.ext.AfterMarketOrder2cInfoMapperExt;
import com.chinamobile.install.enums.*;
import com.chinamobile.install.excel.OrderHenanZwImportExcel;
import com.chinamobile.install.exception.B2BConstant;
import com.chinamobile.install.exception.ServicePowerException;
import com.chinamobile.install.exception.StatusConstant;
import com.chinamobile.install.feign.GuangdongOpenFeignClient;
import com.chinamobile.install.feign.HenanOpenFeignClient;
import com.chinamobile.install.feign.ShandongOpenFeignClient;
import com.chinamobile.install.pojo.dto.CategoryInfoDTO;
import com.chinamobile.install.pojo.dto.Order2cAtomInfoDTO;
import com.chinamobile.install.pojo.dto.SpuOfferingInfoDTO;
import com.chinamobile.install.pojo.entity.*;
import com.chinamobile.install.pojo.param.HenanZwAuthenticationBody;
import com.chinamobile.install.pojo.param.QueryOrderHenanZwParam;
import com.chinamobile.install.pojo.param.SaveSheetHistoryParam;
import com.chinamobile.install.pojo.vo.OrderHenanZwDetailVO;
import com.chinamobile.install.pojo.vo.OrderHenanZwHistoryVO;
import com.chinamobile.install.pojo.vo.OrderHenanZwListItemVO;
import com.chinamobile.install.request.*;
import com.chinamobile.install.response.AuthenticationResponse;
import com.chinamobile.install.response.BaseZwResponse;
import com.chinamobile.install.response.HenanOpenResponse;
import com.chinamobile.install.response.ProgressSyncResponseBody;
import com.chinamobile.install.service.*;
import com.chinamobile.install.util.IOTRequestUtils;
import com.chinamobile.install.util.SignUtils;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.entity.b2b.CommonProvinceSyncParam;
import com.chinamobile.iot.sc.entity.b2b.HenanZwSendOrderBusiNewParam;
import com.chinamobile.iot.sc.entity.b2b.HenanZwSendOrderBusiParam;
import com.chinamobile.iot.sc.entity.b2b.OrderHenanZwFeignDeviceInfo;
import com.chinamobile.iot.sc.entity.iot.ServiceOrderResultRequest;
import com.chinamobile.iot.sc.enums.ProvinceInstallPlatformEnum;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.feign.UserFeignClient;
import com.chinamobile.iot.sc.mode.Data4User;
import com.chinamobile.iot.sc.mode.IOTAnswer;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.util.DateUtils;
import com.chinamobile.iot.sc.util.IOTEncodeUtils;
import com.google.common.base.Joiner;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.http.util.TextUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/19 15:31
 * @description TODO
 */
@Service
@Slf4j
public class OrderHenanZwServiceImpl implements OrderHenanZwService {
    @Resource
    private RedisTemplate redisTemplate;
    @Value("${install.encodeKey}")
    private String encodeKey;
    @Resource
    private ProvinceEncryptConfig provinceEncryptConfig;
    @Value("${install.sm4Key}")
    private String iotSm4Key;
    @Value("${install.sm4Iv}")
    private String iotSm4Iv;
    @Value("${install.secretKey}")
    private String secretKey;
    @Value("${zhuangwei.methodCode.commonOpenCreate}")
    private String commonOpenCreate;
    @Resource
    private OrderHenanZwMapper orderHenanZwMapper;

    @Resource
    private OrderHenanZwHistoryService orderHenanZwHistoryService;

    @Resource
    private HenanZwCityServiceImpl henanZwCityService;


    @Resource
    private HenanZwAreaService henanZwAreaService;

    @Resource
    private UserFeignClient userFeignClient;

    @Resource
    private SpuOfferingInfoService spuOfferingInfoService;

    @Resource
    private Order2cAtomSnService order2cAtomSnService;

    @Resource
    private OrderStockItemSnService orderStockItemSnService;

    @Resource
    private Order2cAtomInfoService order2CAtomInfoService;
    @Value("${install.serviceOrderResultUrl}")
    private String serviceOrderResultUrl;

    @Resource
    private CategoryInfoService categoryInfoService;

    @Resource
    private HenanZwSheetActionService henanZwSheetActionService;


    @Resource
    private HenanOpenFeignClient henanOpenFeignClient;
    @Resource
    private ShandongOpenFeignClient shandongOpenFeignClient;
    @Resource
    private GuangdongOpenFeignClient guangdongOpenFeignClient;
    @Resource
    private Order2cAtomSnMapper order2cAtomSnMapper;




    /**
     * 河南业编认证方法Code
     */
    @Value("${zhuangwei.methodCode.authentication}")
    private String methodCodeAuthentication;
    /**
     * 河南烟气感业编认证方法Code
     */
    @Value("${zhuangwei.methodCode.commonAuthentication}")
    private String commonAuthentication;

    /**
     * 河南业编认证方法Code
     */
    @Value("${zhuangwei.methodCode.openCreate}")
    private String methodCodeOpenCreate;

    private static final SimpleDateFormat SDF = new SimpleDateFormat("yyyyMMdd");

    private static final String ORDER_PREFIX = "jkiot";

    private static final String HENAN_ZW_SUBSCRIBER = "ORCHESTRATION";

    @Resource
    private AfterMarketOrder2cInfoMapper afterMarketOrder2cInfoMapper;
    @Resource
    private AfterMarketOrder2cInfoMapperExt afterMarketOrder2cInfoMapperExt;
    @Resource
    private MallAddress2ProvinceAddressInfoMapper mallAddress2ProvinceAddressInfoMapper;

    @Resource
    private AftermarketOrderHistoryMapper aftermarketOrderHistoryMapper;
    @Resource
    private AftermarketOfferingCodeMapper aftermarketOfferingCodeMapper;
    @Resource
    private Order2cAtomInfoMapper atomOrderInfoMapper;

    @Resource
    private AfterMarketOrder2cOfferingInfoMapper afterMarketOrder2cOfferingInfoMapper;

    @Resource
    private LogisticsInfoMapper logisticsInfoMapper;

    /**
     * 导入装维工单
     *
     * @param file
     * @param loginIfo4Redis
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> importOrders(MultipartFile file, LoginIfo4Redis loginIfo4Redis) {
        List<String> failedReasons = new ArrayList<>();
        try {
            InputStream inputStream = file.getInputStream();
            List<OrderHenanZwImportExcel> excels = EasyExcel.read(inputStream, OrderHenanZwImportExcel.class, null)
                    .ignoreEmptyRow(true).sheet(0).headRowNumber(1).doReadSync();
            if (!CollectionUtils.isEmpty(excels)) {

                Date now = new Date();
                log.info("Excel数据: {}", JSON.toJSONString(excels));
                for (OrderHenanZwImportExcel excel : excels) {
                    if (isAllFieldsNull(excel)) {
                        // 读到Excel空行
                        continue;
                    }
                    String orderNo = excel.getOrderNo();
                    if (orderNo.startsWith(ORDER_PREFIX)) {
                        orderNo = orderNo.substring(ORDER_PREFIX.length());
                    }
                    String orderNoWithPrefix = ORDER_PREFIX + orderNo;

                    // 异常处理
                    if (null == excel.getOrderNo()) {
                        throw new BusinessException("-1", "excel解析异常，缺少订单号");
                    } else if (null == excel.getOrderType()) {
                        failedReasons.add("订单号：" + orderNoWithPrefix + ", 失败原因：缺少工单类别");
                        continue;
                    } else if (null == excel.getBusinessTypeString()) {
                        failedReasons.add("订单号：" + orderNoWithPrefix + ", 失败原因：缺少装维类型");
                        continue;
                    } else if (null == excel.getCustomContact()) {
                        failedReasons.add("订单号：" + orderNoWithPrefix + ", 失败原因：客户联系人不能为空");
                        continue;
                    } else if (null == excel.getCustomContactPhone()) {
                        failedReasons.add("订单号：" + orderNoWithPrefix + ", 失败原因：客户联系电话不能为空");
                        continue;
                    } else if (null == excel.getProvinceName()) {
                        failedReasons.add("订单号：" + orderNoWithPrefix + ", 失败原因：安装地市所属省份不能为空");
                        continue;
                    } else if (null == excel.getCityName()) {
                        failedReasons.add("订单号：" + orderNoWithPrefix + ", 失败原因：安装地市所属地市不能为空");
                        continue;
                    } else if (null == excel.getAreaName()) {
                        failedReasons.add("订单号：" + orderNoWithPrefix + ", 失败原因：安装地市所属区县不能为空");
                        continue;
                    } else if (null == excel.getAddress()) {
                        failedReasons.add("订单号：" + orderNoWithPrefix + ", 失败原因：安装详细地址不能为空");
                        continue;
                    }

                    List<OrderHenanZw> existOrders = orderHenanZwMapper.selectByExample(
                            new OrderHenanZwExample().createCriteria()
                                    .andOrderNoEqualTo(orderNoWithPrefix)
                                    .example()
                    );
                    if (!CollectionUtils.isEmpty(existOrders)) {
                        boolean duplicated = false;
                        for (OrderHenanZw existOrder : existOrders) {
                            if (existOrder.getStatus() != OrderHenanZwStatusEnum.PENDING.getStatus() && existOrder.getStatus() != OrderHenanZwStatusEnum.SUCCESS.getStatus()) {
                                failedReasons.add("订单号：" + orderNoWithPrefix + ", 失败原因：该订单号已录入并流转中，请勿重复录入");
                                duplicated = true;
                                break;
                            }
                        }
                        if (duplicated) {
                            continue;
                        }
                    }

                    OrderHenanZw orderHenanZw = new OrderHenanZw();
                    BeanUtils.copyProperties(excel, orderHenanZw);
                    orderHenanZw.setSheetNo(generateSheetNo());
                    orderHenanZw.setOrderNo(orderNoWithPrefix);

                    Order2cAtomInfoDTO order2CAtomInfoDTO = order2CAtomInfoService.getOrderDetail(orderNo);
                    if (null == order2CAtomInfoDTO) {
                        failedReasons.add("订单号：" + orderNoWithPrefix + ", 失败原因：订单信息不存在");
                        continue;
                    }
                    SpuOfferingInfoDTO spuOfferingInfoDTO = spuOfferingInfoService.getSpuByCode(order2CAtomInfoDTO.getSpuOfferingCode());
                    if (null == spuOfferingInfoDTO) {
                        failedReasons.add("订单号：" + orderNoWithPrefix + ", 失败原因：订单spu商品信息不存在");
                        continue;
                    }
                    CategoryInfoDTO categoryInfoDTO = categoryInfoService.getCategoryInfo(spuOfferingInfoDTO.getId());
                    if (null == categoryInfoDTO) {
                        failedReasons.add("订单号：" + orderNoWithPrefix + ", 失败原因：订单商品类型信息不存在");
                        continue;
                    }
                    orderHenanZw.setSpuCode(spuOfferingInfoDTO.getOfferingCode());
                    orderHenanZw.setSpuName(spuOfferingInfoDTO.getOfferingName());
                    orderHenanZw.setSpuOfferingClass(categoryInfoDTO.getOfferingClass());
                    orderHenanZw.setSkuCode(order2CAtomInfoDTO.getSkuOfferingCode());
                    orderHenanZw.setSkuName(order2CAtomInfoDTO.getSkuOfferingName());
                    orderHenanZw.setAtomCode(order2CAtomInfoDTO.getAtomOfferingCode());
                    orderHenanZw.setAtomName(order2CAtomInfoDTO.getAtomOfferingName());
                    orderHenanZw.setAtomOfferingClass(order2CAtomInfoDTO.getAtomOfferingClass());
                    orderHenanZw.setModel(order2CAtomInfoDTO.getModel());
                    orderHenanZw.setQuantity(order2CAtomInfoDTO.getSkuQuantity());
                    orderHenanZw.setPrice(order2CAtomInfoDTO.getSkuPrice());
                    orderHenanZw.setStatus(OrderHenanZwStatusEnum.PENDING.getStatus());
                    orderHenanZw.setSendTime(now);
                    orderHenanZw.setCooperatorId(order2CAtomInfoDTO.getCooperatorId());
                    BaseAnswer<Data4User> data4UserBaseAnswer = userFeignClient.userInfo(order2CAtomInfoDTO.getCooperatorId());
                    Data4User data4User = data4UserBaseAnswer.getData();
                    if (data4User != null) {
                        orderHenanZw.setCooperatorName(data4User.getName());
                    }

                    if (OrderHenanZwBusinessTypeEnum.INSTALL_ONLY.getMessage().equals(excel.getBusinessTypeString())) {
                        orderHenanZw.setBusinessType(OrderHenanZwBusinessTypeEnum.INSTALL_ONLY.getStatus());
                    } else if (OrderHenanZwBusinessTypeEnum.INSTALL_MAINTAIN.getMessage().equals(excel.getBusinessTypeString())) {
                        orderHenanZw.setBusinessType(OrderHenanZwBusinessTypeEnum.INSTALL_MAINTAIN.getStatus());
                    }

                    orderHenanZw.setProvinceCode("HA");
                    orderHenanZw.setProvinceName("河南");
                    String cityCode = henanZwCityService.getCityCodeByName(excel.getCityName());
                    if (null == cityCode) {
                        failedReasons.add("订单号：" + orderNoWithPrefix + ", 失败原因：地市信息不正确");
                    }
                    orderHenanZw.setCityCode(cityCode);

                    String areaCode = henanZwAreaService.getAreaCodeByName(excel.getAreaName());
                    if (null == areaCode) {
                        failedReasons.add("订单号：" + orderNoWithPrefix + ", 失败原因：区县信息不正确");
                    }
                    orderHenanZw.setAreaCode(areaCode);

                    String sn = order2cAtomSnService.getSn(orderNo);
                    if (null == sn) {
                        sn = orderStockItemSnService.getSn(orderNoWithPrefix);
                        if (null == sn) {
                            failedReasons.add("订单号：" + orderNoWithPrefix + ", 失败原因：订单设备SN不存在");
                            continue;
                        }
                    }
                    orderHenanZw.setSn(sn);

                    orderHenanZw.setCreateTime(now);
                    orderHenanZw.setUpdateTime(now);

                    // 保存处理信息
                    SaveSheetHistoryParam saveSheetHistoryParam = new SaveSheetHistoryParam();
                    saveSheetHistoryParam.setSheetNo(orderHenanZw.getSheetNo());
                    saveSheetHistoryParam.setOrderNo(orderHenanZw.getOrderNo());
                    saveSheetHistoryParam.setStatus(OrderHenanZwStatusEnum.PENDING.getStatus());

                    data4UserBaseAnswer = userFeignClient.userInfo(loginIfo4Redis.getUserId());
                    data4User = data4UserBaseAnswer.getData();
                    saveSheetHistoryParam.setOperatorId(data4User.getUserId());
                    saveSheetHistoryParam.setOperatorRole(data4User.getRoleName());
                    saveSheetHistoryParam.setOperatorName(data4User.getName());
                    saveSheetHistoryParam.setCreateTime(now);

                    orderHenanZwMapper.insert(orderHenanZw);
                    orderHenanZwHistoryService.saveSheetHistory(saveSheetHistoryParam);
                }
            }
        } catch (IOException e) {
            throw new RuntimeException("文件上传错误");
        }
        return failedReasons;
    }

    /**
     * 列出装维工单
     *
     * @param param
     * @return
     */
    @Override
    public PageData<OrderHenanZwListItemVO> listSheets(QueryOrderHenanZwParam param) {
        OrderHenanZwExample.Criteria criteria = new OrderHenanZwExample().createCriteria();
        if (param.getOrderNo() != null) {
            criteria.andOrderNoEqualTo(param.getOrderNo());
        }
        if (param.getTimeFrom() != null && param.getTimeTo() != null) {
            criteria.andSendTimeBetween(param.getTimeFrom(), param.getTimeTo());
        }
        if (param.getStatus() != null) {
            criteria.andStatusEqualTo(param.getStatus());
        }
        if (null == param.getPage()) {
            param.setPage(1);
        }
        if (null == param.getSize()) {
            param.setSize(10);
        }
        OrderHenanZwExample example = criteria.example();
        example.setPageInfo(param.getPage(), param.getSize());
        List<OrderHenanZw> orderHenanZws = orderHenanZwMapper.selectByExample(example);
        List<OrderHenanZwListItemVO> orderHenanZwListItemVOS = Collections.EMPTY_LIST;
        if (!CollectionUtils.isEmpty(orderHenanZws)) {
            orderHenanZwListItemVOS = orderHenanZws.stream().map(orderHenanZw -> {
                OrderHenanZwListItemVO orderHenanZwListItemVO = new OrderHenanZwListItemVO();
                BeanUtils.copyProperties(orderHenanZw, orderHenanZwListItemVO);
                orderHenanZwListItemVO.setStatusString(OrderHenanZwStatusEnum.getMessage(orderHenanZw.getStatus()));
                return orderHenanZwListItemVO;
            }).collect(Collectors.toList());
        }
        PageData<OrderHenanZwListItemVO> pageData = new PageData<>();
        pageData.setPage(param.getPage());
        pageData.setCount(orderHenanZwListItemVOS.size());
        pageData.setData(orderHenanZwListItemVOS);
        return pageData;
    }

    /**
     * 同步售后订单至省侧 OS => 能开平台 => 河南业编
     *
     * @param param
     */
    @Override
    public void syncToHenan(HenanZwSendOrderBusiParam param) {
        Map body = new HashMap();
        body.put("busi", JSON.parseObject(JSON.toJSONString(param), HashMap.class));
        Map header = new HashMap();
        header.put("accessToken", getOrderHenanZwSystemToken());
//        header.put("accessToken", "test");
        header.put("Content-Type", "application/json");
        header.put("Accept-Charset", "UTF-8");
        body.put("header", header);
        Map sysParam = buildHenanZwSendOrderSysParam(JSON.toJSONString(param));
        log.info("获取业编token请求sysParam：{}", Joiner.on("&").useForNull("").withKeyValueSeparator("=").join(sysParam));
        log.info("同步售后订单至省侧请求busiParam：{}", JSON.toJSONString(body));
        String respString = henanOpenFeignClient.sendZwOrder(sysParam, body);
        log.info("同步售后订单至省侧响应：" + respString);
        HenanOpenResponse henanOpenResponse = JSON.parseObject(respString, HenanOpenResponse.class);
        if ("00000".equals(henanOpenResponse.getRespCode())) {
            BaseZwResponse response = JSON.parseObject(henanOpenResponse.getResult(), BaseZwResponse.class);
            if (!"OK".equals(response.getState())) {
                throw new BusinessException(response.getErrorCode(), response.getErrorMessage());
            }
            // 添加历史记录
            AftermarketOrderHistory history = new AftermarketOrderHistory();
            history.setId(BaseServiceUtils.getId());
            history.setServiceOrderId(param.getOrderNo().substring(ORDER_PREFIX.length()));
            history.setOperateType(AfterMarketOrderResultConstant.AFTER_MARKET_ORDER_HISTORY_OPERATE_TYPE_);
            history.setInnerStatus(AfterMarketOrderStatusEnum.ORDER_SYNC_PROVINCE.getStatus());
            String operateMessage = "同步到省侧成功";
            history.setOperateMessage(operateMessage);
            Date now = new Date();
            history.setCreateTime(now);
            history.setUpdateTime(now);
            aftermarketOrderHistoryMapper.insertSelective(history);
        } else {
            throw new BusinessException(henanOpenResponse.getRespCode(), henanOpenResponse.getRespDesc());
        }
    }
    @Override
    public void syncToHenanCommon(HenanZwSendOrderBusiNewParam param) {
        Map body = new HashMap();
        body.put("busi", JSON.parseObject(JSON.toJSONString(param), HashMap.class));
        Map header = new HashMap();
        header.put("accessToken", getOrderHenanZwSystemTokenCommon());
//        header.put("accessToken", "cs");
        // header.put("accessToken", "test");
        header.put("Content-Type", "application/json");
        header.put("Accept-Charset", "UTF-8");
        body.put("header", header);
        Map sysParam = buildHenanZwCommonSendOrderSysParam(JSON.toJSONString(param));
        log.info("烟感气感获取业编token请求sysParam：{}", Joiner.on("&").useForNull("").withKeyValueSeparator("=").join(sysParam));
        log.info("烟感气感同步售后订单至省侧请求busiParam：{}", JSON.toJSONString(body));
        String respString = henanOpenFeignClient.sendZwOrder(sysParam, body);
        log.info("烟感气感同步售后订单至省侧响应：" + respString);
        HenanOpenResponse henanOpenResponse = JSON.parseObject(respString, HenanOpenResponse.class);
        if ("00000".equals(henanOpenResponse.getRespCode())) {
            log.info("烟感气感同步售后订单至省侧成功,订单id:{}", param.getOrderNo());
        } else {
            log.error("烟感气感同步售后订单至省侧失败,订单id:{}", param.getOrderNo());

        }
    }

    /**
     * 通用省侧同步方法，支持自定义字段映射（新版本）
     *
     * @param param 通用省侧同步参数
     */
    @Override
    public void syncToCommonProvince(CommonProvinceSyncParam param) {

        try {
            String respString;
            // 根据开通平台来调用不同Feign
            // 从订单号中提取服务订单ID
            String serviceOrderId = param.getOrderNo();
            if (serviceOrderId.startsWith(ORDER_PREFIX)) {
                serviceOrderId = serviceOrderId.substring(ORDER_PREFIX.length());
            }

            // 查询售后订单商品信息获取省份平台
            String provincePlatform = getProvinceInstallPlatform(serviceOrderId);
            if (StringUtils.isEmpty(provincePlatform)) {
                // 如果数据库中没有配置省份平台，默认使用山东
                provincePlatform = ProvinceInstallPlatformEnum.SHANDONG.getCode();
                log.warn("订单{}的省份平台代码为空，默认使用山东平台", serviceOrderId);
            }

            if (ProvinceInstallPlatformEnum.SHANDONG.getCode().equals(provincePlatform)) {
                // 调用山东省开通平台
                respString = shandongOpenFeignClient.sendZwOrder(param);
                log.info("调用山东省开通平台，响应：{}", respString);
            } else if (ProvinceInstallPlatformEnum.GUANGDONG.getCode().equals(provincePlatform)) {
                // 调用广东省开通平台
                respString = guangdongOpenFeignClient.sendZwOrder(param);
                log.info("调用广东省开通平台，响应：{}", respString);
            }  else {
                throw new BusinessException("UNSUPPORTED_PROVINCE_PLATFORM",
                    "不支持的省份平台：" + provincePlatform + "，目前支持：" +
                    ProvinceInstallPlatformEnum.SHANDONG.getCode() + "、" +
                    ProvinceInstallPlatformEnum.GUANGDONG.getCode() + "、" +
                    ProvinceInstallPlatformEnum.HENAN.getCode());
            }
            ProgressSyncResponseBody  henanOpenResponse = JSON.parseObject(respString, ProgressSyncResponseBody.class);
            log.info("通用省侧同步方法同步售后订单至省侧响应：{}", respString);
            if ("00000".equals(henanOpenResponse.getStateCode())) {
                // 添加历史记录
                AftermarketOrderHistory history = new AftermarketOrderHistory();
                history.setId(BaseServiceUtils.getId());
                history.setServiceOrderId(param.getOrderNo().substring(ORDER_PREFIX.length()));
                history.setOperateType(AfterMarketOrderResultConstant.AFTER_MARKET_ORDER_HISTORY_OPERATE_TYPE_);
                history.setInnerStatus(AfterMarketOrderStatusEnum.ORDER_SYNC_PROVINCE.getStatus());
                String operateMessage = "同步到" + ProvinceInstallPlatformEnum.getName(provincePlatform) + "成功";
                history.setOperateMessage(operateMessage);
                Date now = new Date();
                history.setCreateTime(now);
                history.setUpdateTime(now);
                aftermarketOrderHistoryMapper.insertSelective(history);
            } else {
                throw new BusinessException(henanOpenResponse.getStateCode(), henanOpenResponse.getMessage());
            }
        } catch (Exception e) {
            log.error("通用省侧同步方法同步售后订单至省侧异常：{}", e.getMessage());
            throw new BusinessException(StatusConstant.SYNC_HENAN_ZW_FAILED, e.getMessage());
        }
    }


    /**
     * 派发工单 OS => 能开平台 => 河南业编
     *
     * @param sheetNo 工单编号
     */
    @Override
    public void sendSheet(String sheetNo) {
        Date now = new Date();
        HenanZwSendOrderBusiParam busiParam = buildSendOrderBusiParam(sheetNo, now);
        syncToHenan(busiParam);
    }

    /**
     * 批量派发工单 OS => 能开平台 => 河南业编
     *
     * @param sheetNos 工单编号数组
     * @return
     */
    @Override
    public List<String> batchSendSheets(List<String> sheetNos) {
        Date now = new Date();
        List<String> errorMessages = new ArrayList<>();
        if (CollectionUtils.isEmpty(sheetNos)) {
            throw new BusinessException(B2BConstant.HANAN_ZW_ORDER_NO_EMPTY);
        }
        for (String sheetNo : sheetNos) {
            HenanZwSendOrderBusiParam busiParam = buildSendOrderBusiParam(sheetNo, now);
            Map body = new HashMap();
            body.put("busi", JSON.toJSONString(busiParam));
            Map header = new HashMap();
            header.put("accessToken", getOrderHenanZwSystemToken());
            header.put("Content-Type", "application/json");
            header.put("Accept-Charset", "UTF-8");
            body.put("header", JSON.toJSONString(header));
            String respString = henanOpenFeignClient.sendZwOrder(buildHenanZwSendOrderSysParam(JSON.toJSONString(busiParam)), body);
            HenanOpenResponse henanOpenResponse = JSON.parseObject(respString, HenanOpenResponse.class);
            if ("00000".equals(henanOpenResponse.getRespCode())) {
                BaseZwResponse response = JSON.parseObject(henanOpenResponse.getResult(), BaseZwResponse.class);
                if (!"OK".equals(response.getState())) {
                    errorMessages.add("订单号：" + busiParam.getOrderNo() + "，失败原因：" + response.getErrorMessage());
                }
            } else {
                errorMessages.add("订单号：" + busiParam.getOrderNo() + "，失败原因：" + henanOpenResponse.getRespDesc());
            }
        }
        return errorMessages;
    }

    /**
     * 查询工单详情
     *
     * @param sheetNo
     * @return
     */
    @Override
    public OrderHenanZwDetailVO getSheetDetail(String sheetNo) {
        OrderHenanZwDetailVO orderHenanZwDetailVO = new OrderHenanZwDetailVO();
        OrderHenanZw orderHenanZw = orderHenanZwMapper.selectByPrimaryKey(sheetNo);
        BeanUtils.copyProperties(orderHenanZw, orderHenanZwDetailVO);
        orderHenanZwDetailVO.setStatusString(OrderHenanZwStatusEnum.getMessage(orderHenanZw.getStatus()));
        List<OrderHenanZwHistoryVO> orderHenanZwHistoryVOS = orderHenanZwHistoryService.getSheetHistory(sheetNo);
        orderHenanZwDetailVO.setHistories(orderHenanZwHistoryVOS);
        return orderHenanZwDetailVO;
    }

    /**
     * 开通工单回复 河南业编 => 能开平台 => OS
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseZwResponse openResp(SyncCommonRequest syncCommonRequest) {
//        SyncCommonRequest syncCommonRequest = getSyncCommonRequest(request);
        log.info("开通工单回复, data:{}", JSON.toJSONString(syncCommonRequest));
        String input = syncCommonRequest.getInput();
        String sign = syncCommonRequest.getSign();
        // sign验签
        try {
            SignUtils.checkSign(input, sign);
        } catch (ServicePowerException e) {
            return BaseZwResponse.error(BaseZwResponse.ErrorCode.VALIDATE_EXCEPTION, e.getMessage());
        }
        if (!HENAN_ZW_SUBSCRIBER.equals(syncCommonRequest.getSubscriber())) {
            return BaseZwResponse.error(BaseZwResponse.ErrorCode.VALIDATE_EXCEPTION, "subscriber字段错误");
        }

        OpenRespRequest openRespRequest = JSON.parseObject(input, OpenRespRequest.class);
        OrderHenanZw orderHenanZw = orderHenanZwMapper.selectByPrimaryKey(openRespRequest.getOrderNo());
        if (orderHenanZw != null) {
            Date now = new Date();
            orderHenanZw.setUpdateTime(now);

            SaveSheetHistoryParam saveSheetHistoryParam = new SaveSheetHistoryParam();
            saveSheetHistoryParam.setSheetNo(orderHenanZw.getSheetNo());
            saveSheetHistoryParam.setOrderNo(openRespRequest.getOrderNo());
            if ("1".equals(openRespRequest.getDealResult())) {
                // 开通成功
                saveSheetHistoryParam.setStatus(OrderHenanZwStatusEnum.SUCCESS.getStatus());
                orderHenanZw.setStatus(OrderHenanZwStatusEnum.SUCCESS.getStatus());
            } else if ("2".equals(openRespRequest.getDealResult())) {
                saveSheetHistoryParam.setStatus(OrderHenanZwStatusEnum.FAILED.getStatus());
                saveSheetHistoryParam.setRejectReason(openRespRequest.getDealDesc());
                orderHenanZw.setStatus(OrderHenanZwStatusEnum.FAILED.getStatus());
            }
            saveSheetHistoryParam.setCreateTime(now);

            orderHenanZwMapper.updateByPrimaryKey(orderHenanZw);
            orderHenanZwHistoryService.saveSheetHistory(saveSheetHistoryParam);


        } else {
            //查看售后订单是否存在相关订单
            String serviceOrderNo = openRespRequest.getOrderNo().substring(ORDER_PREFIX.length());
            AfterMarketOrder2cInfo afterMarketOrder2cInfo = afterMarketOrder2cInfoMapper.selectByPrimaryKey(serviceOrderNo);
            if (afterMarketOrder2cInfo != null) {
                // 添加历史记录
                AftermarketOrderHistory history = new AftermarketOrderHistory();
                history.setId(BaseServiceUtils.getId());
                history.setServiceOrderId(afterMarketOrder2cInfo.getServiceOrderId());
                history.setOperateType(AfterMarketOrderResultConstant.AFTER_MARKET_ORDER_HISTORY_OPERATE_TYPE_);
                String operateMessage = "完成交付。交付结果 ";

                // 同步到商城
                ServiceOrderResultRequest resultRequest = new ServiceOrderResultRequest();
                resultRequest.setOrderId(afterMarketOrder2cInfo.getServiceOrderId());
                resultRequest.setOprType("2");
                ServiceOrderResultRequest.DeliverInfo deliverInfo = new ServiceOrderResultRequest.DeliverInfo();

                //     * 4.已完结（成功）
                //     * 5.已完成（失败）
                if ("1".equals(openRespRequest.getDealResult())) {
                    // 开通成功
                    afterMarketOrder2cInfo.setStatus(AfterMarketOrderStatusEnum.DELIVERY_SUCCESS.getStatus());
                    history.setInnerStatus(AfterMarketOrderStatusEnum.DELIVERY_SUCCESS.getStatus());
                    operateMessage = operateMessage.concat("成功");
                    deliverInfo.setDeliverResult("1");
                } else if ("2".equals(openRespRequest.getDealResult())) {
                    afterMarketOrder2cInfo.setStatus(AfterMarketOrderStatusEnum.DELIVERY_FAIL.getStatus());
                    history.setInnerStatus(AfterMarketOrderStatusEnum.DELIVERY_FAIL.getStatus());
                    operateMessage = operateMessage.concat("失败");
                    deliverInfo.setDeliverResult("2");
                    deliverInfo.setReason(openRespRequest.getDealDesc());
                }
                Date now = new Date();
                afterMarketOrder2cInfo.setUpdateTime(new Date());
                afterMarketOrder2cInfoMapper.updateByPrimaryKeySelective(afterMarketOrder2cInfo);

                history.setOperateMessage(operateMessage);
                history.setCreateTime(now);
                history.setUpdateTime(now);
                aftermarketOrderHistoryMapper.insertSelective(history);

                resultRequest.setDeliverInfo(deliverInfo);
                sendServiceOrderResult(resultRequest);
            } else {
                return BaseZwResponse.error(BaseZwResponse.ErrorCode.INTERNAL_SERVER_EXCEPTION, "工单不存在，工单号：" + openRespRequest.getSerialNo());
            }
        }
        return BaseZwResponse.success();
    }

    /**
     * 开通工单驳回 河南业编 => 能开平台 => OS
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseZwResponse openRejct(SyncCommonRequest syncCommonRequest) {
//        SyncCommonRequest syncCommonRequest = getSyncCommonRequest(request);
        log.info("开通工单驳回, data:{}", JSON.toJSONString(syncCommonRequest));
        String input = syncCommonRequest.getInput();
        String sign = syncCommonRequest.getSign();
        // sign验签
        try {
            SignUtils.checkSign(input, sign);
        } catch (ServicePowerException e) {
            return BaseZwResponse.error(BaseZwResponse.ErrorCode.VALIDATE_EXCEPTION, e.getMessage());
        }
        if (!HENAN_ZW_SUBSCRIBER.equals(syncCommonRequest.getSubscriber())) {
            return BaseZwResponse.error(BaseZwResponse.ErrorCode.VALIDATE_EXCEPTION, "subscriber字段错误");
        }

        OpenRejectRequest openRejectRequest = JSON.parseObject(input, OpenRejectRequest.class);
        OrderHenanZw orderHenanZw = orderHenanZwMapper.selectByPrimaryKey(openRejectRequest.getSerialNo());
        if (orderHenanZw != null) {
            Date now = new Date();
            orderHenanZw.setStatus(OrderHenanZwStatusEnum.FAILED.getStatus());
            orderHenanZw.setUpdateTime(now);

            SaveSheetHistoryParam saveSheetHistoryParam = new SaveSheetHistoryParam();
            saveSheetHistoryParam.setSheetNo(orderHenanZw.getSheetNo());
            saveSheetHistoryParam.setOrderNo(openRejectRequest.getOrderNo());
            saveSheetHistoryParam.setStatus(OrderHenanZwStatusEnum.FAILED.getStatus());
            saveSheetHistoryParam.setRejectReason(openRejectRequest.getOpDesc());
            saveSheetHistoryParam.setCreateTime(now);

            orderHenanZwMapper.updateByPrimaryKey(orderHenanZw);
            orderHenanZwHistoryService.saveSheetHistory(saveSheetHistoryParam);


        } else {
            //查看售后订单是否存在相关订单
            String serviceOrderNo = openRejectRequest.getOrderNo().substring(ORDER_PREFIX.length());
            AfterMarketOrder2cInfo afterMarketOrder2cInfo = afterMarketOrder2cInfoMapper.selectByPrimaryKey(serviceOrderNo);
            if (afterMarketOrder2cInfo != null) {
                //     * 5.已完成（失败）
                Date now = new Date();
                afterMarketOrder2cInfo.setStatus(AfterMarketOrderStatusEnum.DELIVERY_FAIL.getStatus());
                afterMarketOrder2cInfo.setUpdateTime(now);
                afterMarketOrder2cInfoMapper.updateByPrimaryKeySelective(afterMarketOrder2cInfo);

                // 添加历史记录
                AftermarketOrderHistory history = new AftermarketOrderHistory();
                history.setId(BaseServiceUtils.getId());
                history.setServiceOrderId(afterMarketOrder2cInfo.getServiceOrderId());
                history.setOperateType(AfterMarketOrderResultConstant.AFTER_MARKET_ORDER_HISTORY_OPERATE_TYPE_);
                String operateMessage = "完成交付。交付结果 ";
                history.setInnerStatus(AfterMarketOrderStatusEnum.DELIVERY_FAIL.getStatus());
                operateMessage = operateMessage.concat("失败");
                history.setOperateMessage(operateMessage);
                history.setCreateTime(now);
                history.setUpdateTime(now);
                aftermarketOrderHistoryMapper.insertSelective(history);

                // 同步到商城
                ServiceOrderResultRequest resultRequest = new ServiceOrderResultRequest();
                resultRequest.setOrderId(afterMarketOrder2cInfo.getServiceOrderId());
                resultRequest.setOprType("2");
                ServiceOrderResultRequest.DeliverInfo deliverInfo = new ServiceOrderResultRequest.DeliverInfo();
                deliverInfo.setDeliverResult("2");
                deliverInfo.setReason(openRejectRequest.getOpDesc());
                resultRequest.setDeliverInfo(deliverInfo);
                sendServiceOrderResult(resultRequest);
            } else {
                return BaseZwResponse.error(BaseZwResponse.ErrorCode.INTERNAL_SERVER_EXCEPTION, "工单不存在，工单号：" + openRejectRequest.getSerialNo());
            }
        }
        return BaseZwResponse.success();
    }

    /**
     * 开通工单状态 河南业编 => 能开平台 => OS
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseZwResponse openAccept(SyncCommonRequest syncCommonRequest) {
//        SyncCommonRequest syncCommonRequest = getSyncCommonRequest(request);
        log.info("透明化接口, data:{}", JSON.toJSONString(syncCommonRequest));
        String input = syncCommonRequest.getInput();
        String sign = syncCommonRequest.getSign();
        // sign验签
        try {
            SignUtils.checkSign(input, sign);
        } catch (ServicePowerException e) {
            return BaseZwResponse.error(BaseZwResponse.ErrorCode.VALIDATE_EXCEPTION, e.getMessage());
        }
        if (!HENAN_ZW_SUBSCRIBER.equals(syncCommonRequest.getSubscriber())) {
            return BaseZwResponse.error(BaseZwResponse.ErrorCode.VALIDATE_EXCEPTION, "subscriber字段错误");
        }

        OpenAcceptRequest openAcceptRequest = JSON.parseObject(input, OpenAcceptRequest.class);
        OrderHenanZw orderHenanZw = orderHenanZwMapper.selectByPrimaryKey(openAcceptRequest.getSerialNo());
        if (orderHenanZw != null) {
            String sheetNo = orderHenanZw.getSheetNo();
            henanZwSheetActionService.saveSheetFlow(openAcceptRequest, sheetNo);


            if (HenanZwSheetActionEnum.ACCEPT.getAction().equals(openAcceptRequest.getDealAction())) {
                orderHenanZw.setStatus(OrderHenanZwStatusEnum.ACCEPT.getStatus());
                orderHenanZw.setUpdateTime(openAcceptRequest.getOperTime());

                SaveSheetHistoryParam saveSheetHistoryParam = new SaveSheetHistoryParam();
                saveSheetHistoryParam.setSheetNo(orderHenanZw.getSheetNo());
                saveSheetHistoryParam.setOrderNo(openAcceptRequest.getOrderNo());
                saveSheetHistoryParam.setStatus(OrderHenanZwStatusEnum.ACCEPT.getStatus());
                saveSheetHistoryParam.setInstallerName(openAcceptRequest.getOpPerson());
                saveSheetHistoryParam.setInstallerPhone(openAcceptRequest.getOpContact());
                saveSheetHistoryParam.setCreateTime(openAcceptRequest.getOperTime());

                orderHenanZwMapper.updateByPrimaryKey(orderHenanZw);
                orderHenanZwHistoryService.saveSheetHistory(saveSheetHistoryParam);


            }

        } else {
            //查看售后订单是否存在相关订单
            String serviceOrderNo = openAcceptRequest.getOrderNo().substring(ORDER_PREFIX.length());
            AfterMarketOrder2cInfo afterMarketOrder2cInfo = afterMarketOrder2cInfoMapper.selectByPrimaryKey(serviceOrderNo);
            if (afterMarketOrder2cInfo != null) {
                if (HenanZwSheetActionEnum.ACCEPT.getAction().equals(openAcceptRequest.getDealAction())) {
                    afterMarketOrderDispatch(afterMarketOrder2cInfo, openAcceptRequest);
                }
            } else {
                return BaseZwResponse.error(BaseZwResponse.ErrorCode.INTERNAL_SERVER_EXCEPTION, "工单不存在，工单号：" + openAcceptRequest.getSerialNo());
            }
        }
        return BaseZwResponse.success();
    }

    private SyncCommonRequest getSyncCommonRequest(HttpServletRequest request) {
        String contentType = request.getContentType();
        SyncCommonRequest syncCommonRequest = null;
        if (contentType.contains("application/json")) {
            try {
                BufferedReader br = request.getReader();
                String str, wholeStr = "";
                while((str = br.readLine()) != null){
                    wholeStr += str;
                }
                syncCommonRequest = JSON.parseObject(wholeStr, SyncCommonRequest.class);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        } else if (contentType.contains("application/x-www-form-urlencoded")) {
            Map<String, String[]> map = request.getParameterMap();
            Map paramMap = new HashMap();
            Iterator<String> it = map.keySet().iterator();
            while (it.hasNext()) {
                String key = it.next();
                paramMap.put(key, map.get(key)[0]);
            }
            String json = JSON.toJSONString(paramMap);
            syncCommonRequest = JSON.parseObject(json, SyncCommonRequest.class);
        }
        return syncCommonRequest;
    }

    private void afterMarketOrderDispatch(AfterMarketOrder2cInfo afterMarketOrder2cInfo, OpenAcceptRequest openAcceptRequest) {
        String serviceOrderId = afterMarketOrder2cInfo.getServiceOrderId();
        //省测接单传递默认确认编码
        String code = "000000";
        log.info("省测接单开始派单订单号：{}", serviceOrderId, code);
        //查询当前订单信息
        Integer status = afterMarketOrder2cInfo.getStatus();
        if (status.equals(AfterMarketOrderStatusEnum.DISPATCHING.getStatus()) || status.equals(AfterMarketOrderStatusEnum.DISPATCHED.getStatus())) {

            // 派遣成功修改订单状态及同步状态到商城
            Date date = new Date();
            afterMarketOrder2cInfo.setStatus(AfterMarketOrderStatusEnum.DISPATCHED.getStatus());
            afterMarketOrder2cInfo.setUpdateTime(date);
            afterMarketOrder2cInfoMapper.updateByPrimaryKeySelective(afterMarketOrder2cInfo);
            //更新订单商品关系表信息

            AfterMarketOrder2cOfferingInfo afterMarketOrder2cOfferingInfo = new AfterMarketOrder2cOfferingInfo();
            afterMarketOrder2cOfferingInfo.setPresentSendOrderName(openAcceptRequest.getOpPerson());
            afterMarketOrder2cOfferingInfo.setPresentSendOrderPhone(openAcceptRequest.getOpContact());
            afterMarketOrder2cOfferingInfo.setSendOrderTime(date);
            afterMarketOrder2cOfferingInfo.setUpdateTime(date);
            AfterMarketOrder2cOfferingInfoExample offeringInfoExample = new AfterMarketOrder2cOfferingInfoExample();
            AfterMarketOrder2cOfferingInfoExample.Criteria offeringCriteria = offeringInfoExample.createCriteria();
            offeringCriteria.andServiceOrderIdEqualTo(serviceOrderId);
            afterMarketOrder2cOfferingInfoMapper.updateByExampleSelective(afterMarketOrder2cOfferingInfo,offeringInfoExample);
            //修改状态记录历史
            AftermarketOrderHistory aftermarketOrderHistory = new AftermarketOrderHistory();
            aftermarketOrderHistory.setId(BaseServiceUtils.getId());
            aftermarketOrderHistory.setServiceOrderId(serviceOrderId);
            aftermarketOrderHistory.setOperateType(AfterMarketOrderResultConstant.AFTER_MARKET_ORDER_HISTORY_OPERATE_TYPE_);
            aftermarketOrderHistory.setInnerStatus(AfterMarketOrderStatusEnum.DISPATCHED.getStatus());
            aftermarketOrderHistory.setCreateTime(date);
            aftermarketOrderHistory.setUpdateTime(date);
            if (status.equals(AfterMarketOrderStatusEnum.DISPATCHING.getStatus())) {
                aftermarketOrderHistory.setOperateMessage("完成派单。接单人员姓名".
                        concat(openAcceptRequest.getOpPerson()).concat(",").concat("接单人员电话")
                        .concat(openAcceptRequest.getOpContact()).concat("。"));
            } else if (status.equals(AfterMarketOrderStatusEnum.DISPATCHED.getStatus())) {
                aftermarketOrderHistory.setOperateMessage("修改派单。接单人员姓名".
                        concat(openAcceptRequest.getOpPerson()).concat(",").concat("接单人员电话")
                        .concat(openAcceptRequest.getOpContact()).concat("。"));
            }
            aftermarketOrderHistoryMapper.insertSelective(aftermarketOrderHistory);
            //同步状态到商城
            ServiceOrderResultRequest serviceOrderResultRequest = new ServiceOrderResultRequest();
            serviceOrderResultRequest.setOrderId(serviceOrderId);
            serviceOrderResultRequest.setOprType("1");
            ServiceOrderResultRequest.DispatchInfo dispatchInfo = new ServiceOrderResultRequest.DispatchInfo();
            dispatchInfo.setName(openAcceptRequest.getOpPerson());
            dispatchInfo.setPhone(openAcceptRequest.getOpContact());
            dispatchInfo.setNumber(code);
            serviceOrderResultRequest.setDispatchInfo(dispatchInfo);
            sendServiceOrderResult(serviceOrderResultRequest);
        }
    }

    /**
     * 生成装维工单号
     * 规则：SH + yyyyMMdd + 4位数字自增序号
     *
     * @return
     */
    private String generateSheetNo() {
        long count = orderHenanZwMapper.countByExample(new OrderHenanZwExample());
        String orderSerial = getOrderSerial(count);
        String dateString = SDF.format(new Date());
        return "SH" + dateString + orderSerial;
    }

    /**
     * 生成装维工单4位数字自增序号
     *
     * @param count
     * @return
     */
    private String getOrderSerial(long count) {
        String orderSerial = String.valueOf(count);
        int length = orderSerial.length();
        for (int i = 0; i < 4 - length; i++) {
            orderSerial = "0".concat(orderSerial);
        }
        return orderSerial;
    }

    /**
     * 构造开通工单接口业务参数
     *
     * @param sheetNo
     * @param date
     * @return
     */
    private HenanZwSendOrderBusiParam buildSendOrderBusiParam(String sheetNo, Date date) {
        OrderHenanZw orderHenanZw = orderHenanZwMapper.selectByPrimaryKey(sheetNo);
        HenanZwSendOrderBusiParam param = new HenanZwSendOrderBusiParam();
        param.setOrderNo(orderHenanZw.getOrderNo());
        param.setOrderType("2");
        param.setOrgId("13");
        param.setProductName("qly");
        param.setBusinessType(orderHenanZw.getBusinessType().toString());
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.roll(Calendar.DAY_OF_YEAR, 2);
        param.setRequiredTime(calendar.getTime());
        // 获取河南省的加密密钥
        String henanEncryptKey = provinceEncryptConfig.getEncryptKey("henan");

        param.setCustomContact(IOTEncodeUtils.encryptIOTMessage(orderHenanZw.getCustomContact(), henanEncryptKey));
        param.setCustomContactPhone(IOTEncodeUtils.encryptIOTMessage(orderHenanZw.getCustomContactPhone(), henanEncryptKey));
        param.setProvinceA(IOTEncodeUtils.encryptIOTMessage("HA", henanEncryptKey));
        param.setCityA(IOTEncodeUtils.encryptIOTMessage(orderHenanZw.getCityCode(), henanEncryptKey));
        param.setAreaA(IOTEncodeUtils.encryptIOTMessage(orderHenanZw.getAreaCode(), henanEncryptKey));
        param.setInstallAddr(IOTEncodeUtils.encryptIOTMessage(orderHenanZw.getAddress(), henanEncryptKey));

        List<OrderHenanZwFeignDeviceInfo> deviceInfos = new ArrayList<>();
        OrderHenanZwFeignDeviceInfo deviceInfo = new OrderHenanZwFeignDeviceInfo();
        deviceInfo.setModel(orderHenanZw.getModel());
        deviceInfo.setSerialNumber(orderHenanZw.getSn());
        deviceInfos.add(deviceInfo);
        param.setDeviceInfo(deviceInfos);

        return param;
    }

    /**
     * 构造河南能力开放平台能力调用接口系统参数
     *
     * @param method      能力编码
     * @param appId       应用编码
     * @param accessToken oauth授权令牌
     * @param opCode      操作员工号
     * @param busiParam   业务参数
     * @return
     */
    private Map<String, String> buildHenanOpenSysParam(String method, String appId, String accessToken, String opCode, String busiParam) {
        Map<String, String> sysParam = new HashMap<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        sysParam.put("method", method);
        sysParam.put("format", "json");
        sysParam.put("timestamp", sdf.format(new Date()));
        sysParam.put("appId", appId);
        sysParam.put("version", "1.0");
        sysParam.put("accessToken", accessToken);
        sysParam.put("busiSerial", "1");
        sysParam.put("OPCODE", opCode);

        String key = "9c5209987c9112c248e1ef1e5f3a5769";

        try {
            String entity = SecurityUtils.encodeAES256HexUpper(busiParam, SecurityUtils.decodeHexUpper(key));
            String sign = SignUtil.sign(sysParam, entity, "HmacSHA256", key);
            sysParam.put("sign", sign);
        } catch (Exception e) {
            throw new RuntimeException("签名错误");
        }
        return sysParam;
    }

    /**
     * 构造业编系统认证能力系统参数
     *
     * @param busiParam 业务参数
     * @return
     */
    private Map<String, String> buildAuthenticationSysParam(String busiParam) {
        Map<String, String> sysParam = buildHenanOpenSysParam(
                methodCodeAuthentication,
                "1120654",
                "default",
                "YzzIOTSM",
                busiParam);
        return sysParam;
    }
    /**
     * 构造业编系统认证能力系统参数河南烟气感
     *
     * @param busiParam 业务参数
     * @return
     */
    private Map<String, String> buildAuthenticationCommonSysParam(String busiParam) {
        Map<String, String> sysParam = buildHenanOpenSysParam(
                commonAuthentication,
                "1120654",
                "default",
                "YzzIOTSM",
                busiParam);
        return sysParam;
    }

    /**
     * 构造业编系统派发业务开通能力系统参数
     *
     * @param busiParam 业务参数
     * @return
     */
    private Map<String, String> buildHenanZwSendOrderSysParam(String busiParam) {
        Map<String, String> sysParam = buildHenanOpenSysParam(
                methodCodeOpenCreate,
                "1120654",
                "default",
                "YzzIOTSM",
                busiParam);
        return sysParam;
    }
    /**
     * 构造业编系统烟感气感派发业务开通能力系统参数
     *
     * @param busiParam 业务参数
     * @return
     */
    private Map<String, String> buildHenanZwCommonSendOrderSysParam(String busiParam) {
        Map<String, String> sysParam = buildHenanOpenSysParam(
                commonOpenCreate,
                "1120654",
                "default",
                "YzzIOTSM",
                busiParam);
        return sysParam;
    }
    @Override
    public String getOrderHenanZwSystemToken() {
        String token = (String) redisTemplate.opsForValue().get(Constant.REDIS_HENAN_ZW_SYS_TOKEN);
        if (null == token) {
            HenanZwAuthenticationBody body = new HenanZwAuthenticationBody();
            body.setGrantType("password");
            body.setUserName("SD-WAN");
            body.setValue("SDWAN123");
            Map sysParam = buildAuthenticationSysParam(JSON.toJSONString(body));
            log.info("获取业编token请求sysParam：{}", Joiner.on("&").useForNull("").withKeyValueSeparator("=").join(sysParam));
            log.info("获取业编token请求busiParam：{}", JSON.toJSONString(body));
            String respString = henanOpenFeignClient.authenticationZw(sysParam, body);
            log.info("获取业编token响应：" + respString);
            HenanOpenResponse henanOpenResponse = JSON.parseObject(respString, HenanOpenResponse.class);
            if ("00000".equals(henanOpenResponse.getRespCode())) {
                BaseZwResponse<AuthenticationResponse> response = JSON.parseObject(henanOpenResponse.getResult(), new TypeReference<BaseZwResponse<AuthenticationResponse>>() {});
                if ("OK".equals(response.getState())) {
                    AuthenticationResponse authenticationResponse = response.getBody();
                    if (null == authenticationResponse || TextUtils.isEmpty(authenticationResponse.getAccessToken())) {
                        throw new BusinessException("-1", "获取业编token响应异常：" + henanOpenResponse.getResult());
                    }
                    token = authenticationResponse.getAccessToken();
                    Duration duration = Duration.ofMinutes(authenticationResponse.getExpires());
                    redisTemplate.opsForValue().setIfAbsent(Constant.REDIS_HENAN_ZW_SYS_TOKEN, token, duration);
                } else {
                    throw new BusinessException(response.getErrorCode(), response.getErrorMessage());
                }
            } else {
                throw new BusinessException(henanOpenResponse.getRespCode(), henanOpenResponse.getRespDesc());
            }
        }
        return token;
    }
    @Override
    public String getOrderHenanZwSystemTokenCommon(){
        String token = (String) redisTemplate.opsForValue().get(Constant.REDIS_KEY_HENAN_ZW_SYS_TOKEN_COMMON);
        if (null == token) {
            HenanZwAuthenticationBody body = new HenanZwAuthenticationBody();
            body.setGrantType("password");
            body.setUserName("SD-WAN");
            body.setValue("SDWAN123");
            Map sysParam = buildAuthenticationCommonSysParam(JSON.toJSONString(body));
            log.info("获取业编token请求sysParam：{}", Joiner.on("&").useForNull("").withKeyValueSeparator("=").join(sysParam));
            log.info("获取业编token请求busiParam：{}", JSON.toJSONString(body));
            String respString = henanOpenFeignClient.authenticationZw(sysParam, body);
            log.info("获取业编token响应：" + respString);
            HenanOpenResponse henanOpenResponse = JSON.parseObject(respString, HenanOpenResponse.class);
            if ("00000".equals(henanOpenResponse.getRespCode())) {
                BaseZwResponse<AuthenticationResponse> response = JSON.parseObject(henanOpenResponse.getResult(), new TypeReference<BaseZwResponse<AuthenticationResponse>>() {});
                if ("OK".equals(response.getState())) {
                    AuthenticationResponse authenticationResponse = response.getBody();
                    if (null == authenticationResponse || TextUtils.isEmpty(authenticationResponse.getAccessToken())) {
                        throw new BusinessException("-1", "获取业编token响应异常：" + henanOpenResponse.getResult());
                    }
                    token = authenticationResponse.getAccessToken();
                    Duration duration = Duration.ofMinutes(authenticationResponse.getExpires());
                    redisTemplate.opsForValue().setIfAbsent(Constant.REDIS_KEY_HENAN_ZW_SYS_TOKEN_COMMON, token, duration);
                } else {
                    throw new BusinessException(response.getErrorCode(), response.getErrorMessage());
                }
            } else {
                throw new BusinessException(henanOpenResponse.getRespCode(), henanOpenResponse.getRespDesc());
            }
        }
        return token;
    };

    private boolean isAllFieldsNull(Object o) {
        try {
            Field[] fields = o.getClass().getDeclaredFields();
            for (Field field : fields) {
                field.setAccessible(true);
                Object value = field.get(o);
                if (value != null) {
                    return false;
                }
            }
            return true;
        } catch (IllegalAccessException e) {
            return true;
        }
    }

    /**
     * 售后服务结果反馈，用于OS系统向IoT应用商城返回售后服务派单、交付的结果
     *
     * @param request
     */
    @Override
    public void sendServiceOrderResult(ServiceOrderResultRequest request) {
        log.info("发送售后服务结果反馈:{}", JSON.toJSONString(request));
        String afterMarketOrderId = request.getOrderId();
        AfterMarketOrder2cInfo afterMarketOrder2cInfo = afterMarketOrder2cInfoMapper
                .selectByPrimaryKey(afterMarketOrderId);
        if (afterMarketOrder2cInfo == null) {
            // 售后服务订单不存在
            throw new BusinessException(BaseErrorConstant.AFTER_MARKET_INFO_NOT_EXIST);
        }
        // String orderId = afterMarketOrder2cInfo.getOfferingOrderId();
        // Order2cInfo order2cInfo = order2cInfoMapper.selectByPrimaryKey(orderId);
        // if (order2cInfo == null) {
        // // 关联商品订单不存在
        // throw new
        // BusinessException(BaseErrorConstant.AFTER_MARKET_ORDER_2C_INFO_NOT_EXIST);
        // }
        sendServiceOrderResultToIot(request, afterMarketOrder2cInfo.getBeId(), serviceOrderResultUrl,
                afterMarketOrder2cInfo.getRegionId());
    }
    /**
     * 将售后服务订单，同步到省侧（河南专用）
     */
    @Override
    public void sync2Province(Order2cInfo order2cInfo, AfterMarketOrder2cInfo afterMarketOrder2cInfo) {
        HenanZwSendOrderBusiParam zwSendOrderBusiParam = new HenanZwSendOrderBusiParam();
        zwSendOrderBusiParam.setOrderNo("jkiot" + afterMarketOrder2cInfo.getServiceOrderId());
        zwSendOrderBusiParam.setOrderType("2");
        zwSendOrderBusiParam.setOrgId("13");
        zwSendOrderBusiParam.setProductName("qly");
        zwSendOrderBusiParam.setBusinessType("1");
        zwSendOrderBusiParam.setRequiredTime(DateUtils.addDay(2));

        // 获取河南省的加密密钥
        String henanEncryptKey = provinceEncryptConfig.getEncryptKey("henan");

        zwSendOrderBusiParam.setCustomContact(
                IOTEncodeUtils.encryptIOTMessage(afterMarketOrder2cInfo.getAppointmentName(), henanEncryptKey));
        zwSendOrderBusiParam.setCustomContactPhone(
                IOTEncodeUtils.encryptIOTMessage(afterMarketOrder2cInfo.getAppointmentPhone(), henanEncryptKey));

        // 映射商城地址到省测地址
        MallAddress2ProvinceAddressInfoExample addressInfoExample = new MallAddress2ProvinceAddressInfoExample();
        MallAddress2ProvinceAddressInfoExample.Criteria addressInfoCriteria = addressInfoExample.createCriteria();
        String afterAddrProvince = IOTEncodeUtils.decryptSM4(afterMarketOrder2cInfo.getAddr1(), iotSm4Key, iotSm4Iv);
        String afterAddrCity = IOTEncodeUtils.decryptSM4(afterMarketOrder2cInfo.getAddr2(), iotSm4Key, iotSm4Iv);
        String afterAddrDistrict = IOTEncodeUtils.decryptSM4(afterMarketOrder2cInfo.getAddr3(), iotSm4Key, iotSm4Iv);
        if (StringUtils.isEmpty(afterAddrProvince) || StringUtils.isEmpty(afterAddrCity)
                || StringUtils.isEmpty(afterAddrDistrict)) {
            afterAddrProvince = IOTEncodeUtils.decryptSM4(order2cInfo.getAddr1(), iotSm4Key, iotSm4Iv);
            afterAddrCity = IOTEncodeUtils.decryptSM4(order2cInfo.getAddr2(), iotSm4Key, iotSm4Iv);
            afterAddrDistrict = IOTEncodeUtils.decryptSM4(order2cInfo.getAddr3(), iotSm4Key, iotSm4Iv);
        }

        addressInfoCriteria.andProvinceNameEqualTo(afterAddrProvince);
        addressInfoCriteria.andCityMallNameEqualTo(afterAddrCity);
        addressInfoCriteria.andDistrictMallNameEqualTo(afterAddrDistrict);
        List<MallAddress2ProvinceAddressInfo> addressInfos = mallAddress2ProvinceAddressInfoMapper
                .selectByExample(addressInfoExample);
        if (CollectionUtils.isEmpty(addressInfos) || addressInfos.size() > 1) {
            String msg = String.format("商城地址（%s,%s,%s）不能正确映射到河南省测地址",
                    afterAddrProvince,
                    afterAddrCity,
                    afterAddrDistrict);
            log.error(msg);
            throw new BusinessException(StatusConstant.SYNC_PROVINCE_FAILED, msg);
        }
        AfterMarketOrder2cOfferingInfoExample offeringInfoExample = new AfterMarketOrder2cOfferingInfoExample();
        AfterMarketOrder2cOfferingInfoExample.Criteria offeringCriteria = offeringInfoExample.createCriteria();
        offeringCriteria.andServiceOrderIdEqualTo(afterMarketOrder2cInfo.getServiceOrderId());
        List<AfterMarketOrder2cOfferingInfo> offeringInfos = afterMarketOrder2cOfferingInfoMapper
                .selectByExample(offeringInfoExample);
        AfterMarketOrder2cOfferingInfo afterMarketOrder2cOfferingInfo = offeringInfos.get(0);
        MallAddress2ProvinceAddressInfo addressInfo = addressInfos.get(0);
        zwSendOrderBusiParam
                .setProvinceA(IOTEncodeUtils.encryptIOTMessage(addressInfo.getProvinceProvinceCode(), henanEncryptKey));
        zwSendOrderBusiParam.setCityA(IOTEncodeUtils.encryptIOTMessage(addressInfo.getCityProvinceCode(), henanEncryptKey));
        zwSendOrderBusiParam
                .setAreaA(IOTEncodeUtils.encryptIOTMessage(addressInfo.getDistrictProvinceCode(), henanEncryptKey));
        if (StringUtils.isEmpty(afterMarketOrder2cInfo.getUsaddr())) {
            zwSendOrderBusiParam.setInstallAddr(IOTEncodeUtils.encryptIOTMessage(
                    IOTEncodeUtils.decryptSM4(order2cInfo.getUsaddr(), iotSm4Key, iotSm4Iv), henanEncryptKey));
        } else {
            zwSendOrderBusiParam.setInstallAddr(IOTEncodeUtils.encryptIOTMessage(
                    IOTEncodeUtils.decryptSM4(afterMarketOrder2cInfo.getUsaddr(), iotSm4Key, iotSm4Iv), henanEncryptKey));
        }
        // 查询原子订单
        Order2cAtomInfoExample atomInfoExample = new Order2cAtomInfoExample();
        Order2cAtomInfoExample.Criteria atomCriteria = atomInfoExample.createCriteria();
        atomCriteria.andOrderIdEqualTo(order2cInfo.getOrderId());
        List<Order2cAtomInfo> atomInfos = atomOrderInfoMapper.selectByExample(atomInfoExample);
        List<OrderHenanZwFeignDeviceInfo> deviceInfos = new ArrayList<>();

        List<Order2cAtomSn> order2cAtomSns = order2cAtomSnMapper.selectByExample(
                new Order2cAtomSnExample().createCriteria()
                        .andAtomOrderIdIn(atomInfos.stream().map(Order2cAtomInfo::getId).collect(Collectors.toList()))
                        .example());

        if (!CollectionUtils.isEmpty(order2cAtomSns)) {
            // 存在发货的SN，以存在的SN为准向省侧同步数据
            List<String> hasSnAtomOrderIds = order2cAtomSns.stream().map(Order2cAtomSn::getAtomOrderId)
                    .distinct().collect(Collectors.toList());
            atomInfos.stream().filter(x -> hasSnAtomOrderIds.contains(x.getId())).forEach(atom -> {
                // 增加版本号相关
                AftermarketOfferingCodeExample aftermarketOfferingCodeExample = new AftermarketOfferingCodeExample();
                AftermarketOfferingCodeExample.Criteria aftermarketOfferingCodeCriteria1 = aftermarketOfferingCodeExample
                        .or();
                // 添加非空检查，避免null值导致查询异常
                if (afterMarketOrder2cOfferingInfo.getAfterMarketCode() != null) {
                    aftermarketOfferingCodeCriteria1.andAfterMarketCodeEqualTo(afterMarketOrder2cOfferingInfo.getAfterMarketCode());
                }
                if (atom.getSkuOfferingCode() != null) {
                    aftermarketOfferingCodeCriteria1.andSkuOfferingCodeEqualTo(atom.getSkuOfferingCode());
                }
                if (afterMarketOrder2cOfferingInfo.getAfterMarketVersion() != null) {
                    aftermarketOfferingCodeCriteria1.andAfterMarketVersionEqualTo(afterMarketOrder2cOfferingInfo.getAfterMarketVersion());
                }
                if (atom.getSkuOfferingVersion() != null) {
                    aftermarketOfferingCodeCriteria1.andSkuOfferingVersionEqualTo(atom.getSkuOfferingVersion());
                }

                AftermarketOfferingCodeExample.Criteria aftermarketOfferingCodeCriteria2 = aftermarketOfferingCodeExample
                        .or();
                // 添加非空检查，避免null值导致查询异常
                if (afterMarketOrder2cOfferingInfo.getAfterMarketCode() != null) {
                    aftermarketOfferingCodeCriteria2.andAfterMarketCodeEqualTo(afterMarketOrder2cOfferingInfo.getAfterMarketCode());
                }
                if (atom.getAtomOfferingCode() != null) {
                    aftermarketOfferingCodeCriteria2.andOfferingCodeEqualTo(atom.getAtomOfferingCode());
                }
                if (afterMarketOrder2cOfferingInfo.getAfterMarketVersion() != null) {
                    aftermarketOfferingCodeCriteria2.andAfterMarketVersionEqualTo(afterMarketOrder2cOfferingInfo.getAfterMarketVersion());
                }
                if (atom.getAtomOfferingVersion() != null) {
                    aftermarketOfferingCodeCriteria2.andAtomOfferingVersionEqualTo(atom.getAtomOfferingVersion());
                }
                List<AftermarketOfferingCode> codes = aftermarketOfferingCodeMapper
                        .selectByExample(aftermarketOfferingCodeExample);

                if (!CollectionUtils.isEmpty(codes) && !CollectionUtils.isEmpty(order2cAtomSns)) {
                    order2cAtomSns.stream().filter(x -> x.getAtomOrderId().equals(atom.getId())).forEach(sn -> {
                        OrderHenanZwFeignDeviceInfo deviceInfo = new OrderHenanZwFeignDeviceInfo();
                        deviceInfo.setModel(codes.get(0).getModel());
                        deviceInfo.setSerialNumber(sn.getSn());
                        deviceInfos.add(deviceInfo);
                    });
                }
            });
        } else {
            // 向省侧发送skuQuantity数量的虚拟SN
            AftermarketOfferingCodeExample aftermarketOfferingCodeExample = new AftermarketOfferingCodeExample();
            // 增加版本号相关
            AftermarketOfferingCodeExample.Criteria aftermarketOfferingCodeCriteria1 = aftermarketOfferingCodeExample
                    .or();
            // 添加非空检查，避免null值导致查询异常
            if (afterMarketOrder2cOfferingInfo.getAfterMarketCode() != null) {
                aftermarketOfferingCodeCriteria1.andAfterMarketCodeEqualTo(afterMarketOrder2cOfferingInfo.getAfterMarketCode());
            }
            if (afterMarketOrder2cOfferingInfo.getAfterMarketVersion() != null) {
                aftermarketOfferingCodeCriteria1.andAfterMarketVersionEqualTo(afterMarketOrder2cOfferingInfo.getAfterMarketVersion());
            }
            // 过滤掉null值的SkuOfferingCode
            List<String> skuOfferingCodes = atomInfos.stream()
                    .map(Order2cAtomInfo::getSkuOfferingCode)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            if (!skuOfferingCodes.isEmpty()) {
                aftermarketOfferingCodeCriteria1.andSkuOfferingCodeIn(skuOfferingCodes);
            }

            AftermarketOfferingCodeExample.Criteria aftermarketOfferingCodeCriteria2 = aftermarketOfferingCodeExample
                    .or();
            // 添加非空检查，避免null值导致查询异常
            if (afterMarketOrder2cOfferingInfo.getAfterMarketCode() != null) {
                aftermarketOfferingCodeCriteria2.andAfterMarketCodeEqualTo(afterMarketOrder2cOfferingInfo.getAfterMarketCode());
            }
            if (afterMarketOrder2cOfferingInfo.getAfterMarketVersion() != null) {
                aftermarketOfferingCodeCriteria2.andAfterMarketVersionEqualTo(afterMarketOrder2cOfferingInfo.getAfterMarketVersion());
            }
            // 过滤掉null值的AtomOfferingCode
            List<String> atomOfferingCodes = atomInfos.stream()
                    .map(Order2cAtomInfo::getAtomOfferingCode)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            if (!atomOfferingCodes.isEmpty()) {
                aftermarketOfferingCodeCriteria2.andOfferingCodeIn(atomOfferingCodes);
            }
            List<AftermarketOfferingCode> codes = aftermarketOfferingCodeMapper
                    .selectByExample(aftermarketOfferingCodeExample);

            if (!CollectionUtils.isEmpty(codes)) {
                // 当没有原子订单的SN号信息时，填入默认的SN号，方便派单继续，当前只有OneNet范式使用
                // 添加非空检查，避免null值导致异常
                Long quantity = afterMarketOrder2cOfferingInfo.getQuantity();
                int quantityValue = quantity != null ? quantity.intValue() : 0;
                for (int i = 0; i < quantityValue; i++) {
                    OrderHenanZwFeignDeviceInfo deviceInfo = new OrderHenanZwFeignDeviceInfo();
                    deviceInfo.setModel(codes.get(0).getModel());
                    deviceInfo.setSerialNumber("000000000000000");
                    deviceInfos.add(deviceInfo);
                }
            }
        }

        zwSendOrderBusiParam.setDeviceInfo(deviceInfos);

        log.info("b2b省侧接单地址：{}{}{}{}", afterAddrProvince, afterAddrCity, afterAddrDistrict,
                IOTEncodeUtils.decryptSM4(afterMarketOrder2cInfo.getUsaddr(), iotSm4Key, iotSm4Iv));
        log.info("b2b省测接单参数：{}", JSON.toJSONString(zwSendOrderBusiParam));
        try {
            syncToHenan(zwSendOrderBusiParam);
        } catch (Exception e) {
            log.info("b2b省测接单失败：{}", e.getMessage());
            throw new BusinessException(StatusConstant.SYNC_PROVINCE_FAILED, "省测接单失败" + e.getMessage());
        }
    }

    /**
     * 通用省侧同步方法，支持自定义字段映射
     */
    @Override
    public void sync2ProvinceCommon(Order2cInfo order2cInfo, AfterMarketOrder2cInfo afterMarketOrder2cInfo) {
        CommonProvinceSyncParam commonParam = new CommonProvinceSyncParam();

        // 获取当前订单的省份信息
        AfterMarketOrder2cOfferingInfoExample offeringInfoExample = new AfterMarketOrder2cOfferingInfoExample();
        AfterMarketOrder2cOfferingInfoExample.Criteria offeringCriteria = offeringInfoExample.createCriteria();
        offeringCriteria.andServiceOrderIdEqualTo(afterMarketOrder2cInfo.getServiceOrderId());
        List<AfterMarketOrder2cOfferingInfo> offeringInfos = afterMarketOrder2cOfferingInfoMapper.selectByExample(offeringInfoExample);

        String provinceCode = "shandong"; // 默认使用山东省
        if (!CollectionUtils.isEmpty(offeringInfos)) {
            String provinceInstallPlatform = offeringInfos.get(0).getProvinceInstallPlatform();
            if (StringUtils.isNotEmpty(provinceInstallPlatform)) {
                provinceCode = provinceInstallPlatform;
            }
        }

        // 根据省份获取对应的加密密钥
        String encryptKey = provinceEncryptConfig.getEncryptKey(provinceCode);

        // 查询售后商品信息
        AfterMarketOrder2cOfferingInfo afterMarketOrder2cOfferingInfo = offeringInfos.get(0);

        // 判断商品名字是否为空(备注1)，为空则报错
        String productName = "qly"; // 默认商品名称
        String title = "IOT商城_千里眼_安装工单"; // 默认标题

        // 查询售后商品配置信息获取备注1字段
        AftermarketOfferingCodeExample aftermarketOfferingCodeExampleAll = new AftermarketOfferingCodeExample();
        AftermarketOfferingCodeExample.Criteria aftermarketOfferingCodeCriteriaAll = aftermarketOfferingCodeExampleAll.createCriteria();
        // 添加非空检查，避免null值导致查询异常
        if (afterMarketOrder2cOfferingInfo.getAfterMarketCode() != null) {
            aftermarketOfferingCodeCriteriaAll.andAfterMarketCodeEqualTo(afterMarketOrder2cOfferingInfo.getAfterMarketCode());
        }
        if (afterMarketOrder2cOfferingInfo.getAfterMarketVersion() != null) {
            aftermarketOfferingCodeCriteriaAll.andAfterMarketVersionEqualTo(afterMarketOrder2cOfferingInfo.getAfterMarketVersion());
        }
        if(afterMarketOrder2cOfferingInfo.getSpuOfferingCode() != null){
            aftermarketOfferingCodeCriteriaAll.andSpuOfferingCodeEqualTo(afterMarketOrder2cOfferingInfo.getSpuOfferingCode());
        }
        if(afterMarketOrder2cOfferingInfo.getSpuOfferingVersion() != null){
            aftermarketOfferingCodeCriteriaAll.andSpuOfferingVersionEqualTo(afterMarketOrder2cOfferingInfo.getSpuOfferingVersion());
        }
        if (afterMarketOrder2cOfferingInfo.getSkuOfferingCode() != null) {
            aftermarketOfferingCodeCriteriaAll.andSkuOfferingCodeEqualTo(afterMarketOrder2cOfferingInfo.getSkuOfferingCode());
        }
        if (afterMarketOrder2cOfferingInfo.getSkuOfferingVersion() != null) {
            aftermarketOfferingCodeCriteriaAll.andSkuOfferingVersionEqualTo(afterMarketOrder2cOfferingInfo.getSkuOfferingVersion());

        }
        if (afterMarketOrder2cOfferingInfo.getAtomOfferingCode() != null) {
            aftermarketOfferingCodeCriteriaAll.andOfferingCodeEqualTo(afterMarketOrder2cOfferingInfo.getAtomOfferingCode());

        }
        if (afterMarketOrder2cOfferingInfo.getAtomOfferingVersion() != null) {
            aftermarketOfferingCodeCriteriaAll.andAtomOfferingVersionEqualTo(afterMarketOrder2cOfferingInfo.getAtomOfferingVersion());
        }

        List<AftermarketOfferingCode> codesAll = aftermarketOfferingCodeMapper.selectByExample(aftermarketOfferingCodeExampleAll);

        if (!CollectionUtils.isEmpty(codesAll)) {
            AftermarketOfferingCode offeringCode = codesAll.get(0);
            // 判断商品名字(备注1)是否为空，为空则报错
            if (StringUtils.isEmpty(offeringCode.getRemark1())) {
                throw new BusinessException(StatusConstant.SYNC_PROVINCE_FAILED, "售后商品备注1不能为空");
            } else {
                productName = offeringCode.getRemark1();
                title = "IOT商城_" + offeringCode.getRemark1() + "_安装工单";
            }
        }

        // 基础字段设置
        commonParam.setTitle(title);
        commonParam.setOrderNo("jkiot" + afterMarketOrder2cInfo.getServiceOrderId());
        commonParam.setOrderType("2"); // 开通工单
        commonParam.setOrgId("13"); // 商城os系统
        commonParam.setProductName(productName);
        commonParam.setBusinessType("0"); // 只装不维
        commonParam.setRequiredTime(DateUtils.addDay(2));

        // 客户联系信息（加密传输）
        commonParam.setCustomContact(
                IOTEncodeUtils.encryptIOTMessage(afterMarketOrder2cInfo.getAppointmentName(), encryptKey));
        commonParam.setCustomContactPhone(
                IOTEncodeUtils.encryptIOTMessage(afterMarketOrder2cInfo.getAppointmentPhone(), encryptKey));

        // 设置客户信息
        commonParam.setCustName(IOTEncodeUtils.encryptIOTMessage(IOTEncodeUtils.decryptSM4(order2cInfo.getCustName(), iotSm4Key, iotSm4Iv),encryptKey)  );
        commonParam.setCreatTime(afterMarketOrder2cInfo.getCreateTime());

        // 映射商城地址到省测地址
        MallAddress2ProvinceAddressInfoExample addressInfoExample = new MallAddress2ProvinceAddressInfoExample();
        MallAddress2ProvinceAddressInfoExample.Criteria addressInfoCriteria = addressInfoExample.createCriteria();
        String afterAddrProvince = IOTEncodeUtils.decryptSM4(afterMarketOrder2cInfo.getAddr1(), iotSm4Key, iotSm4Iv);
        String afterAddrCity = IOTEncodeUtils.decryptSM4(afterMarketOrder2cInfo.getAddr2(), iotSm4Key, iotSm4Iv);
        String afterAddrDistrict = IOTEncodeUtils.decryptSM4(afterMarketOrder2cInfo.getAddr3(), iotSm4Key, iotSm4Iv);
        String afterDetailAddr = IOTEncodeUtils.decryptSM4(afterMarketOrder2cInfo.getUsaddr(), iotSm4Key, iotSm4Iv);

        if (StringUtils.isEmpty(afterAddrProvince) || StringUtils.isEmpty(afterAddrCity)
                || StringUtils.isEmpty(afterAddrDistrict)) {
            afterAddrProvince = IOTEncodeUtils.decryptSM4(order2cInfo.getAddr1(), iotSm4Key, iotSm4Iv);
            afterAddrCity = IOTEncodeUtils.decryptSM4(order2cInfo.getAddr2(), iotSm4Key, iotSm4Iv);
            afterAddrDistrict = IOTEncodeUtils.decryptSM4(order2cInfo.getAddr3(), iotSm4Key, iotSm4Iv);
            if (StringUtils.isEmpty(afterDetailAddr)) {
                afterDetailAddr = IOTEncodeUtils.decryptSM4(order2cInfo.getUsaddr(), iotSm4Key, iotSm4Iv);
            }
        }

        addressInfoCriteria.andProvinceNameEqualTo(afterAddrProvince);
        addressInfoCriteria.andCityMallNameEqualTo(afterAddrCity);
        addressInfoCriteria.andDistrictMallNameEqualTo(afterAddrDistrict);
        List<MallAddress2ProvinceAddressInfo> addressInfos = mallAddress2ProvinceAddressInfoMapper
                .selectByExample(addressInfoExample);
        if (CollectionUtils.isEmpty(addressInfos) || addressInfos.size() > 1) {
            String msg = String.format("商城地址（%s,%s,%s）不能正确映射到省测地址",
                    afterAddrProvince,
                    afterAddrCity,
                    afterAddrDistrict);
            log.error(msg);
            throw new BusinessException(StatusConstant.SYNC_PROVINCE_FAILED, msg);
        }
        //判断地址和开通平台是否一致
        MallAddress2ProvinceAddressInfo addressInfo = addressInfos.get(0);

        // 获取地址对应的省份名称
        String addressProvinceName = addressInfo.getProvinceName();

        // 获取订单商品配置的省份平台代码
        String provinceInstallPlatform = offeringInfos.get(0).getProvinceInstallPlatform();

        // 验证地址省份与开通平台是否一致
        if (StringUtils.isNotEmpty(provinceInstallPlatform) && StringUtils.isNotEmpty(addressProvinceName)) {
            // 根据平台代码获取对应的省份名称
            String platformProvinceName = ProvinceInstallPlatformEnum.getProvinceName(provinceInstallPlatform);

            if (StringUtils.isNotEmpty(platformProvinceName) && !addressProvinceName.equals(platformProvinceName)) {
                String msg = String.format("地址省份（%s）与开通平台省份（%s）不一致，平台代码：%s",
                        addressProvinceName, platformProvinceName, provinceInstallPlatform);
                log.error(msg);
                throw new BusinessException(StatusConstant.SYNC_PROVINCE_FAILED, msg);
            }

            log.info("地址省份（{}）与开通平台省份（{}）一致性验证通过，平台代码：{}",
                    addressProvinceName, platformProvinceName, provinceInstallPlatform);
        } else {
            throw new BusinessException(StatusConstant.SYNC_PROVINCE_FAILED, "平台代码或地址省份名称为空");

        }
        // 设置地址信息（加密传输）
        commonParam.setProvinceA(IOTEncodeUtils.encryptIOTMessage(addressInfo.getProvinceProvinceCode(), encryptKey));
        commonParam.setCityA(IOTEncodeUtils.encryptIOTMessage(addressInfo.getCityProvinceCode(), encryptKey));
        commonParam.setAreaA(IOTEncodeUtils.encryptIOTMessage(addressInfo.getDistrictProvinceCode(), encryptKey));
        commonParam.setInstallAddr(IOTEncodeUtils.encryptIOTMessage(afterDetailAddr, encryptKey));

        // 设置收货人信息（加密传输）
        // 收货人姓名 - 从订单联系人信息获取
        if (StringUtils.isNotEmpty(order2cInfo.getContactPersonName())) {
            commonParam.setContactPersonName(IOTEncodeUtils.encryptIOTMessage(
                    IOTEncodeUtils.decryptSM4(order2cInfo.getContactPersonName(), iotSm4Key, iotSm4Iv), encryptKey));
        }

        // 收货人手机号 - 从订单联系人电话获取
        if (StringUtils.isNotEmpty(order2cInfo.getContactPhone())) {
            commonParam.setContactPhone(IOTEncodeUtils.encryptIOTMessage(
                    IOTEncodeUtils.decryptSM4(order2cInfo.getContactPhone(), iotSm4Key, iotSm4Iv), encryptKey));
        }

        // 设置收货地址信息（加密传输）
        // 收货地址省份 - 使用与安装地址相同的省份编码
        commonParam.setProvinceB(IOTEncodeUtils.encryptIOTMessage(addressInfo.getProvinceProvinceCode(), encryptKey));

        // 收货地址地市 - 使用与安装地址相同的地市编码
        commonParam.setCityB(IOTEncodeUtils.encryptIOTMessage(addressInfo.getCityProvinceCode(), encryptKey));

        // 收货地址区县 - 使用与安装地址相同的区县编码
        commonParam.setAreaB(IOTEncodeUtils.encryptIOTMessage(addressInfo.getDistrictProvinceCode(), encryptKey));

        // 收货地址乡镇 - 从订单地址信息获取（使用addr4字段）
        if (StringUtils.isNotEmpty(order2cInfo.getAddr4())) {
            commonParam.setVillageB(IOTEncodeUtils.encryptIOTMessage(
                    IOTEncodeUtils.decryptSM4(order2cInfo.getAddr4(), iotSm4Key, iotSm4Iv), encryptKey));
        }

        // 收货详细地址 - 从订单收货地址获取
        if (StringUtils.isNotEmpty(order2cInfo.getUsaddr())) {
            commonParam.setUsaddrB(IOTEncodeUtils.encryptIOTMessage(
                    IOTEncodeUtils.decryptSM4(order2cInfo.getUsaddr(), iotSm4Key, iotSm4Iv), encryptKey));
        }

        // 设置物流信息 - 从logistics_info表中获取
        try {
            // 查询物流信息，优先查询发货物流（logistics_type = 0）
            LogisticsInfoExample logisticsExample = new LogisticsInfoExample();
            LogisticsInfoExample.Criteria logisticsCriteria = logisticsExample.createCriteria();
            logisticsCriteria.andOrderIdEqualTo(order2cInfo.getOrderId());
            logisticsCriteria.andLogisticsTypeEqualTo(0); // 0表示发货物流

            List<LogisticsInfo> logisticsList = logisticsInfoMapper.selectByExample(logisticsExample);

            if (CollectionUtil.isNotEmpty(logisticsList)) {
                // 取第一条物流记录
                LogisticsInfo logisticsInfo = logisticsList.get(0);

                // 物流供应商编码
                if (StringUtils.isNotEmpty(logisticsInfo.getSupplierName())) {
                    commonParam.setSupplierCode(logisticsInfo.getSupplierName());
                }

                // 物流单号
                if (StringUtils.isNotEmpty(logisticsInfo.getLogisCode())) {
                    commonParam.setLogisCode(logisticsInfo.getLogisCode());
                }
            }
        } catch (Exception e) {
            log.warn("查询物流信息失败，订单ID: {}, 错误信息: {}", order2cInfo.getOrderId(), e.getMessage());
        }

        // 查询原子订单
        Order2cAtomInfoExample atomInfoExample = new Order2cAtomInfoExample();
        Order2cAtomInfoExample.Criteria atomCriteria = atomInfoExample.createCriteria();
        atomCriteria.andOrderIdEqualTo(order2cInfo.getOrderId());
        List<Order2cAtomInfo> atomInfos = atomOrderInfoMapper.selectByExample(atomInfoExample);
        List<CommonProvinceSyncParam.DeviceInfo> deviceInfos = new ArrayList<>();

        List<Order2cAtomSn> order2cAtomSns = order2cAtomSnMapper.selectByExample(
                new Order2cAtomSnExample().createCriteria()
                        .andAtomOrderIdIn(atomInfos.stream().map(Order2cAtomInfo::getId).collect(Collectors.toList()))
                        .example());

        if (!CollectionUtils.isEmpty(order2cAtomSns)) {
            // 存在发货的SN，以存在的SN为准向省侧同步数据
            List<String> hasSnAtomOrderIds = order2cAtomSns.stream().map(Order2cAtomSn::getAtomOrderId)
                    .distinct().collect(Collectors.toList());
            atomInfos.stream().filter(x -> hasSnAtomOrderIds.contains(x.getId())).forEach(atom -> {
                // 查询售后商品配置信息
                AftermarketOfferingCodeExample aftermarketOfferingCodeExample = new AftermarketOfferingCodeExample();
                AftermarketOfferingCodeExample.Criteria aftermarketOfferingCodeCriteria1 = aftermarketOfferingCodeExample
                        .or();
                // 添加非空检查，避免null值导致查询异常
                if (afterMarketOrder2cOfferingInfo.getAfterMarketCode() != null) {
                    aftermarketOfferingCodeCriteria1.andAfterMarketCodeEqualTo(afterMarketOrder2cOfferingInfo.getAfterMarketCode());
                }
                if (atom.getSkuOfferingCode() != null) {
                    aftermarketOfferingCodeCriteria1.andSkuOfferingCodeEqualTo(atom.getSkuOfferingCode());
                }
                if (afterMarketOrder2cOfferingInfo.getAfterMarketVersion() != null) {
                    aftermarketOfferingCodeCriteria1.andAfterMarketVersionEqualTo(afterMarketOrder2cOfferingInfo.getAfterMarketVersion());
                }
                if (atom.getSkuOfferingVersion() != null) {
                    aftermarketOfferingCodeCriteria1.andSkuOfferingVersionEqualTo(atom.getSkuOfferingVersion());
                }

                AftermarketOfferingCodeExample.Criteria aftermarketOfferingCodeCriteria2 = aftermarketOfferingCodeExample
                        .or();
                // 添加非空检查，避免null值导致查询异常
                if (afterMarketOrder2cOfferingInfo.getAfterMarketCode() != null) {
                    aftermarketOfferingCodeCriteria2.andAfterMarketCodeEqualTo(afterMarketOrder2cOfferingInfo.getAfterMarketCode());
                }
                if (atom.getAtomOfferingCode() != null) {
                    aftermarketOfferingCodeCriteria2.andOfferingCodeEqualTo(atom.getAtomOfferingCode());
                }
                if (afterMarketOrder2cOfferingInfo.getAfterMarketVersion() != null) {
                    aftermarketOfferingCodeCriteria2.andAfterMarketVersionEqualTo(afterMarketOrder2cOfferingInfo.getAfterMarketVersion());
                }
                if (atom.getAtomOfferingVersion() != null) {
                    aftermarketOfferingCodeCriteria2.andAtomOfferingVersionEqualTo(atom.getAtomOfferingVersion());
                }
                List<AftermarketOfferingCode> codes = aftermarketOfferingCodeMapper
                        .selectByExample(aftermarketOfferingCodeExample);

                if (!CollectionUtils.isEmpty(codes) && !CollectionUtils.isEmpty(order2cAtomSns)) {
                    order2cAtomSns.stream().filter(x -> x.getAtomOrderId().equals(atom.getId())).forEach(sn -> {
                        CommonProvinceSyncParam.DeviceInfo deviceInfo = new CommonProvinceSyncParam.DeviceInfo();
                        deviceInfo.setModel(codes.get(0).getModel());
                        deviceInfo.setSerialNumber(sn.getSn());
                        deviceInfos.add(deviceInfo);
                    });
                }
            });
        } else {
            // 向省侧发送skuQuantity数量的虚拟SN
            AftermarketOfferingCodeExample aftermarketOfferingCodeExample = new AftermarketOfferingCodeExample();
            AftermarketOfferingCodeExample.Criteria aftermarketOfferingCodeCriteria1 = aftermarketOfferingCodeExample
                    .or();
            // 添加非空检查，避免null值导致查询异常
            if (afterMarketOrder2cOfferingInfo.getAfterMarketCode() != null) {
                aftermarketOfferingCodeCriteria1.andAfterMarketCodeEqualTo(afterMarketOrder2cOfferingInfo.getAfterMarketCode());
            }
            if (afterMarketOrder2cOfferingInfo.getAfterMarketVersion() != null) {
                aftermarketOfferingCodeCriteria1.andAfterMarketVersionEqualTo(afterMarketOrder2cOfferingInfo.getAfterMarketVersion());
            }
            // 过滤掉null值的SkuOfferingCode
            List<String> skuOfferingCodes = atomInfos.stream()
                    .map(Order2cAtomInfo::getSkuOfferingCode)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            if (!skuOfferingCodes.isEmpty()) {
                aftermarketOfferingCodeCriteria1.andSkuOfferingCodeIn(skuOfferingCodes);
            }

            AftermarketOfferingCodeExample.Criteria aftermarketOfferingCodeCriteria2 = aftermarketOfferingCodeExample
                    .or();
            // 添加非空检查，避免null值导致查询异常
            if (afterMarketOrder2cOfferingInfo.getAfterMarketCode() != null) {
                aftermarketOfferingCodeCriteria2.andAfterMarketCodeEqualTo(afterMarketOrder2cOfferingInfo.getAfterMarketCode());
            }
            if (afterMarketOrder2cOfferingInfo.getAfterMarketVersion() != null) {
                aftermarketOfferingCodeCriteria2.andAfterMarketVersionEqualTo(afterMarketOrder2cOfferingInfo.getAfterMarketVersion());
            }
            // 过滤掉null值的AtomOfferingCode
            List<String> atomOfferingCodes = atomInfos.stream()
                    .map(Order2cAtomInfo::getAtomOfferingCode)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            if (!atomOfferingCodes.isEmpty()) {
                aftermarketOfferingCodeCriteria2.andOfferingCodeIn(atomOfferingCodes);
            }
            List<AftermarketOfferingCode> codes = aftermarketOfferingCodeMapper
                    .selectByExample(aftermarketOfferingCodeExample);

            if (!CollectionUtils.isEmpty(codes)) {
                // 当没有原子订单的SN号信息时，填入默认的SN号，方便派单继续，当前只有OneNet范式使用
                // 添加非空检查，避免null值导致异常
                Long quantity = afterMarketOrder2cOfferingInfo.getQuantity();
                int quantityValue = quantity != null ? quantity.intValue() : 0;
                for (int i = 0; i < quantityValue; i++) {
                    CommonProvinceSyncParam.DeviceInfo deviceInfo = new CommonProvinceSyncParam.DeviceInfo();
                    deviceInfo.setModel(codes.get(0).getModel());
                    deviceInfo.setSerialNumber("000000000000000");
                    deviceInfos.add(deviceInfo);
                }
            }
        }

        // 设置设备信息和数量
        commonParam.setDeviceInfo(deviceInfos);
        // 添加非空检查，避免null值导致异常
        Long quantity = afterMarketOrder2cOfferingInfo.getQuantity();
        commonParam.setQuantity(String.valueOf(quantity != null ? quantity : 0));

        // 设置客户编码和客户经理信息（可选字段）
        if (StringUtils.isNotEmpty(order2cInfo.getCustCode())) {
            commonParam.setCustCode(IOTEncodeUtils.encryptIOTMessage(IOTEncodeUtils.decryptSM4(order2cInfo.getCustCode(), iotSm4Key, iotSm4Iv),encryptKey));
        }

        // 查询客户经理信息
        if(order2cInfo.getBillLadderType()!=null&& order2cInfo.getBillLadderType().equals("1")){
            if (StringUtils.isNotEmpty(order2cInfo.getCustMgPhone())) {

                commonParam.setCreateOperPhone(IOTEncodeUtils.decryptSM4(order2cInfo.getCustMgPhone(), iotSm4Key, iotSm4Iv) );
            }

            if (StringUtils.isNotEmpty(order2cInfo.getCustMgName())) {

                commonParam.setCustomerManagerName(IOTEncodeUtils.decryptSM4(order2cInfo.getCustMgName(), iotSm4Key, iotSm4Iv) );
            }
        }

        log.info("b2b省侧接单地址：{}{}{}{}", afterAddrProvince, afterAddrCity, afterAddrDistrict, afterDetailAddr);
        log.info("b2b省测接单参数（通用方法）：{}", JSON.toJSONString(commonParam));
        try {
            syncToCommonProvince(commonParam);
        } catch (Exception e) {
            log.info("b2b省测接单失败（通用方法）：{}", e.getMessage());
            throw new BusinessException(StatusConstant.SYNC_PROVINCE_FAILED, "省测接单失败" + e.getMessage());
        }
    }

    @Override
    public void sync2ProvinceForConfigCommon(Order2cInfo order2cInfo, AfterMarketOrder2cInfo afterMarketOrder2cInfo,
                                             AftermarketOfferingCode aftermarketOfferingCode) {
        CommonProvinceSyncParam commonParam = new CommonProvinceSyncParam();

        // 获取当前订单的省份信息
        AfterMarketOrder2cOfferingInfoExample offeringInfoExample = new AfterMarketOrder2cOfferingInfoExample();
        AfterMarketOrder2cOfferingInfoExample.Criteria offeringCriteria = offeringInfoExample.createCriteria();
        offeringCriteria.andServiceOrderIdEqualTo(afterMarketOrder2cInfo.getServiceOrderId());
        List<AfterMarketOrder2cOfferingInfo> offeringInfos = afterMarketOrder2cOfferingInfoMapper.selectByExample(offeringInfoExample);

        String provinceCode = "shandong";
        if (!CollectionUtils.isEmpty(offeringInfos)) {
            String provinceInstallPlatform = offeringInfos.get(0).getProvinceInstallPlatform();
            if (StringUtils.isNotEmpty(provinceInstallPlatform)) {
                provinceCode = provinceInstallPlatform;
            }
        }

        // 根据省份获取对应的加密密钥
        String encryptKey = provinceEncryptConfig.getEncryptKey(provinceCode);
        if(StringUtils.isEmpty(aftermarketOfferingCode.getRemark1())){
            throw new BusinessException(StatusConstant.SYNC_PROVINCE_FAILED, "售后商品备注1不能为空");
        }else{
            commonParam.setTitle("IOT商城_" + aftermarketOfferingCode.getRemark1() + "_安装工单");
        }
        // 基础字段设置

        commonParam.setOrderNo("jkiot" + afterMarketOrder2cInfo.getServiceOrderId());
        commonParam.setOrderType("2"); // 开通工单
        commonParam.setOrgId("13"); // 商城os系统
        commonParam.setProductName(aftermarketOfferingCode.getRemark1()); // IOT千里眼
        commonParam.setBusinessType("0"); // 只装不维
        commonParam.setRequiredTime(DateUtils.addDay(2));

        // 客户联系信息（加密传输）
        commonParam.setCustomContact(
                IOTEncodeUtils.encryptIOTMessage(afterMarketOrder2cInfo.getAppointmentName(), encryptKey));
        commonParam.setCustomContactPhone(
                IOTEncodeUtils.encryptIOTMessage(afterMarketOrder2cInfo.getAppointmentPhone(), encryptKey));

        // 设置客户信息
        commonParam.setCustName(IOTEncodeUtils.decryptSM4(order2cInfo.getCustName(), iotSm4Key, iotSm4Iv));
        commonParam.setCreatTime(afterMarketOrder2cInfo.getCreateTime());

        // 映射商城地址到省测地址
        MallAddress2ProvinceAddressInfoExample addressInfoExample = new MallAddress2ProvinceAddressInfoExample();
        MallAddress2ProvinceAddressInfoExample.Criteria addressInfoCriteria = addressInfoExample.createCriteria();
        String afterAddrProvince = IOTEncodeUtils.decryptSM4(afterMarketOrder2cInfo.getAddr1(), iotSm4Key, iotSm4Iv);
        String afterAddrCity = IOTEncodeUtils.decryptSM4(afterMarketOrder2cInfo.getAddr2(), iotSm4Key, iotSm4Iv);
        String afterAddrDistrict = IOTEncodeUtils.decryptSM4(afterMarketOrder2cInfo.getAddr3(), iotSm4Key, iotSm4Iv);
        String afterDetailAddr = IOTEncodeUtils.decryptSM4(afterMarketOrder2cInfo.getUsaddr(), iotSm4Key, iotSm4Iv);

        if (StringUtils.isEmpty(afterAddrProvince) || StringUtils.isEmpty(afterAddrCity)
                || StringUtils.isEmpty(afterAddrDistrict)) {
            afterAddrProvince = IOTEncodeUtils.decryptSM4(order2cInfo.getAddr1(), iotSm4Key, iotSm4Iv);
            afterAddrCity = IOTEncodeUtils.decryptSM4(order2cInfo.getAddr2(), iotSm4Key, iotSm4Iv);
            afterAddrDistrict = IOTEncodeUtils.decryptSM4(order2cInfo.getAddr3(), iotSm4Key, iotSm4Iv);
            if (StringUtils.isEmpty(afterDetailAddr)) {
                afterDetailAddr = IOTEncodeUtils.decryptSM4(order2cInfo.getUsaddr(), iotSm4Key, iotSm4Iv);
            }
        }

        addressInfoCriteria.andProvinceNameEqualTo(afterAddrProvince);
        addressInfoCriteria.andCityMallNameEqualTo(afterAddrCity);
        addressInfoCriteria.andDistrictMallNameEqualTo(afterAddrDistrict);
        List<MallAddress2ProvinceAddressInfo> addressInfos = mallAddress2ProvinceAddressInfoMapper
                .selectByExample(addressInfoExample);
        if (CollectionUtils.isEmpty(addressInfos) || addressInfos.size() > 1) {
            String msg = String.format("商城地址（%s,%s,%s）不能正确映射到省测地址",
                    afterAddrProvince,
                    afterAddrCity,
                    afterAddrDistrict);
            log.error(msg);
            throw new BusinessException(StatusConstant.SYNC_PROVINCE_FAILED, msg);
        }

        MallAddress2ProvinceAddressInfo addressInfo = addressInfos.get(0);
        // 设置地址信息（加密传输）
        commonParam.setProvinceA(IOTEncodeUtils.encryptIOTMessage(addressInfo.getProvinceProvinceCode(), encryptKey));
        commonParam.setCityA(IOTEncodeUtils.encryptIOTMessage(addressInfo.getCityProvinceCode(), encryptKey));
        commonParam.setAreaA(IOTEncodeUtils.encryptIOTMessage(addressInfo.getDistrictProvinceCode(), encryptKey));
        commonParam.setInstallAddr(IOTEncodeUtils.encryptIOTMessage(afterDetailAddr, encryptKey));

        // 查询售后商品信息
        AfterMarketOrder2cOfferingInfo afterMarketOrder2cOfferingInfo = offeringInfos.get(0);

        // 查询原子订单
        Order2cAtomInfoExample atomInfoExample = new Order2cAtomInfoExample();
        Order2cAtomInfoExample.Criteria atomCriteria = atomInfoExample.createCriteria();
        atomCriteria.andOrderIdEqualTo(order2cInfo.getOrderId());
        List<Order2cAtomInfo> atomInfos = atomOrderInfoMapper.selectByExample(atomInfoExample);
        List<CommonProvinceSyncParam.DeviceInfo> deviceInfos = new ArrayList<>();

        List<Order2cAtomSn> order2cAtomSns = order2cAtomSnMapper.selectByExample(
                new Order2cAtomSnExample().createCriteria()
                        .andAtomOrderIdIn(atomInfos.stream().map(Order2cAtomInfo::getId).collect(Collectors.toList()))
                        .example());

        if (!CollectionUtils.isEmpty(order2cAtomSns)) {
            // 存在发货的SN，以存在的SN为准向省侧同步数据
            List<String> hasSnAtomOrderIds = order2cAtomSns.stream().map(Order2cAtomSn::getAtomOrderId)
                    .distinct().collect(Collectors.toList());
            atomInfos.stream().filter(x -> hasSnAtomOrderIds.contains(x.getId())).forEach(atom -> {
                if (!CollectionUtils.isEmpty(order2cAtomSns)) {
                    order2cAtomSns.stream().filter(x -> x.getAtomOrderId().equals(atom.getId())).forEach(sn -> {
                        CommonProvinceSyncParam.DeviceInfo deviceInfo = new CommonProvinceSyncParam.DeviceInfo();
                        deviceInfo.setModel(aftermarketOfferingCode.getModel());
                        deviceInfo.setSerialNumber(sn.getSn());
                        deviceInfos.add(deviceInfo);
                    });
                }
            });
        } else {
            // 当没有原子订单的SN号信息时，填入默认的SN号，方便派单继续，当前只有OneNet范式使用
            // 添加非空检查，避免null值导致异常
            Long quantity = afterMarketOrder2cOfferingInfo.getQuantity();
            int quantityValue = quantity != null ? quantity.intValue() : 0;
            for (int i = 0; i < quantityValue; i++) {
                CommonProvinceSyncParam.DeviceInfo deviceInfo = new CommonProvinceSyncParam.DeviceInfo();
                deviceInfo.setModel(aftermarketOfferingCode.getModel());
                deviceInfo.setSerialNumber("000000000000000");
                deviceInfos.add(deviceInfo);
            }
        }

        // 设置设备信息和数量
        commonParam.setDeviceInfo(deviceInfos);
        // 添加非空检查，避免null值导致异常
        Long quantity = afterMarketOrder2cOfferingInfo.getQuantity();
        commonParam.setQuantity(String.valueOf(quantity != null ? quantity : 0));

        // 设置客户编码和客户经理信息（可选字段）
        if (StringUtils.isNotEmpty(order2cInfo.getCustCode())) {
            commonParam.setCustCode(IOTEncodeUtils.decryptSM4(order2cInfo.getCustCode(), iotSm4Key, iotSm4Iv) );
        }

        // 查询客户经理信息
        if(order2cInfo.getBillLadderType().equals("1")){
            if (StringUtils.isNotEmpty(order2cInfo.getCustMgPhone())) {
                commonParam.setCreateOperPhone(order2cInfo.getCustMgPhone());
            }

            if (StringUtils.isNotEmpty(order2cInfo.getCustMgName())) {
                commonParam.setCustomerManagerName(order2cInfo.getCustMgName());
            }
        }

        log.info("通用省侧接单地址：{}{}{}{}", afterAddrProvince, afterAddrCity, afterAddrDistrict, afterDetailAddr);
        log.info("通用省测接单参数（配置方法）：{}", JSON.toJSONString(commonParam));
        try {
            syncToCommonProvince(commonParam);
        } catch (Exception e) {
            log.info("通用省测接单失败（配置方法）：{}", e.getMessage());
            throw new BusinessException(StatusConstant.SYNC_PROVINCE_FAILED, "省测接单失败" + e.getMessage());
        }
    }

    @Override
    public void sync2ProvinceForConfig(Order2cInfo order2cInfo, AfterMarketOrder2cInfo afterMarketOrder2cInfo,
                                       AftermarketOfferingCode aftermarketOfferingCode) {
        HenanZwSendOrderBusiParam zwSendOrderBusiParam = new HenanZwSendOrderBusiParam();
        zwSendOrderBusiParam.setOrderNo("jkiot" + afterMarketOrder2cInfo.getServiceOrderId());
        zwSendOrderBusiParam.setOrderType("2");
        zwSendOrderBusiParam.setOrgId("13");
        zwSendOrderBusiParam.setProductName("qly");
        zwSendOrderBusiParam.setBusinessType("1");
        zwSendOrderBusiParam.setRequiredTime(DateUtils.addDay(2));

        // 获取河南省的加密密钥
        String henanEncryptKey = provinceEncryptConfig.getEncryptKey("henan");

        zwSendOrderBusiParam.setCustomContact(
                IOTEncodeUtils.encryptIOTMessage(afterMarketOrder2cInfo.getAppointmentName(), henanEncryptKey));
        zwSendOrderBusiParam.setCustomContactPhone(
                IOTEncodeUtils.encryptIOTMessage(afterMarketOrder2cInfo.getAppointmentPhone(), henanEncryptKey));

        // 映射商城地址到省测地址
        MallAddress2ProvinceAddressInfoExample addressInfoExample = new MallAddress2ProvinceAddressInfoExample();
        MallAddress2ProvinceAddressInfoExample.Criteria addressInfoCriteria = addressInfoExample.createCriteria();
        String afterAddrProvince = IOTEncodeUtils.decryptSM4(afterMarketOrder2cInfo.getAddr1(), iotSm4Key, iotSm4Iv);
        String afterAddrCity = IOTEncodeUtils.decryptSM4(afterMarketOrder2cInfo.getAddr2(), iotSm4Key, iotSm4Iv);
        String afterAddrDistrict = IOTEncodeUtils.decryptSM4(afterMarketOrder2cInfo.getAddr3(), iotSm4Key, iotSm4Iv);
        if (StringUtils.isEmpty(afterAddrProvince) || StringUtils.isEmpty(afterAddrCity)
                || StringUtils.isEmpty(afterAddrDistrict)) {
            afterAddrProvince = IOTEncodeUtils.decryptSM4(order2cInfo.getAddr1(), iotSm4Key, iotSm4Iv);
            afterAddrCity = IOTEncodeUtils.decryptSM4(order2cInfo.getAddr2(), iotSm4Key, iotSm4Iv);
            afterAddrDistrict = IOTEncodeUtils.decryptSM4(order2cInfo.getAddr3(), iotSm4Key, iotSm4Iv);
        }

        addressInfoCriteria.andProvinceNameEqualTo(afterAddrProvince);
        addressInfoCriteria.andCityMallNameEqualTo(afterAddrCity);
        addressInfoCriteria.andDistrictMallNameEqualTo(afterAddrDistrict);
        List<MallAddress2ProvinceAddressInfo> addressInfos = mallAddress2ProvinceAddressInfoMapper
                .selectByExample(addressInfoExample);
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(addressInfos) || addressInfos.size() > 1) {
            String msg = String.format("商城地址（%s,%s,%s）不能正确映射到河南省测地址",
                    afterAddrProvince,
                    afterAddrCity,
                    afterAddrDistrict);
            log.error(msg);
            throw new BusinessException(StatusConstant.SYNC_PROVINCE_FAILED, msg);
        }

        AfterMarketOrder2cOfferingInfoExample offeringInfoExample = new AfterMarketOrder2cOfferingInfoExample();
        AfterMarketOrder2cOfferingInfoExample.Criteria offeringCriteria = offeringInfoExample.createCriteria();
        offeringCriteria.andServiceOrderIdEqualTo(afterMarketOrder2cInfo.getServiceOrderId());
        List<AfterMarketOrder2cOfferingInfo> offeringInfos = afterMarketOrder2cOfferingInfoMapper
                .selectByExample(offeringInfoExample);
        AfterMarketOrder2cOfferingInfo afterMarketOrder2cOfferingInfo = offeringInfos.get(0);

        MallAddress2ProvinceAddressInfo addressInfo = addressInfos.get(0);
        zwSendOrderBusiParam
                .setProvinceA(IOTEncodeUtils.encryptIOTMessage(addressInfo.getProvinceProvinceCode(), henanEncryptKey));
        zwSendOrderBusiParam.setCityA(IOTEncodeUtils.encryptIOTMessage(addressInfo.getCityProvinceCode(), henanEncryptKey));
        zwSendOrderBusiParam
                .setAreaA(IOTEncodeUtils.encryptIOTMessage(addressInfo.getDistrictProvinceCode(), henanEncryptKey));
        if (StringUtils.isEmpty(afterMarketOrder2cInfo.getUsaddr())) {
            zwSendOrderBusiParam.setInstallAddr(IOTEncodeUtils.encryptIOTMessage(
                    IOTEncodeUtils.decryptSM4(order2cInfo.getUsaddr(), iotSm4Key, iotSm4Iv), henanEncryptKey));
        } else {
            zwSendOrderBusiParam.setInstallAddr(IOTEncodeUtils.encryptIOTMessage(
                    IOTEncodeUtils.decryptSM4(afterMarketOrder2cInfo.getUsaddr(), iotSm4Key, iotSm4Iv), henanEncryptKey));
        }
        // 查询原子订单
        Order2cAtomInfoExample atomInfoExample = new Order2cAtomInfoExample();
        Order2cAtomInfoExample.Criteria atomCriteria = atomInfoExample.createCriteria();
        atomCriteria.andOrderIdEqualTo(order2cInfo.getOrderId());
        List<Order2cAtomInfo> atomInfos = atomOrderInfoMapper.selectByExample(atomInfoExample);
        List<OrderHenanZwFeignDeviceInfo> deviceInfos = new ArrayList<>();

        List<Order2cAtomSn> order2cAtomSns = order2cAtomSnMapper.selectByExample(
                new Order2cAtomSnExample().createCriteria()
                        .andAtomOrderIdIn(atomInfos.stream().map(Order2cAtomInfo::getId).collect(Collectors.toList()))
                        .example());

        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(order2cAtomSns)) {
            // 存在发货的SN，以存在的SN为准向省侧同步数据
            List<String> hasSnAtomOrderIds = order2cAtomSns.stream().map(Order2cAtomSn::getAtomOrderId)
                    .distinct().collect(Collectors.toList());
            atomInfos.stream().filter(x -> hasSnAtomOrderIds.contains(x.getId())).forEach(atom -> {
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(order2cAtomSns)) {
                    order2cAtomSns.stream().filter(x -> x.getAtomOrderId().equals(atom.getId())).forEach(sn -> {
                        OrderHenanZwFeignDeviceInfo deviceInfo = new OrderHenanZwFeignDeviceInfo();
                        deviceInfo.setModel(aftermarketOfferingCode.getModel());
                        deviceInfo.setSerialNumber(sn.getSn());
                        deviceInfos.add(deviceInfo);
                    });
                }
            });
        } else {
            // 当没有原子订单的SN号信息时，填入默认的SN号，方便派单继续，当前只有OneNet范式使用
            // 添加非空检查，避免null值导致异常
            Long quantity = afterMarketOrder2cOfferingInfo.getQuantity();
            int quantityValue = quantity != null ? quantity.intValue() : 0;
            for (int i = 0; i < quantityValue; i++) {
                OrderHenanZwFeignDeviceInfo deviceInfo = new OrderHenanZwFeignDeviceInfo();
                deviceInfo.setModel(aftermarketOfferingCode.getModel());
                deviceInfo.setSerialNumber("000000000000000");
                deviceInfos.add(deviceInfo);
            }
        }

        zwSendOrderBusiParam.setDeviceInfo(deviceInfos);

        log.info("syncToHenan省侧接单地址：{}{}{}{}", afterAddrProvince, afterAddrCity, afterAddrDistrict,
                IOTEncodeUtils.decryptSM4(afterMarketOrder2cInfo.getUsaddr(), iotSm4Key, iotSm4Iv));
        log.info("syncToHenan省测接单参数：{}", JSON.toJSONString(zwSendOrderBusiParam));
        try {
           syncToHenan(zwSendOrderBusiParam);
        } catch (Exception e) {
            log.info("syncToHenan省测接单失败：{}", e.getMessage());
            throw new BusinessException(StatusConstant.SYNC_PROVINCE_FAILED, "syncToHenan省测接单失败" + e.getMessage());
        }
    }
    private void sendServiceOrderResultToIot(Object o, String beId, String url, String regionId) {
        HttpHeaders headers = new HttpHeaders();
        headers.add("content-type", "application/json;charset=utf-8");

        String iotRequest = IOTRequestUtils.getIotRequest(JSON.toJSONString(o), secretKey, beId, regionId);
        log.info("请求IOT商城内容为:" + iotRequest);
        HttpEntity<String> requestEntity = new HttpEntity<>(iotRequest, headers);
        ResponseEntity<IOTAnswer> response;
        try {
            HttpComponentsClientHttpRequestFactory factory = RestTemplateConfig.generateHttpRequestFactory();
            factory.setConnectTimeout(20000);
            factory.setConnectionRequestTimeout(20000);
            factory.setReadTimeout(20000);
            RestTemplate restTemplateHttps = new RestTemplate(factory);
            response = restTemplateHttps.postForEntity(url, requestEntity, IOTAnswer.class);
        } catch (Exception e) {
            log.error("{}请求IOT异常捕获:{}", iotRequest, e);
            throw new BusinessException(StatusConstant.SYNC_IOT_FAILED);
        }
        IOTAnswer iotAnswer = response.getBody();
        if (iotAnswer == null || !"0".equals(iotAnswer.getResultCode())) {
            // 没返回信息则为提示同步失败，返回错误码则提示返回错误描述
            log.error("{}同步到IOT商城失败，请求地址为:{}，返回结果为:{}", iotRequest, url, iotAnswer);
            throw new BusinessException(StatusConstant.SYNC_IOT_FAILED,
                    iotAnswer == null ? "同步到IOT商城失败，请重试" : iotAnswer.getResultDesc());
        }
        log.info("{}请求IOT商城结果返回为:{}", iotRequest, iotAnswer);
    }


    /**
     * 单进度接口（透明化）
     * 用于省侧装维业务平台向商城OS系统同步工单处理进度信息
     * 整合了openResp、openRejct、openAccept三个方法的功能
     *
     * @param request 进度同步请求参数
     * @return 进度同步响应
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProgressSyncResponseBody progressSync(ProgressSyncRequest request) {
        try {
            log.info("接收到进度同步请求：{}", JSON.toJSONString(request));

            // 参数校验
            String validationResult = validateProgressSyncRequest(request);
            if (validationResult != null) {
                log.warn("进度同步请求参数校验失败：{}", validationResult);
                return ProgressSyncResponseBody.error(validationResult);
            }

            // 查询订单信息
            String orderNo = request.getOrderNo();
            if (!orderNo.startsWith(ORDER_PREFIX)) {
                orderNo = ORDER_PREFIX + orderNo;
            }

            // 根据操作动作分发处理逻辑
            String dealAction = request.getDealAction();
            switch (dealAction) {
                case "C01": // 待派单
                    return handleWaitDispatch(request, orderNo);
                case "C02": // 已驳回 - 对应openRejct功能
                    return handleReject(request, orderNo);
                case "C03": // 已派单 - 对应openAccept功能
                    return handleDispatch(request, orderNo);
                case "C04": // 已签到
                    return handleSignIn(request, orderNo);
                case "C05": // 已完结（成功） - 对应openResp成功功能
                    return handleCompleteSuccess(request, orderNo);
                case "C06": // 已完结（失败） - 对应openResp失败功能
                    return handleCompleteFailed(request, orderNo);
                default:
                    log.warn("不支持的操作动作：{}", dealAction);
                    return ProgressSyncResponseBody.error("不支持的操作动作：" + dealAction);
            }

        } catch (Exception e) {
            log.error("进度同步处理异常：{}", e.getMessage(), e);
            return ProgressSyncResponseBody.error("系统内部错误：" + e.getMessage());
        }
    }

    /**
     * 处理待派单状态
     */
    private ProgressSyncResponseBody handleWaitDispatch(ProgressSyncRequest request, String orderNo) {
        log.info("处理待派单状态，订单号：{}", orderNo);

        // 查找订单
        AfterMarketOrder2cInfo afterMarketOrder = findAfterMarketOrder(orderNo);
        if (afterMarketOrder == null) {
            return ProgressSyncResponseBody.error("订单不存在");
        }

        // 更新订单状态为待派单
        Date now = new Date();
        afterMarketOrder.setStatus(AfterMarketOrderStatusEnum.DISPATCHING.getStatus()); // 待派单
        afterMarketOrder.setUpdateTime(now);
        afterMarketOrder2cInfoMapper.updateByPrimaryKeySelective(afterMarketOrder);

        // 记录操作历史
        recordProgressHistory(afterMarketOrder, request, AfterMarketOrderStatusEnum.DISPATCHING.getStatus(), "进度同步：待派单");

        log.info("待派单处理成功，订单号：{}", orderNo);
        return ProgressSyncResponseBody.success();
    }

    /**
     * 处理驳回状态 - 对应原openRejct功能
     */
    private ProgressSyncResponseBody handleReject(ProgressSyncRequest request, String orderNo) {
        log.info("处理驳回状态，订单号：{}", orderNo);

        // 查找订单
        AfterMarketOrder2cInfo afterMarketOrder = findAfterMarketOrder(orderNo);
        if (afterMarketOrder == null) {
            return ProgressSyncResponseBody.error("订单不存在");
        }
        //判断是否已同步C03已派单，否则直接报错
        if (!hasDispatchedStatus(afterMarketOrder.getServiceOrderId())) {
            log.warn("订单{}未经过派单状态，不允许驳回", orderNo);
            return ProgressSyncResponseBody.error("订单未经过派单状态，不允许驳回");
        }

        // 更新订单状态为已完结（失败）
        Date now = new Date();
        afterMarketOrder.setStatus(AfterMarketOrderStatusEnum.DELIVERY_FAIL.getStatus()); // 已完结（失败）
        afterMarketOrder.setUpdateTime(now);
        afterMarketOrder2cInfoMapper.updateByPrimaryKeySelective(afterMarketOrder);

        // 记录操作历史
        String operateMessage = "进度同步：已驳回";
        if (StringUtils.isNotBlank(request.getResultDesc())) {
            operateMessage += "，原因：" + request.getResultDesc();
        }
        recordProgressHistory(afterMarketOrder, request, AfterMarketOrderStatusEnum.DELIVERY_FAIL.getStatus(), operateMessage);

        // 同步到商城 - 对应原openRejct中的逻辑
        try {
            ServiceOrderResultRequest resultRequest = new ServiceOrderResultRequest();
            resultRequest.setOrderId(afterMarketOrder.getServiceOrderId());
            resultRequest.setOprType("2");
            ServiceOrderResultRequest.DeliverInfo deliverInfo = new ServiceOrderResultRequest.DeliverInfo();
            deliverInfo.setDeliverResult("2");
            deliverInfo.setReason(request.getResultDesc());
            resultRequest.setDeliverInfo(deliverInfo);
            sendServiceOrderResult(resultRequest);
        } catch (Exception e) {
            log.error("同步驳回结果到商城失败：{}", e.getMessage(), e);
        }

        log.info("驳回处理成功，订单号：{}", orderNo);
        return ProgressSyncResponseBody.success();
    }

    /**
     * 校验进度同步请求参数
     */
    private String validateProgressSyncRequest(ProgressSyncRequest request) {
        // 校验流程名称
        if (!FlowNameEnum.isValid(request.getFlowName())) {
            return "无效的流程名称：" + request.getFlowName();
        }

        // 校验工单状态
        if (!SheetStateEnum.isValid(request.getSheetState())) {
            return "无效的工单状态：" + request.getSheetState();
        }

        // 校验操作动作
        if (!DealActionEnum.isValid(request.getDealAction())) {
            return "无效的操作动作：" + request.getDealAction();
        }

        // 校验条件必填字段
        if (DealActionEnum.needResultDesc(request.getDealAction()) &&
            StringUtils.isBlank(request.getResultDesc())) {
            return "操作动作为" + request.getDealAction() + "时，驳回原因必填";
        }

        if (DealActionEnum.needContactInfo(request.getDealAction()) &&
            (StringUtils.isBlank(request.getOpPerson()) || StringUtils.isBlank(request.getOpContact()))) {
            return "操作动作为" + request.getDealAction() + "时，联系人信息必填";
        }

        return null;
    }

    /**
     * 处理派单状态 - 对应原openAccept功能
     */
    private ProgressSyncResponseBody handleDispatch(ProgressSyncRequest request, String orderNo) {
        log.info("处理派单状态，订单号：{}", orderNo);

        // 查找订单
        AfterMarketOrder2cInfo afterMarketOrder = findAfterMarketOrder(orderNo);
        if (afterMarketOrder == null) {
            return ProgressSyncResponseBody.error("订单不存在");
        }

        // 执行派单逻辑 - 对应原afterMarketOrderDispatch方法
        String serviceOrderId = afterMarketOrder.getServiceOrderId();
        Integer status = afterMarketOrder.getStatus();

        if (status.equals(AfterMarketOrderStatusEnum.DISPATCHING.getStatus()) || status.equals(AfterMarketOrderStatusEnum.DISPATCHED.getStatus())) {
            Date now = new Date();
            afterMarketOrder.setStatus(AfterMarketOrderStatusEnum.DISPATCHED.getStatus()); // 已派单
            afterMarketOrder.setUpdateTime(now);
            afterMarketOrder2cInfoMapper.updateByPrimaryKeySelective(afterMarketOrder);

            // 更新订单商品关系表信息
            AfterMarketOrder2cOfferingInfo afterMarketOrder2cOfferingInfo = new AfterMarketOrder2cOfferingInfo();
            afterMarketOrder2cOfferingInfo.setPresentSendOrderName(request.getOpPerson());
            afterMarketOrder2cOfferingInfo.setPresentSendOrderPhone(request.getOpContact());
            afterMarketOrder2cOfferingInfo.setSendOrderTime(now);
            afterMarketOrder2cOfferingInfo.setUpdateTime(now);

            AfterMarketOrder2cOfferingInfoExample offeringInfoExample = new AfterMarketOrder2cOfferingInfoExample();
            AfterMarketOrder2cOfferingInfoExample.Criteria offeringCriteria = offeringInfoExample.createCriteria();
            offeringCriteria.andServiceOrderIdEqualTo(serviceOrderId);
            afterMarketOrder2cOfferingInfoMapper.updateByExampleSelective(afterMarketOrder2cOfferingInfo, offeringInfoExample);

            // 记录操作历史
            String operateMessage = status.equals(AfterMarketOrderStatusEnum.DISPATCHING.getStatus()) ? "完成派单" : "修改派单";
            operateMessage += "。接单人员姓名" + request.getOpPerson() + "，接单人员电话" + request.getOpContact() + "。";
            recordProgressHistory(afterMarketOrder, request, AfterMarketOrderStatusEnum.DISPATCHED.getStatus(), operateMessage);

            // 同步状态到商城
            try {
                ServiceOrderResultRequest serviceOrderResultRequest = new ServiceOrderResultRequest();
                serviceOrderResultRequest.setOrderId(serviceOrderId);
                serviceOrderResultRequest.setOprType("1");
                ServiceOrderResultRequest.DispatchInfo dispatchInfo = new ServiceOrderResultRequest.DispatchInfo();
                dispatchInfo.setName(request.getOpPerson());
                dispatchInfo.setPhone(request.getOpContact());
                dispatchInfo.setNumber("000000"); // 默认确认编码
                serviceOrderResultRequest.setDispatchInfo(dispatchInfo);
                sendServiceOrderResult( serviceOrderResultRequest);
            } catch (Exception e) {
                log.error("同步派单结果到商城失败：{}", e.getMessage(), e);
            }
        }

        log.info("派单处理成功，订单号：{}", orderNo);
        return ProgressSyncResponseBody.success();
    }

    /**
     * 处理签到状态
     */
    private ProgressSyncResponseBody handleSignIn(ProgressSyncRequest request, String orderNo) {
        log.info("处理签到状态，订单号：{}", orderNo);

        // 查找订单
        AfterMarketOrder2cInfo afterMarketOrder = findAfterMarketOrder(orderNo);
        if (afterMarketOrder == null) {
            return ProgressSyncResponseBody.error("订单不存在");
        }

        // 更新订单状态为已签到
        Date now = new Date();
        afterMarketOrder.setStatus(AfterMarketOrderStatusEnum.SIGN_IN.getStatus()); // 已签到
        afterMarketOrder.setUpdateTime(now);
        afterMarketOrder2cInfoMapper.updateByPrimaryKeySelective(afterMarketOrder);

        // 记录操作历史
        recordProgressHistory(afterMarketOrder, request, AfterMarketOrderStatusEnum.SIGN_IN.getStatus(), "进度同步：已签到");

        log.info("签到处理成功，订单号：{}", orderNo);
        return ProgressSyncResponseBody.success();
    }

    /**
     * 处理完结成功状态 - 对应原openResp成功功能
     */
    private ProgressSyncResponseBody handleCompleteSuccess(ProgressSyncRequest request, String orderNo) {
        log.info("处理完结成功状态，订单号：{}", orderNo);

        // 查找订单
        AfterMarketOrder2cInfo afterMarketOrder = findAfterMarketOrder(orderNo);
        if (afterMarketOrder == null) {
            return ProgressSyncResponseBody.error("订单不存在");
        }

        // 更新订单状态为已完结（成功）
        Date now = new Date();
        afterMarketOrder.setStatus(AfterMarketOrderStatusEnum.DELIVERY_SUCCESS.getStatus()); // 已完结（成功）
        afterMarketOrder.setUpdateTime(now);
        afterMarketOrder2cInfoMapper.updateByPrimaryKeySelective(afterMarketOrder);

        // 记录操作历史
        recordProgressHistory(afterMarketOrder, request, AfterMarketOrderStatusEnum.DELIVERY_SUCCESS.getStatus(), "完成交付。交付结果成功");

        // 同步到商城 - 对应原openResp中的逻辑
        try {
            ServiceOrderResultRequest resultRequest = new ServiceOrderResultRequest();
            resultRequest.setOrderId(afterMarketOrder.getServiceOrderId());
            resultRequest.setOprType("2");
            ServiceOrderResultRequest.DeliverInfo deliverInfo = new ServiceOrderResultRequest.DeliverInfo();
            deliverInfo.setDeliverResult("1"); // 成功
            resultRequest.setDeliverInfo(deliverInfo);
            sendServiceOrderResult(resultRequest);
        } catch (Exception e) {
            log.error("同步完结成功结果到商城失败：{}", e.getMessage(), e);
        }

        log.info("完结成功处理成功，订单号：{}", orderNo);
        return ProgressSyncResponseBody.success();
    }

    /**
     * 处理完结失败状态 - 对应原openResp失败功能
     */
    private ProgressSyncResponseBody handleCompleteFailed(ProgressSyncRequest request, String orderNo) {
        log.info("处理完结失败状态，订单号：{}", orderNo);

        // 查找订单
        AfterMarketOrder2cInfo afterMarketOrder = findAfterMarketOrder(orderNo);
        if (afterMarketOrder == null) {
            return ProgressSyncResponseBody.error("订单不存在");
        }

        // 更新订单状态为已完结（失败）
        Date now = new Date();
        afterMarketOrder.setStatus(AfterMarketOrderStatusEnum.DELIVERY_FAIL.getStatus()); // 已完结（失败）
        afterMarketOrder.setUpdateTime(now);
        afterMarketOrder2cInfoMapper.updateByPrimaryKeySelective(afterMarketOrder);

        // 记录操作历史
        String operateMessage = "完成交付。交付结果失败";
        if (StringUtils.isNotBlank(request.getResultDesc())) {
            operateMessage += "，原因：" + request.getResultDesc();
        }
        recordProgressHistory(afterMarketOrder, request, AfterMarketOrderStatusEnum.DELIVERY_FAIL.getStatus(), operateMessage);

        // 同步到商城 - 对应原openResp中的逻辑
        try {
            ServiceOrderResultRequest resultRequest = new ServiceOrderResultRequest();
            resultRequest.setOrderId(afterMarketOrder.getServiceOrderId());
            resultRequest.setOprType("2");
            ServiceOrderResultRequest.DeliverInfo deliverInfo = new ServiceOrderResultRequest.DeliverInfo();
            deliverInfo.setDeliverResult("2"); // 失败
            deliverInfo.setReason(request.getResultDesc());
            resultRequest.setDeliverInfo(deliverInfo);
            sendServiceOrderResult(resultRequest);
        } catch (Exception e) {
            log.error("同步完结失败结果到商城失败：{}", e.getMessage(), e);
        }

        log.info("完结失败处理成功，订单号：{}", orderNo);
        return ProgressSyncResponseBody.success();
    }

    /**
     * 查找售后订单
     */
    private AfterMarketOrder2cInfo findAfterMarketOrder(String orderNo) {
        try {
            //处理orderNo,去掉jkiot
            if (orderNo.startsWith(ORDER_PREFIX)) {
                orderNo = orderNo.substring(ORDER_PREFIX.length());
            }
            return afterMarketOrder2cInfoMapper.selectByPrimaryKey(orderNo);
        } catch (Exception e) {
            log.error("查询售后订单失败，订单号：{}，错误：{}", orderNo, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 记录进度历史 - 重载方法
     */
    private void recordProgressHistory(AfterMarketOrder2cInfo afterMarketOrder, ProgressSyncRequest request, Integer status, String operateMessage) {
        try {
            AftermarketOrderHistory history = new AftermarketOrderHistory();
            history.setId(BaseServiceUtils.getId());
            history.setServiceOrderId(afterMarketOrder.getServiceOrderId());
            history.setOperateType(AfterMarketOrderResultConstant.AFTER_MARKET_ORDER_HISTORY_OPERATE_TYPE_); // 系统操作
            history.setInnerStatus(status);
            history.setOperateMessage(operateMessage);
            history.setCreateTime(new Date());
            history.setUpdateTime(new Date());

            aftermarketOrderHistoryMapper.insertSelective(history);
            log.info("记录进度历史成功，订单号：{}，状态：{}，消息：{}", afterMarketOrder.getOfferingOrderId(), status, operateMessage);
        } catch (Exception e) {
            log.error("记录进度历史失败：{}", e.getMessage(), e);
        }
    }

    /**
     * 映射操作动作到订单状态
     */
    private Integer mapDealActionToOrderStatus(String dealAction) {
        switch (dealAction) {
            case "C01": // 待派单
                return 2; // 待派单
            case "C02": // 已驳回
                return 1; // 待预约
            case "C03": // 已派单
                return 3; // 已派单
            case "C04": // 已签到
                return 31; // 已签到
            case "C05": // 已完结（成功）
                return 4; // 已完结（成功）
            case "C06": // 已完结（失败）
                return 5; // 已完结（失败）
            default:
                return null;
        }
    }
    /**
     * 根据服务订单ID获取省份装维平台代码
     *
     * @param serviceOrderId 服务订单ID
     * @return 省份装维平台代码，如：henan、shandong
     */
    private String getProvinceInstallPlatform(String serviceOrderId) {
        try {
            AfterMarketOrder2cOfferingInfoExample example = new AfterMarketOrder2cOfferingInfoExample();
            AfterMarketOrder2cOfferingInfoExample.Criteria criteria = example.createCriteria();
            criteria.andServiceOrderIdEqualTo(serviceOrderId);

            List<AfterMarketOrder2cOfferingInfo> offeringInfos = afterMarketOrder2cOfferingInfoMapper.selectByExample(example);
            if (!CollectionUtils.isEmpty(offeringInfos)) {
                AfterMarketOrder2cOfferingInfo offeringInfo = offeringInfos.get(0);
                String provincePlatform = offeringInfo.getProvinceInstallPlatform();
                log.info("订单{}的省份装维平台：{}", serviceOrderId, provincePlatform);
                return provincePlatform;
            } else {
                log.warn("未找到订单{}的商品信息", serviceOrderId);
                return null;
            }
        } catch (Exception e) {
            log.error("查询订单{}的省份装维平台失败：{}", serviceOrderId, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 河南状态映射到省状态
     * 河南:工单状态 0-待派发，1-已派发，2-已受理，3-开通成功，4-开通失败
     * 省:工单状态0-已接单，1-待派发，2-已驳回，3-已派单，4-已签到，5-开通成功，6-开通失败
     *
     * @param henanStatus 河南状态
     * @return 省状态枚举
     */
    private OrderProvinceZwStatusEnum mapHenanStatusToProvinceStatus(Integer henanStatus) {
        OrderProvinceZwStatusEnum provinceStatusEnum = OrderProvinceZwStatusEnum.mapFromHenanStatus(henanStatus);
        if (provinceStatusEnum == null) {
            log.warn("未知的河南状态：{}，无法映射到省状态", henanStatus);
        }
        return provinceStatusEnum;
    }

    /**
     * 检查订单是否已经经过派单状态（C03）
     * 通过查询订单历史记录来判断是否存在已派单状态
     *
     * @param serviceOrderId 服务订单ID
     * @return true-已经经过派单状态，false-未经过派单状态
     */
    private boolean hasDispatchedStatus(String serviceOrderId) {
        try {
            // 构建查询条件：查询指定订单的历史记录，状态为已派单（3）
            AftermarketOrderHistoryExample example = new AftermarketOrderHistoryExample();
            AftermarketOrderHistoryExample.Criteria criteria = example.createCriteria();
            criteria.andServiceOrderIdEqualTo(serviceOrderId)
                    .andOperateTypeEqualTo(AfterMarketOrderResultConstant.AFTER_MARKET_ORDER_HISTORY_OPERATE_TYPE_)
                    .andInnerStatusEqualTo(AfterMarketOrderStatusEnum.DISPATCHED.getStatus());

            // 查询历史记录
            List<AftermarketOrderHistory> historyList = aftermarketOrderHistoryMapper.selectByExample(example);

            // 如果存在已派单的历史记录，说明订单已经经过派单状态
            boolean hasDispatched = !CollectionUtils.isEmpty(historyList);

            log.info("订单{}派单状态检查结果：{}", serviceOrderId, hasDispatched ? "已派单" : "未派单");
            return hasDispatched;

        } catch (Exception e) {
            log.error("检查订单{}派单状态时发生异常：{}", serviceOrderId, e.getMessage(), e);
            // 发生异常时，为了安全起见，返回false，不允许驳回
            return false;
        }
    }


}
