package com.chinamobile.install.service.impl;

import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.exception.ExcelAnalysisException;
import com.chinamobile.install.config.RedisLockConstant;
import com.chinamobile.install.config.SupplierInfoMapConfig;
import com.chinamobile.install.dao.*;
import com.chinamobile.install.dao.ext.AfterMarkOrder2cOfferingInfoMapperExt;
import com.chinamobile.install.dao.ext.AfterMarketOrder2cInfoMapperExt;
import com.chinamobile.install.enums.AfterMarketOrderResultConstant;
import com.chinamobile.install.enums.AfterMarketOrderStatusConstant;
import com.chinamobile.install.enums.AfterMarketOrderStatusEnum;
import com.chinamobile.install.exception.StatusConstant;
import com.chinamobile.install.handler.AfterMarketOrderExcelListener;
import com.chinamobile.install.handler.AfterMarketOrderImportFailed;
import com.chinamobile.install.pojo.entity.*;
import com.chinamobile.install.pojo.param.*;
import com.chinamobile.install.pojo.vo.*;
import com.chinamobile.install.service.IAfterMarketOrderWebService;
import com.chinamobile.install.service.Order2cAttachmentService;
import com.chinamobile.install.service.OrderHenanZwService;
import com.chinamobile.install.util.IotLogUtil;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.BaseConstant;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.entity.Msg4Request;
import com.chinamobile.iot.sc.entity.UpResult;
import com.chinamobile.iot.sc.entity.iot.ServiceOrderResultRequest;
import com.chinamobile.iot.sc.enums.log.LogResultEnum;
import com.chinamobile.iot.sc.enums.log.ModuleEnum;
import com.chinamobile.iot.sc.enums.log.OrderManageOperateEnum;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.feign.SmsFeignClient;
import com.chinamobile.iot.sc.feign.UserFeignClient;
import com.chinamobile.iot.sc.mode.Data4User;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.service.LogService;
import com.chinamobile.iot.sc.util.DateTimeUtil;
import com.chinamobile.iot.sc.util.DateUtils;
import com.chinamobile.iot.sc.util.ExcelUtils;
import com.chinamobile.iot.sc.util.IOTEncodeUtils;
import com.chinamobile.iot.sc.util.excel.EasyExcelUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 *         售后订单service
 */
@Service
@Slf4j
public class AfterMarketOrderWebServiceImpl implements IAfterMarketOrderWebService {

    @Value("${sms.AfterMarketOrderAppointmentTemplateId:106381}")
    private String afterMarketOrderAppointmentTemplateId;

    @Value("${sms.AfterMarketOrderDispatchTemplateId:106382}")
    private String afterMarketOrderDispatchTemplateId;

    @Value("${sms.AfterMarketOrderTerminationTemplateId:106383}")
    private String afterMarketOrderTerminationTemplateId;

    @Value("${h5.loginAddress:http://**********:31330/iotmall/orderProcess/login}")
    private String h5LoginAddress;
    @Resource
    private Order2cDistributorInfoMapper order2cDistributorInfoMapper;
    @Resource
    private Order2cAgentInfoMapper order2cAgentInfoMapper;
    @Resource
    private AfterMarketOrder2cOfferingInfoMapper afterMarketOrder2cOfferingInfoMapper;

    @Resource
    private AfterMarketOrder2cInfoMapper afterMarketOrder2cInfoMapper;

    @Resource
    private AftermarketOrderHistoryMapper aftermarketOrderHistoryMapper;

    @Resource
    private AfterMarketOrder2cInfoMapperExt afterMarketOrder2cInfoMapperExt;

    @Resource
    private OrderHenanZwService orderHenanZwService;

    @Autowired
    private SmsFeignClient smsFeignClient;

    @Resource
    private RedisTemplate redisTemplate;

    @Resource
    private UserFeignClient userFeignClient;

    @Resource
    private AfterMarkOrder2cOfferingInfoMapperExt afterMarkOrder2cOfferingInfoMapperExt;

    @Resource
    private Order2cInfoMapper order2cInfoMapper;

    @Resource
    private Order2cAtomInfoMapper order2cAtomInfoMapper;

    @Resource
    private AftermarketOrderRocInfoMapper aftermarketOrderRocInfoMapper;

    @Resource
    private SpuOfferingInfoHistoryMapper spuOfferingInfoHistoryMapper;
    @Resource
    private AtomOfferingInfoMapper atomOfferingInfoMapper;
    @Resource
    private SkuOfferingInfoMapper skuOfferingInfoMapper;
    @Resource
    private SpuOfferingInfoMapper spuOfferingInfoMapper;
    @Resource
    private SkuOfferingInfoHistoryMapper skuOfferingInfoHistoryMapper;
    @Resource
    private AtomOfferingInfoHistoryMapper atomOfferingInfoHistoryMapper;
    @Resource
    private UserMapper userMapper;

    @Resource
    private UserPartnerMapper userPartnerMapper;
    @Resource
    private LogService logService;

    @Value("${install.encodeKey}")
    private String encodeKey;
    @Value("${install.sm4Key}")
    private String iotSm4Key;

    @Value("${install.sm4Iv}")
    private String iotSm4Iv;

    @Resource
    private Order2cAttachmentService order2cAttachmentService;

    private ExecutorService executorService = new ThreadPoolExecutor(50, 50, 1L, TimeUnit.MINUTES,
            new LinkedBlockingDeque<>(10000));
    @Resource
    private AftermarketOrderDeliveryAttachmentsMapper aftermarketOrderDeliveryAttachmentsMapper;

    @Resource
    private AftermarketOfferingInfoMapper aftermarketOfferingInfoMapper;
    @Resource
    private AftermarketOfferingCodeMapper aftermarketOfferingCodeMapper;

    @Resource
    private RoleInfoMapper roleInfoMapper;
    @Resource
    private InstallManagerAndPartnerRelationMapper installManagerAndPartnerRelationMapper;
    private MallAddress2ProvinceAddressInfoMapper mallAddress2ProvinceAddressInfoMapper;
    @Resource
    private AftermarketOrderSignInAttachmentsMapper aftermarketOrderSignInAttachmentsMapper;

    @Resource
    private LogisticsInfoMapper logisticsInfoMapper;

    @Resource
    private SupplierInfoMapConfig supplierInfoMapConfig;



    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer afterMarketOrderDispatch(OrderDispatchParam orderDispatchParam, LoginIfo4Redis loginIfo4Redis,
            String ip) {
        Integer code = (int) ((Math.random() * 9 + 1) * 100000);
        String serviceOrderId = orderDispatchParam.getServiceOrderId();
        // 查询当前订单信息
        AfterMarketOrder2cInfo afterMarketOrder2cInfo = afterMarketOrder2cInfoMapper.selectByPrimaryKey(serviceOrderId);
        AfterMarketOrder2cOfferingInfo afterMarketOrder2cOfferingInfo = afterMarketOrder2cOfferingInfoMapper
                .selectByPrimaryKey(orderDispatchParam.getOrderOfferingRelationId());
        Integer status = afterMarketOrder2cInfo.getStatus();
        String presentSendOrderPhone = afterMarketOrder2cOfferingInfo.getPresentSendOrderPhone();
        String afterMarketName = afterMarketOrder2cOfferingInfo.getAfterMarketName();
        Integer orderTakeType = afterMarketOrder2cOfferingInfo.getOrderTakeType();
        StringBuilder content = new StringBuilder();
        if (AfterMarketOrderStatusConstant.DISPATCHING_STATUS.equals(status)) {

            content.append(IotLogUtil.afterMarketOrderDispatchFromParam(orderDispatchParam, "0"));
        } else if (AfterMarketOrderStatusConstant.DISPATCHED_STATUS.equals(status)) {
            content.append(IotLogUtil.afterMarketOrderDispatchFromParam(orderDispatchParam, "1"));
        } else {
            content.append("-");
        }
        // 重新派单,获取之前code
        if (AfterMarketOrderStatusConstant.DISPATCHED_STATUS.equals(status)
                && StringUtils.isNotEmpty(presentSendOrderPhone)) {
            String codeStr = (String) redisTemplate.opsForValue()
                    .get(RedisLockConstant.AFTER_ORDER_DISPATCH_INSTALL_REDIS_KEY.concat(serviceOrderId)
                            .concat(presentSendOrderPhone));
            if (StringUtils.isNotEmpty(codeStr)) {
                code = Integer.parseInt(codeStr);
            } else {
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.ORDER_MANAGE.code,
                            OrderManageOperateEnum.AFTER_SALE_ORDER.code,
                            content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code,
                            StatusConstant.NOT_ORIGINAL_DELIVERY_CODE.getMessage());
                });
                throw new BusinessException(StatusConstant.NOT_ORIGINAL_DELIVERY_CODE);
            }
            log.info("开始重新派单派单订单号：{}，确认码：{}", serviceOrderId, code);
        } else {
            log.info("开始第一次派单订单号：{}，确认码：{}", serviceOrderId, code);
        }
        // 查询订单是否包含多个服务商品，多个提示错误
        AfterMarketOrder2cOfferingInfoExample order2cOfferingInfoExample = new AfterMarketOrder2cOfferingInfoExample();
        AfterMarketOrder2cOfferingInfoExample.Criteria criteria = order2cOfferingInfoExample.createCriteria();
        criteria.andServiceOrderIdEqualTo(serviceOrderId);
        List<AfterMarketOrder2cOfferingInfo> afterMarketOrder2cOfferingInfos = afterMarketOrder2cOfferingInfoMapper
                .selectByExample(order2cOfferingInfoExample);
        if (CollectionUtils.isNotEmpty(afterMarketOrder2cOfferingInfos) && afterMarketOrder2cOfferingInfos.size() > 1) {
            String errMessage = "订单%s存在多个售后商品，无法派单，请联系管理员修改商品配置！";
            executorService.execute(() -> {
                logService.recordOperateLogAsync(ModuleEnum.ORDER_MANAGE.code,
                        OrderManageOperateEnum.AFTER_SALE_ORDER.code,
                        content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code,
                        String.format(errMessage, serviceOrderId));
            });
            throw new BusinessException("10030", String.format(errMessage, serviceOrderId));
        }

        if (status.equals(AfterMarketOrderStatusConstant.DISPATCHING_STATUS)
                || status.equals(AfterMarketOrderStatusConstant.DISPATCHED_STATUS)) {
            // 进行派单，发送短信通知客户及装维人员
            if (orderTakeType == null) {
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.ORDER_MANAGE.code,
                            OrderManageOperateEnum.AFTER_SALE_ORDER.code,
                            content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code,
                            StatusConstant.ORDER_NOT_SEND_ORDERS.getMessage());
                });
                throw new BusinessException(StatusConstant.ORDER_NOT_SEND_ORDERS);
            }
            // 判断接单方式
            if (AfterMarketOrderResultConstant.AFTER_MARKET_ORDER_ORDER_TAKE_TYPE_PROVINCE.equals(orderTakeType)) {
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.ORDER_MANAGE.code,
                            OrderManageOperateEnum.AFTER_SALE_ORDER.code,
                            content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code,
                            StatusConstant.ORDER_BELONG_TO_PROVINCE_WITHIN.getMessage());
                });
                throw new BusinessException(StatusConstant.ORDER_BELONG_TO_PROVINCE_WITHIN);
            }
            // 重新派单人员不能和之前一样
            if (AfterMarketOrderStatusConstant.DISPATCHED_STATUS.equals(status)) {
                String userId = afterMarketOrder2cOfferingInfo.getUserId();
                if (userId.equals(orderDispatchParam.getInstallUserId())) {
                    executorService.execute(() -> {
                        logService.recordOperateLogAsync(ModuleEnum.ORDER_MANAGE.code,
                                OrderManageOperateEnum.AFTER_SALE_ORDER.code,
                                content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code,
                                StatusConstant.NEW_INSTALL_USER_NOT_BEFORE_EQUALLY.getMessage());
                    });
                    throw new BusinessException(StatusConstant.NEW_INSTALL_USER_NOT_BEFORE_EQUALLY);
                }
            }
            // 派遣成功修改订单状态及同步状态到商城
            Date date = new Date();
            afterMarketOrder2cInfo.setStatus(AfterMarketOrderStatusConstant.DISPATCHED_STATUS);
            afterMarketOrder2cInfo.setUpdateTime(date);
            afterMarketOrder2cInfo.setStatusTime(date);
            afterMarketOrder2cInfoMapper.updateByPrimaryKeySelective(afterMarketOrder2cInfo);
            // 更新订单商品关系表信息
            afterMarketOrder2cOfferingInfo.setPresentSendOrderName(orderDispatchParam.getInstallUserName());
            afterMarketOrder2cOfferingInfo.setPresentSendOrderPhone(orderDispatchParam.getInstallUserPhone());
            afterMarketOrder2cOfferingInfo.setSendOrderCompany(orderDispatchParam.getInstallPartnerName());
            afterMarketOrder2cOfferingInfo.setUserId(orderDispatchParam.getInstallUserId());
            afterMarketOrder2cOfferingInfo.setSendOrderTime(date);
            afterMarketOrder2cOfferingInfo.setUpdateTime(date);
            afterMarketOrder2cOfferingInfoMapper.updateByPrimaryKeySelective(afterMarketOrder2cOfferingInfo);
            // 修改状态记录历史
            AftermarketOrderHistory aftermarketOrderHistory = new AftermarketOrderHistory();
            aftermarketOrderHistory.setId(BaseServiceUtils.getId());
            aftermarketOrderHistory.setServiceOrderId(serviceOrderId);
            aftermarketOrderHistory.setOperatorId(loginIfo4Redis.getUserId());
            aftermarketOrderHistory
                    .setOperateType(AfterMarketOrderResultConstant.AFTER_MARKET_ORDER_HISTORY_OPERATE_TYPE_);
            aftermarketOrderHistory.setInnerStatus(AfterMarketOrderStatusConstant.DISPATCHED_STATUS);
            aftermarketOrderHistory.setCreateTime(date);
            aftermarketOrderHistory.setUpdateTime(date);
            if (AfterMarketOrderStatusConstant.DISPATCHING_STATUS.equals(status)) {
                aftermarketOrderHistory.setOperateMessage(
                        "完成派单。接单人员姓名".concat(orderDispatchParam.getInstallUserName()).concat(",").concat("接单人员电话")
                                .concat(orderDispatchParam.getInstallUserPhone()).concat("。"));
            } else if (AfterMarketOrderStatusConstant.DISPATCHED_STATUS.equals(status)) {
                aftermarketOrderHistory.setOperateMessage(
                        "修改派单。接单人员姓名".concat(orderDispatchParam.getInstallUserName()).concat(",").concat("接单人员电话")
                                .concat(orderDispatchParam.getInstallUserPhone()).concat("。"));
            }
            aftermarketOrderHistoryMapper.insertSelective(aftermarketOrderHistory);
            // 同步状态到商城，排除导入订单
            if(!afterMarketOrder2cInfo.getIsInner()){
                ServiceOrderResultRequest serviceOrderResultRequest = new ServiceOrderResultRequest();
                serviceOrderResultRequest.setOrderId(serviceOrderId);
                if (AfterMarketOrderStatusConstant.DISPATCHING_STATUS.equals(status)) {
                    serviceOrderResultRequest.setOprType(AfterMarketOrderResultConstant.AFTER_MARKET_ORDER_DISPATCH);
                } else if (AfterMarketOrderStatusConstant.DISPATCHED_STATUS.equals(status)) {
                    serviceOrderResultRequest.setOprType(AfterMarketOrderResultConstant.AFTER_MARKET_ORDER_UPDATE);
                }
                ServiceOrderResultRequest.DispatchInfo dispatchInfo = new ServiceOrderResultRequest.DispatchInfo();
                dispatchInfo.setName(orderDispatchParam.getInstallUserName());
                dispatchInfo.setPhone(orderDispatchParam.getInstallUserPhone());
                dispatchInfo.setNumber(String.valueOf(code));
                serviceOrderResultRequest.setDispatchInfo(dispatchInfo);
                orderHenanZwService.sendServiceOrderResult(serviceOrderResultRequest);
            }

            // 预约人短信信息
            List<String> appointmentMobiles = new ArrayList<>();
            appointmentMobiles.add(afterMarketOrder2cInfo.getAppointmentPhone());
            Map<String, String> appointmentMessage = new HashMap<>();
            appointmentMessage.put("afterMarketCommodityName", afterMarketName);
            appointmentMessage.put("installUserName", orderDispatchParam.getInstallUserName());
            appointmentMessage.put("installUserPhone", orderDispatchParam.getInstallUserPhone());
            appointmentMessage.put("serviceConfirmationCode", String.valueOf(code));
            // 装维人员短信信息
            List<String> dispatchMobiles = new ArrayList<>();
            dispatchMobiles.add(orderDispatchParam.getInstallUserPhone());
            Map<String, String> dispatchMessage = new HashMap<>();
            dispatchMessage.put("serviceOrderId", afterMarketOrder2cInfo.getServiceOrderId());
            dispatchMessage.put("h5Link", h5LoginAddress);
            // 发短信给预约人
            sendSmsDispatch(appointmentMobiles, afterMarketOrderAppointmentTemplateId, appointmentMessage);
            // 发送短信给装维人员
            sendSmsDispatch(dispatchMobiles, afterMarketOrderDispatchTemplateId, dispatchMessage);
            // 如果是已预约状态，就是重新派单，及发短信通知之前的人员
            if (AfterMarketOrderStatusConstant.DISPATCHED_STATUS.equals(status)) {
                List<String> oldDispatchMobiles = new ArrayList<>();
                appointmentMobiles.add(afterMarketOrder2cOfferingInfo.getPresentSendOrderPhone());
                Map<String, String> oldDispatchMessage = new HashMap<>();
                oldDispatchMessage.put("serviceOrderId", serviceOrderId);
                // 重新派单，发送给之前派遣人员终止任务
                sendSmsDispatch(oldDispatchMobiles, afterMarketOrderTerminationTemplateId, oldDispatchMessage);
            }
            // 保存确认码 如果为重新派单，沿用之前交付码（code）
            redisTemplate.opsForValue().set(RedisLockConstant.AFTER_ORDER_DISPATCH_INSTALL_REDIS_KEY
                    .concat(serviceOrderId).concat(orderDispatchParam.getInstallUserPhone()), String.valueOf(code));
        } else {
            executorService.execute(() -> {
                logService.recordOperateLogAsync(ModuleEnum.ORDER_MANAGE.code,
                        OrderManageOperateEnum.AFTER_SALE_ORDER.code,
                        content.toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code,
                        StatusConstant.AFTER_MARKET_ORDER_2C_NOT_DISPATCH.getMessage());
            });
            throw new BusinessException(StatusConstant.AFTER_MARKET_ORDER_2C_NOT_DISPATCH);
        }

        // 记录日志
        logService.recordOperateLog(ModuleEnum.ORDER_MANAGE.code,
                OrderManageOperateEnum.AFTER_SALE_ORDER.code,
                content.toString(), LogResultEnum.LOG_SUCESS.code, null);
        // if (DISPATCHING_STATUS.equals(status)) {
        // logService.recordOperateLog(ModuleEnum.ORDER_MANAGE.code,
        // OrderManageOperateEnum.AFTER_SALE_ORDER.code,
        // IotLogUtil.afterMarketOrderDispatchFromParam(orderDispatchParam, "0"),
        // LogResultEnum.LOG_SUCESS.code, null);
        // } else if (DISPATCHED_STATUS.equals(status)) {
        // logService.recordOperateLog(ModuleEnum.ORDER_MANAGE.code,
        // OrderManageOperateEnum.AFTER_SALE_ORDER.code,
        // IotLogUtil.afterMarketOrderDispatchFromParam(orderDispatchParam,
        // "1"),LogResultEnum.LOG_SUCESS.code, null);
        // }

        return new BaseAnswer();
    }

    @Override
    public Long getAfterMarketOrderByInstallUserId(String installUserId) {
        UserPartner userPartner = userPartnerMapper.selectByPrimaryKey(installUserId);
        RoleInfo roleInfo = roleInfoMapper.selectByPrimaryKey(userPartner.getRoleId());
        String roleType = roleInfo.getRoleType();
        // 装维管理员
        if(roleType.equals(BaseConstant.PARTNER_INSTALL_MANAGER_ROLE)){
            List<AfterMarketOrder2cOfferingInfo> afterMarketOrder2cOfferingInfoList =
                    afterMarketOrder2cOfferingInfoMapper.selectByExample(
                            new AfterMarketOrder2cOfferingInfoExample()
                                    .createCriteria()
                                    .andInstallManagerIdEqualTo(installUserId)
                                    .example()
                    );
            if(afterMarketOrder2cOfferingInfoList.size() > 0){
                return (long) afterMarketOrder2cOfferingInfoList.size();
            }
            return afterMarketOrder2cInfoMapperExt.countAfterMarketOrderByInstallUserIdManager(installUserId);
        }
        // 装维主
        else if (roleType.equals(BaseConstant.PARTNER_INSTALL_LORD_ROLE)){
            List<AfterMarketOrder2cOfferingInfo> afterMarketOrder2cOfferingInfoList =
                    afterMarketOrder2cOfferingInfoMapper.selectByExample(
                            new AfterMarketOrder2cOfferingInfoExample()
                                    .createCriteria()
                                    .andAdminCooperatorIdEqualTo(installUserId)
                                    .example()
                    );
            if(afterMarketOrder2cOfferingInfoList.size() > 0){
                return (long) afterMarketOrder2cOfferingInfoList.size();
            }
            return afterMarketOrder2cInfoMapperExt.countAfterMarketOrderByInstallUserIdAdmin(installUserId);
        }
        //装维从
        else if (roleType.equals(BaseConstant.PARTNER_INSTALL_SUB_ROLE)){
            //查询对应的从账号对应的主账号的信息
            //听说用户相关的代码要后面在上先用笨方法来
            List<UserPartner> userPartner1 = userPartnerMapper.selectByExample(
                    new UserPartnerExample().createCriteria()
                            .andRoleIdEqualTo("1376576697935507457")
                            .andPartnerNameEqualTo(userPartner.getPartnerName())
                            .andIsLogoffEqualTo(false)
                            .example()
            );
            List<AfterMarketOrder2cOfferingInfo> afterMarketOrder2cOfferingInfoList =
                    afterMarketOrder2cOfferingInfoMapper.selectByExample(
                            new AfterMarketOrder2cOfferingInfoExample()
                                    .createCriteria()
                                    .andAdminCooperatorIdEqualTo(userPartner1.get(0).getUserId())
                                    .example()
                    );
            if(afterMarketOrder2cOfferingInfoList.size() > 0){
                return (long) afterMarketOrder2cOfferingInfoList.size();
            }
            return afterMarketOrder2cInfoMapperExt.countAfterMarketOrderByInstallUserIdAdmin(userPartner1.get(0).getUserId());
        }else if (roleType.equals(BaseConstant.PARTNER_INSTALL_ROLE)){
            return afterMarketOrder2cInfoMapperExt.countAfterMarketOrderByInstallUserId(installUserId);
        }
        return 0l;
    }

    /**
     * 发送短信
     *
     * @param mobiles
     * @param templateId
     * @param messageMap
     */
    public void sendSmsDispatch(List<String> mobiles, String templateId, Map<String, String> messageMap) {
        Msg4Request msg4Request = new Msg4Request();
        List<String> phones = mobiles.stream().distinct().collect(Collectors.toList());
        msg4Request.setMobiles(phones);
        msg4Request.setMessage(messageMap);
        msg4Request.setTemplateId(templateId);
        smsFeignClient.asySendMessage(msg4Request);
    }

    /**
     * 获取时候订单列表
     */
    // 数据权限-售后服务订单
    @Override
    public BaseAnswer<PageData<AfterMarketOrderItemVO>> getAfterMarketOrderList(AfterMarketOrderQueryParam param,
            LoginIfo4Redis loginIfo4Redis) {
        if (param.getAppointmentTime() != null) {
            param.setAppointmentTimeDate(new Date(param.getAppointmentTime()));
        }

        boolean forPartner = false;
        List<String> dataPermissionCodes = (List<String>) redisTemplate.opsForValue()
                .get(Constant.REDIS_KEY_ROLE_DATA_PERMISSION_CODE + loginIfo4Redis.getRoleId());
        log.info("dataPermissionCodes:{}",dataPermissionCodes);
        if (ObjectUtils.isEmpty(dataPermissionCodes)
                || (!dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_AFTER_MARKET_SYSTEM)
                        && !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_AFTER_MARKET_COMPANY)
                        && !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_AFTER_MARKET_PERSONAL))) {
            throw new BusinessException(StatusConstant.UN_PERMISSION);
        }
        // 商品名称搜索支持反斜杠适配
        if (param.getSkuOfferingName() != null) {
            param.setSkuOfferingName(param.getSkuOfferingName().replaceAll("\\\\", "\\\\\\\\"));
        }
        if (param.getServiceName() != null) {
            param.setServiceName(param.getServiceName().replaceAll("\\\\", "\\\\\\\\"));
        }

        String roleType = loginIfo4Redis.getRoleType();
        String userId = loginIfo4Redis.getUserId();

        //产品现在没有设置新增的三个管理员的数据权限
//        if (dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_AFTER_MARKET_COMPANY)) {
//            // 从伙伴，查找对应主伙伴的订单
//            BaseAnswer<Data4User> userBaseAnswer = userFeignClient.queryPrimaryUserPhone(userId);
//            if (userBaseAnswer == null
//                    || !userBaseAnswer.getStateCode().equals(ExcepStatus.getSuccInstance().getStateCode())
//                    || userBaseAnswer.getData() == null) {
//                log.error("调用获取主用户信息失败。从合作伙伴ID:{}", userId);
//                throw new BusinessException(StatusConstant.INTERNAL_ERROR, "调用获取主用户信息失败。从合作伙伴ID:" + userId);
//            }
//            param.setAdminCooperatorId(userBaseAnswer.getData().getUserId());
//            forPartner = true;
//        } else if (dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_AFTER_MARKET_PERSONAL)) {
//            if (roleType.equals(BaseConstant.PARTNER_PROVINCE)) {
//                // 省管配置权限为主合作伙伴权限
//                BaseAnswer<Data4User> data4UserBaseAnswer = userFeignClient.userInfoById(userId);
//                if (data4UserBaseAnswer == null || !SUCCESS.getStateCode().equals(data4UserBaseAnswer.getStateCode())) {
//                    throw new BusinessException("10004", "合作伙伴省管账号错误");
//                }
//
//                Data4User data4User = data4UserBaseAnswer.getData();
//                String companyType = data4User.getCompanyType();
//                boolean isProvinceUser = org.apache.commons.lang3.StringUtils.isNotEmpty(companyType)
//                        && "2".equals(companyType);
//                String userLocation = data4User.getLocationIdPartner();
//                if (isProvinceUser) {
//                    BaseAnswer<Data4User> userPartner = userFeignClient
//                            .getUserPartnerPrimaryByPartnerName(data4User.getPartnerName());
//                    if (userPartner == null || !SUCCESS.getStateCode().equals(userPartner.getStateCode())) {
//                        throw new BusinessException("10004", "合作伙伴省管获取主合作伙伴账号错误");
//                    }
//                    Data4User userPartnerData = userPartner.getData();
//                    if (Optional.ofNullable(userPartnerData).isPresent()) {
//                        param.setAdminCooperatorId(userPartnerData.getUserId());
//                    }
//                    if ("all".equals(userLocation)) {
//                        param.setBeId(data4User.getBeIdPartner());
//                    } else {
//                        param.setLocation(userLocation);
//                    }
//                }
//            } else {
//                // 主伙伴，查找自己的
//                param.setAdminCooperatorId(userId);
//            }
//            forPartner = true;
//        }

        // 装维管理员
        if(roleType.equals(BaseConstant.PARTNER_INSTALL_MANAGER_ROLE)){
            forPartner = false;
            param.setInstallManagerId(userId);
        }
        // 装维主
        else if (roleType.equals(BaseConstant.PARTNER_INSTALL_LORD_ROLE)){
            forPartner = true;
            param.setAdminCooperatorId(userId);
        }
        //装维从
        else if (roleType.equals(BaseConstant.PARTNER_INSTALL_SUB_ROLE)){
            forPartner = true;
            //查询对应的从账号对应的主账号的信息
            //听说用户相关的代码要后面在上先用笨方法来
            UserPartner userPartner =  userPartnerMapper.selectByPrimaryKey(userId);

            List<UserPartner> userPartner1 = userPartnerMapper.selectByExample(
                    new UserPartnerExample().createCriteria()
                            .andRoleIdEqualTo("1376576697935507457")
                            .andPartnerNameEqualTo(userPartner.getPartnerName())
                            .andIsLogoffEqualTo(false)
                            .example()
            );
            param.setAdminCooperatorId(userPartner1.get(0).getUserId());

        }
        PageHelper.startPage(param.getPageNum(), param.getPageSize());
        List<AfterMarketOrderItemVO> lst = null;
        if (forPartner) {
            lst = afterMarkOrder2cOfferingInfoMapperExt.getAfterMarketOrderByFilterForPartner(param);
        } else {
            lst = afterMarkOrder2cOfferingInfoMapperExt.getAfterMarketOrderByFilterForAdmin(param);
        }

        // 计算停留时长
        Date now = new Date();
        for (AfterMarketOrderItemVO item : lst) {
            if (item.getStatusTime() != null
                    && item.getStatus() != AfterMarketOrderStatusEnum.DELIVERY_SUCCESS.getStatus()
                    && item.getStatus() != AfterMarketOrderStatusEnum.DELIVERY_FAIL.getStatus()) {
                long durationMillis = now.getTime() - item.getStatusTime().getTime();
                long durationHours = durationMillis / (1000 * 60 * 60); // 转换为小时
                item.setDuration(durationHours + "h");
            }

            // 解密地址字段
            if (StringUtils.isNotEmpty(item.getAddr1())) {
                item.setAddr1(IOTEncodeUtils.decryptSM4(item.getAddr1(), iotSm4Key, iotSm4Iv));
            }
            if (StringUtils.isNotEmpty(item.getAddr2())) {
                item.setAddr2(IOTEncodeUtils.decryptSM4(item.getAddr2(), iotSm4Key, iotSm4Iv));
            }
            if (StringUtils.isNotEmpty(item.getAddr3())) {
                item.setAddr3(IOTEncodeUtils.decryptSM4(item.getAddr3(), iotSm4Key, iotSm4Iv));
            }
            if (StringUtils.isNotEmpty(item.getUsaddr())) {
                item.setUsaddr(IOTEncodeUtils.decryptSM4(item.getUsaddr(), iotSm4Key, iotSm4Iv));
            }
        }

        PageInfo<AfterMarketOrderItemVO> pageInfo = new PageInfo<>(lst);
        PageData<AfterMarketOrderItemVO> pageData = new PageData<>();
        pageData.setPage(param.getPageNum());
        pageData.setData(pageInfo.getList());
        pageData.setCount(pageInfo.getTotal());
        return BaseAnswer.success(pageData);
    }

    @Override
    public BaseAnswer<AfterMarketOrderDetailVO> getAfterMarketOrderDetail(AfterMarketOrderDetailParam afterMarketOrderDetailParam,
            LoginIfo4Redis loginIfo4Redis) {
        String userId = loginIfo4Redis.getUserId();
        String serviceOrderId = afterMarketOrderDetailParam.getServiceOrderId();


        AfterMarketOrder2cInfo afterMarketOrder2cInfo = afterMarketOrder2cInfoMapper.selectByPrimaryKey(serviceOrderId);
        String content = "【查看订单详情】\n"
                .concat("售后服务订单号").concat(serviceOrderId);
        if (afterMarketOrder2cInfo == null) {
            logService.recordOperateLog(ModuleEnum.ORDER_MANAGE.code,
                    OrderManageOperateEnum.AFTER_SALE_ORDER.code,
                    content, LogResultEnum.LOG_FAIL.code, "订单：" + serviceOrderId + "不存在");
            throw new BusinessException(StatusConstant.INTERNAL_ERROR, "订单：" + serviceOrderId + "不存在");
        }

        /*Integer contactDesensitizationStatus = afterMarketOrderDetailParam.getContactDesensitizationStatus();

        // 金额是否设置为星号
        boolean isAmountSetAsterisk = false;
        // 判断是否是合作伙伴
        boolean isCooperator = false;
        // 判断合作伙伴是否是外部人员
        BaseAnswer<Data4User> cooperatorUserBaseAnswer = userFeignClient.partnerInfoById(userId);
        if (cooperatorUserBaseAnswer == null
                || !cooperatorUserBaseAnswer.getStateCode().equals(ExcepStatus.getSuccInstance().getStateCode())) {
            throw new BusinessException("10004","获取合作伙伴数据异常");
        }

        // 判断是否为外部人员
        boolean isExternal = loginIfo4Redis.getIsExternal()==null?false:loginIfo4Redis.getIsExternal();*/


        AfterMarketOrderDetailVO vo = new AfterMarketOrderDetailVO();
        BeanUtils.copyProperties(afterMarketOrder2cInfo, vo);
        // 设置附件
        if(StringUtils.isNotEmpty(vo.getOfferingOrderId())){
            vo.setAttachmentList(order2cAttachmentService.findOrderAttachment(vo.getOfferingOrderId()));
        }

        AfterMarketOrder2cOfferingInfoExample offeringInfoExample = new AfterMarketOrder2cOfferingInfoExample();
        AfterMarketOrder2cOfferingInfoExample.Criteria offeringInfoCriteria = offeringInfoExample.createCriteria();
        offeringInfoCriteria.andServiceOrderIdEqualTo(serviceOrderId);
        List<AfterMarketOrder2cOfferingInfo> order2cOfferingInfoList = afterMarketOrder2cOfferingInfoMapper
                .selectByExample(offeringInfoExample);
        if (CollectionUtils.isNotEmpty(order2cOfferingInfoList)) {
            List<AfterMarketOrderOfferingDetailVO> offeringDetailVOList;
            offeringDetailVOList = order2cOfferingInfoList.stream().map(item -> {
                AfterMarketOrderOfferingDetailVO offeringDetailVO = new AfterMarketOrderOfferingDetailVO();
                BeanUtils.copyProperties(item, offeringDetailVO);
                if (StringUtils.isNotEmpty(offeringDetailVO.getAdminCooperatorId())) {
                    BaseAnswer<Data4User> data4UserBaseAnswer = userFeignClient
                            .userInfoById(offeringDetailVO.getAdminCooperatorId());
                    if (data4UserBaseAnswer != null && data4UserBaseAnswer.getData() != null) {
                        offeringDetailVO.setAdminCooperatorName(data4UserBaseAnswer.getData().getPartnerName());
                        offeringDetailVO.setAdminCooperatorUserName(data4UserBaseAnswer.getData().getName());
                        offeringDetailVO.setAdminCooperatorPhone(IOTEncodeUtils.decryptIOTMessage(data4UserBaseAnswer.getData().getPhone(),encodeKey));
                    }
                }
                return offeringDetailVO;
            }).collect(Collectors.toList());
            vo.setOfferingItems(offeringDetailVOList);
        }

        AftermarketOrderHistoryExample historyExample = new AftermarketOrderHistoryExample();
        AftermarketOrderHistoryExample.Criteria historyCriteria = historyExample.createCriteria();
        historyExample.setOrderByClause("create_time ASC");
        historyCriteria.andServiceOrderIdEqualTo(serviceOrderId);
        // 只展示售后订单本身的状态变化历史，过滤退款订单相关的
        historyCriteria.andOperateTypeEqualTo(1);
        List<AftermarketOrderHistory> histories = aftermarketOrderHistoryMapper.selectByExample(historyExample);

        if (CollectionUtils.isNotEmpty(histories)) {
            List<String> historyVOs = histories.stream()
                    // 筛选掉不在前端显示的状态
                    .filter(item -> !item.getInnerStatus()
                            .equals(AfterMarketOrderStatusEnum.ORDER_SYNC_PROVINCE.getStatus()))
                    .map(item -> {
                        StringBuilder result = new StringBuilder();
                        if (item.getInnerStatus().equals(AfterMarketOrderStatusEnum.TO_APPOINTMENT.getStatus())
                                || item.getInnerStatus().equals(AfterMarketOrderStatusEnum.BEFORE_DISPATCHING.getStatus())
                                || item.getInnerStatus()
                                        .equals(AfterMarketOrderStatusEnum.ORDER_CANCELED.getStatus())) {
                            result.append("买家于")
                                    .append(DateUtils.dateToStr(item.getCreateTime(), DateUtils.DATETIME_FORMAT_T))
                                    .append(item.getOperateMessage());
                        } else if (item.getInnerStatus().equals(AfterMarketOrderStatusEnum.DISPATCHED.getStatus())
                                || item.getInnerStatus().equals(AfterMarketOrderStatusEnum.DELIVERY_SUCCESS.getStatus())
                                || item.getInnerStatus().equals(AfterMarketOrderStatusEnum.SIGN_IN.getStatus())
                                || item.getInnerStatus().equals(AfterMarketOrderStatusEnum.DELIVERY_FAIL.getStatus())) {
                            result.append("卖家于")
                                    .append(DateUtils.dateToStr(item.getCreateTime(), DateUtils.DATETIME_FORMAT_T))
                                    .append(item.getOperateMessage());
                        } else if (item.getInnerStatus().equals(AfterMarketOrderStatusEnum.ORDER_FINISH.getStatus())
                                || item.getInnerStatus().equals(AfterMarketOrderStatusEnum.ORDER_FAIL.getStatus())) {
                            result.append("商城于")
                                    .append(DateUtils.dateToStr(item.getCreateTime(), DateUtils.DATETIME_FORMAT_T))
                                    .append(item.getOperateMessage());
                        } else if (item.getInnerStatus().equals(AfterMarketOrderStatusEnum.DISPATCHING.getStatus())){
                            String saleContent = StringUtils.isEmpty(order2cOfferingInfoList.get(0).getInstallManagerId())?
                                    "买家于" : "卖家于";
                            result.append(saleContent)
                                    .append(DateUtils.dateToStr(item.getCreateTime(), DateUtils.DATETIME_FORMAT_T))
                                    .append(item.getOperateMessage());
                        }else {
                            result.append("错误订单状态");
                        }
                        return result.toString();
                    }).collect(Collectors.toList());
            vo.setHistories(historyVOs);
        }

        Order2cInfo order2cInfo = order2cInfoMapper.selectByPrimaryKey(afterMarketOrder2cInfo.getOfferingOrderId());
        if (order2cInfo != null) {
            vo.setOfferingOrderStatus(order2cInfo.getOrderStatus());

            AftermarketOrderRocInfoExample rocInfoExample = new AftermarketOrderRocInfoExample();
            AftermarketOrderRocInfoExample.Criteria rocInfoCriteria = rocInfoExample.createCriteria();
            rocInfoCriteria.andServOrderIdEqualTo(serviceOrderId);
            List<AftermarketOrderRocInfo> rocInfoList = aftermarketOrderRocInfoMapper.selectByExample(rocInfoExample);
            if (CollectionUtils.isNotEmpty(rocInfoList)) {
                vo.setRefundOrderStatus(rocInfoList.get(0).getInnerStatus());
            }

            // 非合作伙伴，还需要返回主订单相关信息
            // 装维管理员返回
//            if (!PARTNER_LORD_ROLE.equals(loginIfo4Redis.getRoleType())
//                    && !PARTNER_ROLE.equals(loginIfo4Redis.getRoleType())
//                    && !loginIfo4Redis.getIsPartner()) {
//              if(BaseConstant.PARTNER_INSTALL_MANAGER_ROLE.equals(loginIfo4Redis.getRoleType())){
                MallOrderInfoVO mallOrderInfoVO = new MallOrderInfoVO();
                mallOrderInfoVO.setOrderId(order2cInfo.getOrderId());
                mallOrderInfoVO.setSpuCode(order2cInfo.getSpuOfferingCode());
                mallOrderInfoVO.setSpuOfferingVersion(order2cInfo.getSpuOfferingVersion());
                mallOrderInfoVO.setSpuOfferingClass(order2cInfo.getSpuOfferingClass());
                mallOrderInfoVO.setReceiverPhone(
                        IOTEncodeUtils.decryptSM4(order2cInfo.getContactPhone(), iotSm4Key, iotSm4Iv));
                mallOrderInfoVO.setReceiverName(
                        IOTEncodeUtils.decryptSM4(order2cInfo.getContactPersonName(), iotSm4Key, iotSm4Iv));
                mallOrderInfoVO.setReceiverAddress(
                        contactAddr(StringUtils.isEmpty(order2cInfo.getAddr1()) ? "" : order2cInfo.getAddr1(),
                                StringUtils.isEmpty(order2cInfo.getAddr2()) ? "" : order2cInfo.getAddr2(),
                                StringUtils.isEmpty(order2cInfo.getAddr3()) ? "" : order2cInfo.getAddr3(),
                                StringUtils.isEmpty(order2cInfo.getAddr4()) ? "" : order2cInfo.getAddr4(),
                                StringUtils.isEmpty(order2cInfo.getUsaddr()) ? "" : order2cInfo.getUsaddr()));
                mallOrderInfoVO
                        .setTotalPrice(IOTEncodeUtils.decryptSM4(order2cInfo.getTotalPrice(), iotSm4Key, iotSm4Iv));

                // 增加版本号相关适配

                // SpuOfferingInfoExample spuOfferingInfoExample = new SpuOfferingInfoExample();
                // SpuOfferingInfoExample.Criteria spuCriteria =
                // spuOfferingInfoExample.createCriteria();
                // spuCriteria.andOfferingCodeEqualTo(order2cInfo.getSpuOfferingCode());
                // List<SpuOfferingInfo> spuOfferingInfos =
                // spuOfferingInfoMapper.selectByExample(spuOfferingInfoExample);

                SpuOfferingInfoHistoryExample spuOfferingInfoHistoryExample = new SpuOfferingInfoHistoryExample();
                SpuOfferingInfoHistoryExample.Criteria spuCriteria = spuOfferingInfoHistoryExample.createCriteria();
                spuCriteria.andOfferingCodeEqualTo(order2cInfo.getSpuOfferingCode());
                spuCriteria.andSpuOfferingVersionEqualTo(order2cInfo.getSpuOfferingVersion());
                List<SpuOfferingInfoHistory> spuOfferingInfos = spuOfferingInfoHistoryMapper
                        .selectByExample(spuOfferingInfoHistoryExample);
                mallOrderInfoVO.setSpuName(spuOfferingInfos.get(0).getOfferingName());
                mallOrderInfoVO.setSpuHeadPicUrl(spuOfferingInfos.get(0).getImgUrl());
                Order2cAtomInfoExample atomInfoExample = new Order2cAtomInfoExample();
                Order2cAtomInfoExample.Criteria atomCriteria = atomInfoExample.createCriteria();
                atomCriteria.andOrderIdEqualTo(order2cInfo.getOrderId());
                List<Order2cAtomInfo> atomInfos = order2cAtomInfoMapper.selectByExample(atomInfoExample);
                if (CollectionUtils.isNotEmpty(atomInfos)) {
                    List<MallAtomOrderInfoVO> atomOrderInfoVOS = atomInfos.stream().map(item -> {
                        MallAtomOrderInfoVO atomOrderInfoVO = new MallAtomOrderInfoVO();
                        atomOrderInfoVO.setAtomName(item.getAtomOfferingName());
                        atomOrderInfoVO.setAtomOfferingClass(item.getAtomOfferingClass());
                        atomOrderInfoVO.setQuantity(item.getAtomQuantity());
                        atomOrderInfoVO.setModel(item.getModel());
                        atomOrderInfoVO.setSkuName(item.getSkuOfferingName());
                        atomOrderInfoVO.setSkuCode(item.getSkuOfferingCode());
                        atomOrderInfoVO.setAtomOfferingVersion(item.getAtomOfferingVersion());
                        atomOrderInfoVO.setSkuOfferingVersion(item.getSkuOfferingVersion());

                        if ("O".equals(item.getAtomOfferingClass())
                                || "D".equals(item.getAtomOfferingClass())
                                || "P".equals(item.getAtomOfferingClass())
                                || "F".equals(item.getAtomOfferingClass())) {
                            atomOrderInfoVO.setCooperatorName(item.getSupplierName());
                        } else {
                            String cooperatorId = item.getCooperatorId();
                            if (StringUtils.isNotEmpty(cooperatorId)) {
                                BaseAnswer<Data4User> data4UserBaseAnswer = userFeignClient.userInfoById(cooperatorId);
                                if (data4UserBaseAnswer != null && data4UserBaseAnswer.getData() != null) {
                                    atomOrderInfoVO.setCooperatorName(data4UserBaseAnswer.getData().getPartnerName());
                                }
                            }
                        }
                        return atomOrderInfoVO;
                    }).collect(Collectors.toList());
                    mallOrderInfoVO.setAtomOrders(atomOrderInfoVOS);
                }

                vo.setOfferingOrderInfo(mallOrderInfoVO);
                // 添加分销员 客户经理 渠道商信息
                List<Order2cDistributorInfo> order2cDistributorInfos = order2cDistributorInfoMapper
                        .selectByExample(new Order2cDistributorInfoExample().createCriteria()
                                .andOrderIdEqualTo(order2cInfo.getOrderId()).example());
                if (CollectionUtils.isNotEmpty(order2cDistributorInfos)) {
                    AfterMarketOrderDetailVO.Distributor distributor = new AfterMarketOrderDetailVO.Distributor();
                    distributor.setDistributorLevel(order2cDistributorInfos.get(0).getDistributorLevel());
                    distributor.setDistributorPhone(order2cDistributorInfos.get(0).getDistributorPhone());
                    distributor.setDistributorShareCode(order2cDistributorInfos.get(0).getDistributorShareCode());
                    vo.setDistributor(distributor);
                }

                List<Order2cAgentInfo> order2cAgentInfos = order2cAgentInfoMapper
                        .selectByExample(new Order2cAgentInfoExample().createCriteria()
                                .andOrderIdEqualTo(order2cInfo.getOrderId()).example());
                if (CollectionUtils.isNotEmpty(order2cAgentInfos)) {
                    AfterMarketOrderDetailVO.Agent agent = new AfterMarketOrderDetailVO.Agent();
                    agent.setAgentName(order2cAgentInfos.get(0).getAgentName());
                    agent.setAgentNumber(order2cAgentInfos.get(0).getAgentNumber());
                    agent.setAgentPhone(order2cAgentInfos.get(0).getAgentPhone());
                    vo.setAgent(agent);
                }
                AfterMarketOrderDetailVO.AccountManager accountManager = new AfterMarketOrderDetailVO.AccountManager();
                accountManager.setEmployeeNum(order2cInfo.getEmployeeNum());
                accountManager.setCustomerManagerName(order2cInfo.getCustMgName());
                accountManager.setCustomerManagerPhone(order2cInfo.getCustMgPhone());
                vo.setAccountManager(accountManager);
            }

        /*if (PARTNER_LORD_ROLE.equals(loginIfo4Redis.getRoleType())
                || PARTNER_ROLE.equals(loginIfo4Redis.getRoleType())
                || loginIfo4Redis.getIsPartner()) {
            // 从合作伙伴
            if (SPUOfferingClassEnum.A06.getSpuOfferingClass().equals(order2cInfo.getSpuOfferingClass())){
                isAmountSetAsterisk = true;
            }
            isCooperator = true;
        }*/


//        }
        // 添加安装图片
        AftermarketOrderDeliveryAttachmentsExample aftermarketOrderDeliveryAttachmentsExample = new AftermarketOrderDeliveryAttachmentsExample();
        AftermarketOrderDeliveryAttachmentsExample.Criteria aftermarketOrderDeliveryAttachmentsCriteria = aftermarketOrderDeliveryAttachmentsExample
                .createCriteria();
        aftermarketOrderDeliveryAttachmentsCriteria.andServiceOrderIdEqualTo(serviceOrderId);
        List<AftermarketOrderDeliveryAttachments> aftermarketOrderDeliveryAttachmentsList = aftermarketOrderDeliveryAttachmentsMapper
                .selectByExample(aftermarketOrderDeliveryAttachmentsExample);
        if (aftermarketOrderDeliveryAttachmentsList.size() > 0) {
            List<UpResult> upResultList = aftermarketOrderDeliveryAttachmentsList.stream()
                    .map(aftermarketOrderDeliveryAttachments -> {
                        UpResult upResult = new UpResult();
                        upResult.setFileName(aftermarketOrderDeliveryAttachments.getFileName());
                        upResult.setKey(aftermarketOrderDeliveryAttachments.getFileKey());
                        upResult.setOuterUrl(aftermarketOrderDeliveryAttachments.getFileUrl());
                        return upResult;
                    }).collect(Collectors.toList());
            vo.setImageList(upResultList);
        }

        MallOrderInfoVO offeringOrderInfo = vo.getOfferingOrderInfo();
        boolean hasOfferingOrderInfo = Optional.ofNullable(offeringOrderInfo).isPresent();
        if (hasOfferingOrderInfo){
            // 解密后的信息
            String receiverName = offeringOrderInfo.getReceiverName();
            String receiverPhone = offeringOrderInfo.getReceiverPhone();
            String receiverAddress = offeringOrderInfo.getReceiverAddress();
            // 所有范式订单的收货人姓名、收货人手机号、收货人地址默认去敏显示
            /*if (contactDesensitizationStatus == 1){
                if (org.apache.commons.lang.StringUtils.isNotEmpty(receiverName)){
                    offeringOrderInfo.setReceiverName(DesensitizationUtils.smartDesensitizeName(receiverName));
                }

                if (org.apache.commons.lang.StringUtils.isNotEmpty(receiverPhone)){
                    offeringOrderInfo.setReceiverPhone(DesensitizationUtils.replaceWithStar(receiverPhone));
                }
                if (org.apache.commons.lang.StringUtils.isNotEmpty(receiverAddress)){
                    offeringOrderInfo.setReceiverAddress(DesensitizationUtils.maskAddress(receiverAddress));
                }
            }*/
        }

        /*
        合作伙伴中心的联合销售订单的订单金额、抵扣金额显示为**。
        合作伙伴中心其他范式订单的订单金额、抵扣金额均正常显示。
        当合作伙伴属于外部人员时，所有范式订单的订单金额、抵扣金额显示为**。
        OS管理员中心则全部正常显示.

        合作伙伴中心的联合销售订单的软件原子金额显示为**，硬件原子金额正常显示。
        合作伙伴中心其他范式订单的原子金额均正常显示。
        当合作伙伴属于外部人员时，所有范式订单的所有原子金额显示为**。
        OS管理员中心则全部正常显示
         */

        /*if (isAmountSetAsterisk || isExternal){
            vo.setTotalPrice("**");
        }


        // 仅OS管理员中心显示合作伙伴名称板块，合作伙伴中心不显示
        if (isCooperator){
            if (hasOfferingOrderInfo){
                vo.getOfferingOrderInfo().getAtomOrders()
                        .forEach(mallAtomOrderInfoVO -> {
                            mallAtomOrderInfoVO.setCooperatorName("-");
                        });
            }
            List<AfterMarketOrderOfferingDetailVO> offeringItemList = vo.getOfferingItems();
            if (CollectionUtils.isNotEmpty(offeringItemList)){
                offeringItemList.stream().forEach(afterMarketOrderOfferingDetailVO -> {
                    afterMarketOrderOfferingDetailVO.setAdminCooperatorId("-");
                    afterMarketOrderOfferingDetailVO.setAdminCooperatorName("-");
                });
            }
        }*/

        //添加签到图片
        AftermarketOrderSignInAttachmentsExample aftermarketOrderSignInAttachmentsExample = new AftermarketOrderSignInAttachmentsExample();
        List<AftermarketOrderSignInAttachments> signInAttachments = aftermarketOrderSignInAttachmentsMapper.selectByExample(aftermarketOrderSignInAttachmentsExample.createCriteria()
                .andServiceOrderIdEqualTo(serviceOrderId).example());
        if (CollectionUtils.isNotEmpty(signInAttachments)) {
            List<UpResult> upResultList = signInAttachments.stream().map(aftermarketOrderSignInAttachments -> {
                UpResult upResult = new UpResult();
                upResult.setFileName(aftermarketOrderSignInAttachments.getFileName());
                upResult.setKey(aftermarketOrderSignInAttachments.getFileKey());
                upResult.setOuterUrl(aftermarketOrderSignInAttachments.getFileUrl());
                return upResult;
            }).collect(Collectors.toList());
            vo.setSignInImageList(upResultList);
        }

        // 添加物流信息
        if (order2cInfo != null) {
            LogisticsInfoExample logisticsInfoExample = new LogisticsInfoExample();
            LogisticsInfoExample.Criteria logisticsCriteria = logisticsInfoExample.createCriteria();
            logisticsCriteria.andOrderIdEqualTo(order2cInfo.getOrderId());
            List<LogisticsInfo> logisticsInfoList = logisticsInfoMapper.selectByExample(logisticsInfoExample);

            if (CollectionUtils.isNotEmpty(logisticsInfoList)) {
                List<LogisticsInfoVO> logisticsInfoVOList = logisticsInfoList.stream().map(logisticsInfo -> {
                    LogisticsInfoVO logisticsInfoVO = new LogisticsInfoVO();
                    BeanUtils.copyProperties(logisticsInfo, logisticsInfoVO);

                    // 设置物流供应商中文名称
                    if (StringUtils.isNotEmpty(logisticsInfo.getSupplierName()) && supplierInfoMapConfig != null) {
                        String supplierCnName = supplierInfoMapConfig.getSupplierCnName(logisticsInfo.getSupplierName());
                        logisticsInfoVO.setSupplierCnName(supplierCnName);
                    }
                    // 设置物流状态描述
                    String logisticsStateDesc = getLogisticsStateDesc(logisticsInfo.getLogisticsState());
                    logisticsInfoVO.setLogisticsStateDesc(logisticsStateDesc);

                    return logisticsInfoVO;
                }).collect(Collectors.toList());
                vo.setLogisticsInfoList(logisticsInfoVOList);
            }
        }

        logService.recordOperateLog(ModuleEnum.ORDER_MANAGE.code,
                OrderManageOperateEnum.AFTER_SALE_ORDER.code,
                content, LogResultEnum.LOG_SUCESS.code, null);
        return BaseAnswer.success(vo);
    }

    private String contactAddr(String addr1, String addr2, String addr3, String addr4, String usaddr) {
        return IOTEncodeUtils.decryptSM4(addr1, iotSm4Key, iotSm4Iv) +
                IOTEncodeUtils.decryptSM4(addr2, iotSm4Key, iotSm4Iv) +
                IOTEncodeUtils.decryptSM4(addr3, iotSm4Key, iotSm4Iv) +
                IOTEncodeUtils.decryptSM4(addr4, iotSm4Key, iotSm4Iv) +
                IOTEncodeUtils.decryptSM4(usaddr, iotSm4Key, iotSm4Iv);
    }

    /**
     * 获取物流状态描述
     * @param logisticsState 物流状态
     * @return 状态描述
     */
    private String getLogisticsStateDesc(Integer logisticsState) {
        if (logisticsState == null) {
            return "未知状态";
        }
        switch (logisticsState) {
            case 0:
                return "在途";
            case 1:
                return "已揽收";
            case 2:
                return "疑难";
            case 3:
                return "已签收";
            case 4:
                return "退签";
            case 5:
                return "派件";
            case 8:
                return "清关";
            case 14:
                return "拒签";
            default:
                return "未知状态";
        }
    }

    /**
     * 检查订单是否已经同步到省测
     * 通过查询订单历史记录来判断是否存在已派单状态
     *
     * @param serviceOrderId 服务订单ID
     * @return true-已经同步到省测，false-未同步到省测
     */
    private boolean checkIfAlreadySynced(String serviceOrderId) {
        try {
            // 构建查询条件：查询指定订单的历史记录，状态为已派单（3）
            AftermarketOrderHistoryExample example = new AftermarketOrderHistoryExample();
            AftermarketOrderHistoryExample.Criteria criteria = example.createCriteria();
            criteria.andServiceOrderIdEqualTo(serviceOrderId)
                    .andOperateTypeEqualTo(AfterMarketOrderResultConstant.AFTER_MARKET_ORDER_HISTORY_OPERATE_TYPE_)
                    .andInnerStatusEqualTo(AfterMarketOrderStatusEnum.ORDER_SYNC_PROVINCE.getStatus());

            // 查询历史记录
            List<AftermarketOrderHistory> historyList = aftermarketOrderHistoryMapper.selectByExample(example);

            // 如果存在已派单的历史记录，说明订单已经同步到省测
            boolean hasDispatched = !CollectionUtils.isEmpty(historyList);

            log.info("订单{}同步状态检查结果：{}", serviceOrderId, hasDispatched ? "已同步" : "未同步");
            return hasDispatched;

        } catch (Exception e) {
            log.error("检查订单{}同步状态时发生异常：{}", serviceOrderId, e.getMessage(), e);
            // 发生异常时，为了安全起见，返回true，不允许重复同步
            return true;
        }
    }

    /**
     * 判断订单是否被物流接收（已签收）
     *
     * @param orderId 订单ID
     * @return true-订单已被物流接收（已签收），false-订单未被物流接收
     */
    private boolean isOrderReceivedByLogistics(String orderId) {
        List<LogisticsInfo> logisticsInfos = logisticsInfoMapper.selectByExample(new LogisticsInfoExample().createCriteria()
                .andOrderIdEqualTo(orderId).example());
        /**
         * 3	签收	快件已签收
         * 301	本人签收	收件人正常签收
         * 302	派件异常后签收	快件显示派件异常，但后续正常签收
         * 303	代签	快件已被代签
         * 304	投柜或站签收	快件已从快递柜或者驿站取出签收
         * */

        return (!CollectionUtils.isEmpty(logisticsInfos) && (logisticsInfos.stream().allMatch(x -> (x.getLogisticsState() != null
                && (x.getLogisticsState() == 3 || x.getLogisticsState() == 301 || x.getLogisticsState() == 302 || x.getLogisticsState() == 303
                || x.getLogisticsState() == 304)))));
    }

    @Override
    public void export(AfterMarketOrderExportParam param, LoginIfo4Redis loginIfo4Redis, HttpServletResponse response) throws IOException {
        AfterMarketOrder2cInfoExample afterMarketOrder2cInfoExample = new AfterMarketOrder2cInfoExample();
        AfterMarketOrder2cInfoExample.Criteria criteria = afterMarketOrder2cInfoExample.createCriteria();
        List<AfterMarketOrderExportVO> orderExportVOS = new ArrayList<>();
        try {
            if (StringUtils.isNotEmpty(param.getOrderStartTime())) {
                criteria.andCreateTimeGreaterThanOrEqualTo(
                        DateUtils.strToDate(param.getOrderStartTime(), DateUtils.DATETIME_FORMAT_T));
            }
            if (StringUtils.isNotEmpty(param.getOrderEndTime())) {
                criteria.andCreateTimeLessThanOrEqualTo(
                        DateUtils.strToDate(param.getOrderEndTime(), DateUtils.DATETIME_FORMAT_T));
            }
            afterMarketOrder2cInfoExample.orderBy("create_time desc");

            List<AfterMarketOrder2cInfo> afterMarketOrder2cInfos = afterMarketOrder2cInfoMapper
                    .selectByExample(afterMarketOrder2cInfoExample);
            List<String> afterMarketOrderIds = afterMarketOrder2cInfos.stream()
                    .map(AfterMarketOrder2cInfo::getServiceOrderId).collect(Collectors.toList());

            String roleType = loginIfo4Redis.getRoleType();
            String userId = loginIfo4Redis.getUserId();
            if (CollectionUtils.isNotEmpty(afterMarketOrderIds)) {
                AfterMarketOrder2cOfferingInfoExample afterMarketOrder2cOfferingInfoExample = new AfterMarketOrder2cOfferingInfoExample();
                AfterMarketOrder2cOfferingInfoExample.Criteria afterMarketOrder2cOfferingInfoCriteria = afterMarketOrder2cOfferingInfoExample
                        .createCriteria();
                afterMarketOrder2cOfferingInfoCriteria.andServiceOrderIdIn(afterMarketOrderIds);
                // 装维管理员
                if(roleType.equals(BaseConstant.PARTNER_INSTALL_MANAGER_ROLE)){
                    afterMarketOrder2cOfferingInfoCriteria.andInstallManagerIdEqualTo(userId);
                }
                // 装维主
                else if (roleType.equals(BaseConstant.PARTNER_INSTALL_LORD_ROLE)){
                    afterMarketOrder2cOfferingInfoCriteria.andAdminCooperatorIdEqualTo(userId);
                }
                //装维从
                else if (roleType.equals(BaseConstant.PARTNER_INSTALL_SUB_ROLE)){
                    //查询对应的从账号对应的主账号的信息
                    //听说用户相关的代码要后面在上先用笨方法来
                    UserPartner userPartner =  userPartnerMapper.selectByPrimaryKey(userId);

                    List<UserPartner> userPartner1 = userPartnerMapper.selectByExample(
                            new UserPartnerExample().createCriteria()
                                    .andRoleIdEqualTo("1376576697935507457")
                                    .andPartnerNameEqualTo(userPartner.getPartnerName())
                                    .andIsLogoffEqualTo(false)
                                    .example()
                    );
                    afterMarketOrder2cOfferingInfoCriteria.andAdminCooperatorIdEqualTo(userPartner1.get(0).getUserId());

                }
                List<AfterMarketOrder2cOfferingInfo> order2cOfferingInfos = afterMarketOrder2cOfferingInfoMapper
                        .selectByExample(afterMarketOrder2cOfferingInfoExample);

                List<AftermarketOrderHistory> aftermarketOrderHistories = aftermarketOrderHistoryMapper.selectByExample(
                        new AftermarketOrderHistoryExample().createCriteria().andServiceOrderIdIn(afterMarketOrderIds)
                                .andOperateTypeEqualTo(1).andInnerStatusIsNotNull().example()
                                .orderBy("create_time desc"));

                afterMarketOrder2cInfos.forEach(order -> {
                    List<AfterMarketOrder2cOfferingInfo> offeringInfos = order2cOfferingInfos.stream()
                            .filter(x -> StringUtils.equals(order.getServiceOrderId(), x.getServiceOrderId()))
                            .collect(Collectors.toList());
                    if (offeringInfos.stream()
                            .anyMatch(x -> x.getOrderTakeType() != null && x.getOrderTakeType() == 2)) {
                        // 省侧接单
                        AfterMarketOrderExportVO vo = new AfterMarketOrderExportVO();
                        vo.setOrderId(order.getServiceOrderId());
                        vo.setCreateOrderTime(order.getCreateTime());
                        vo.setOrderStatus(order.getStatus());
                        vo.setAppointmentTime(order.getAppointmentTime());
                        vo.setInstallName(offeringInfos.get(0).getPresentSendOrderName());
                        vo.setInstallPhone(offeringInfos.get(0).getPresentSendOrderPhone());

                        List<AftermarketOrderHistory> histories = aftermarketOrderHistories.stream()
                                .filter(x -> StringUtils.equals(order.getServiceOrderId(), x.getServiceOrderId()))
                                .collect(Collectors.toList());

                        histories.forEach(history -> {
                            if (history.getInnerStatus().equals(AfterMarketOrderStatusEnum.DISPATCHING.getStatus())
                                    && ObjectUtils.isEmpty(vo.getCreateAppointmentTime())) {
                                vo.setCreateAppointmentTime(history.getCreateTime());
                            } else if (history.getInnerStatus()
                                    .equals(AfterMarketOrderStatusEnum.ORDER_SYNC_PROVINCE.getStatus())
                                    && ObjectUtils.isEmpty(vo.getSyncProvinceTime())) {
                                vo.setSyncProvinceTime(history.getCreateTime());
                            } else if (history.getInnerStatus()
                                    .equals(AfterMarketOrderStatusEnum.DISPATCHED.getStatus())
                                    && ObjectUtils.isEmpty(vo.getDispatchTime())) {
                                vo.setDispatchTime(history.getCreateTime());
                            } else if (history.getInnerStatus()
                                    .equals(AfterMarketOrderStatusEnum.ORDER_FINISH.getStatus())
                                    && ObjectUtils.isEmpty(vo.getDispatchTime())) {
                                vo.setFinishTime(history.getCreateTime());
                            } else if (history.getInnerStatus()
                                    .equals(AfterMarketOrderStatusEnum.ORDER_FAIL.getStatus())
                                    && ObjectUtils.isEmpty(vo.getDispatchTime())) {
                                vo.setFinishTime(history.getCreateTime());
                            }
                        });
                        orderExportVOS.add(vo);
                    }
                });
            }

            String fileName = "省侧接单装维订单信息表";
            ExportParams exportParams = new ExportParams(fileName,
                    fileName, ExcelType.XSSF);
            ExcelUtils.exportExcel(orderExportVOS, AfterMarketOrderExportVO.class,
                    fileName, exportParams, response);
        } catch (ParseException e) {
            throw new IOException("时间参数异常");
        }

    }
    @Transactional(rollbackFor = Exception.class)
    @Override
    public BaseAnswer<Void> importAfterMarketOrder(InputStream inputStream, LoginIfo4Redis loginIfo4Redis,
            HttpServletRequest request,
            HttpServletResponse response) throws Exception {
        // 日志前缀
        String logPrefix = "导入售后订单-";
        // 获取当前用户信息
        String userId = loginIfo4Redis.getUserId();
        String userName = loginIfo4Redis.getUserName();
        String roleType = loginIfo4Redis.getRoleType();



        // 设置响应头
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");

        log.info("{}开始导入售后订单, 操作人: {}", logPrefix, userName);

        try {
            // 使用AfterMarketOrderExcelListener处理Excel导入
            AfterMarketOrderExcelListener listener = new AfterMarketOrderExcelListener(
                    aftermarketOfferingInfoMapper, aftermarketOfferingCodeMapper);
            List<Object> list = EasyExcel.read(inputStream, AfterMarketOrderImportParam.class, listener)
                    .sheet(0).headRowNumber(1).doReadSync();

            if (list.size() == 0) {
                throw new BusinessException(BaseErrorConstant.EMPTY_EXCEL);
            }

            List<AfterMarketOrderImportParam> successList = listener.getSucceedListData();
            List<AfterMarketOrderImportFailed> failedList = listener.getFailedListData();

            int successSize = successList.size();
            int failSize = failedList.size();
            int totalCount = successSize + failSize;

            log.info("{}共解析{}条数据，成功{}条，失败{}条", logPrefix, totalCount, successSize, failSize);

            // 有校验失败的返回错误信息
            if (CollectionUtils.isNotEmpty(failedList)) {
                log.info("售后订单申请失败：{}", failedList);
                String excelName = "批量售后订单申请失败信息";
                ClassPathResource classPathResource = new ClassPathResource("template/aftermarket_order_import_error.xlsx");
                InputStream templateFileName = classPathResource.getInputStream();
                String stateCode = BaseErrorConstant.BATCH_PRODUCT_APPLY_FAILED.getStateCode();
                String message = BaseErrorConstant.BATCH_PRODUCT_APPLY_FAILED.getMessage();
                //构建填充excel参数
                Map<String, Object> map = new HashMap<String, Object>();
                EasyExcelUtils.exportExcel(response, "list", failedList, map, excelName, templateFileName,
                        0, "失败列表", stateCode, message);
            } else {
                // 处理成功导入的记录
                Date now = new Date();

                for (AfterMarketOrderImportParam param : successList) {
                    // 1. 创建售后订单主信息
                    AfterMarketOrder2cInfo afterMarketOrder2cInfo = new AfterMarketOrder2cInfo();
                    // 记录历史操作
                    AftermarketOrderHistory history = new AftermarketOrderHistory();
                    String serviceOrderId = BaseServiceUtils.getId();
                    afterMarketOrder2cInfo.setServiceOrderId(serviceOrderId);
                    afterMarketOrder2cInfo.setOfferingOrderId(null); // 关联商品订单号
                    // 获取售后服务商品信息
                    // 权限验证：判断是否为业务管理员，如果不是则验证用户权限

                    List<AftermarketOfferingCode> aftermarketOfferingCodes = new ArrayList<>();
                    if (!BaseConstant.ADMIN_ROLE.equals(roleType)) {
                        log.info("{}非业务管理员用户，开始验证权限, 用户ID: {}, 角色类型: {}", logPrefix, userId, roleType);

                        // 获取实际用于权限验证的用户ID（如果是从账号，则使用主账号ID）
                        String permissionUserId = userId;
                        Boolean isPrimary = loginIfo4Redis.getIsPrimary();

                        // 如果是从账号，需要获取主账号ID进行权限验证
                        if (loginIfo4Redis.getRoleType().equals(BaseConstant.PARTNER_INSTALL_SUB_ROLE)) {
                            log.info("{}检测到从账号，开始查找主账号信息, 从账号ID: {}", logPrefix, userId);

                            // 通过partnerName查找主账号
                            UserPartner currentUser = userPartnerMapper.selectByPrimaryKey(userId);
                            if (currentUser == null) {
                                log.error("{}从账号用户信息不存在，用户ID: {}", logPrefix, userId);
                                throw new BusinessException(BaseErrorConstant.UN_PERMISSION, "用户信息不存在");
                            }

                            // 查找同一partnerName下的主账号
                            List<UserPartner> primaryUsers = userPartnerMapper.selectByExample(
                                    new UserPartnerExample().createCriteria()
                                            .andRoleIdEqualTo("1376576697935507457")
                                            .andPartnerNameEqualTo(currentUser.getPartnerName())
                                            .andIsLogoffEqualTo(false)
                                            .andIsCancelEqualTo(false)
                                            .example()
                            );

                            if (primaryUsers.isEmpty()) {
                                log.error("{}未找到对应的主账号，从账号ID: {}, 合作伙伴名称: {}", logPrefix, userId, currentUser.getPartnerName());
                                throw new BusinessException(BaseErrorConstant.UN_PERMISSION, "未找到对应的主账号");
                            }

                            permissionUserId = primaryUsers.get(0).getUserId();
                            log.info("{}从账号权限验证将使用主账号ID: {}, 合作伙伴名称: {}", logPrefix, permissionUserId, currentUser.getPartnerName());
                        }

                        // 查询aftermarket_offering_code表，检查用户是否有权限，支持版本匹配
                        AftermarketOfferingCodeExample permissionExample = new AftermarketOfferingCodeExample();

                        // 构建查询条件：(admin_cooperator_id = permissionUserId OR install_manager_id = permissionUserId) AND after_market_code = servicePackageCode
                        AftermarketOfferingCodeExample.Criteria adminCriteria = permissionExample.createCriteria();
                        adminCriteria.andAdminCooperatorIdEqualTo(permissionUserId);
                        adminCriteria.andAfterMarketCodeEqualTo(param.getServicePackageCode());
                        // 添加版本匹配条件
                        addVersionCriteria(adminCriteria, param);

                        AftermarketOfferingCodeExample.Criteria installCriteria = permissionExample.createCriteria();
                        installCriteria.andInstallManagerIdEqualTo(permissionUserId);
                        installCriteria.andAfterMarketCodeEqualTo(param.getServicePackageCode());
                        // 添加版本匹配条件
                        addVersionCriteria(installCriteria, param);

                        permissionExample.or(adminCriteria);
                        permissionExample.or(installCriteria);

                        aftermarketOfferingCodes = aftermarketOfferingCodeMapper.selectByExample(permissionExample);

                        if (aftermarketOfferingCodes.isEmpty()) {
                            log.error("{}用户权限验证失败，验证用户ID: {} (原用户ID: {}), 服务包代码: {}, 版本信息: {}",
                                    logPrefix, permissionUserId, userId, param.getServicePackageCode(), getVersionInfo(param));
                            throw new BusinessException(BaseErrorConstant.UN_PERMISSION, "没有导入该商品权限或版本信息不匹配");
                        }

                        log.info("{}用户权限验证通过，验证用户ID: {} (原用户ID: {}), 拥有 {} 个相关配置", logPrefix, permissionUserId, userId, aftermarketOfferingCodes.size());
                    } else {
                        AftermarketOfferingCodeExample aftermarketOfferingCodeExample = new AftermarketOfferingCodeExample();
                        AftermarketOfferingCodeExample.Criteria aftermarketOfferingCodeCriteria = aftermarketOfferingCodeExample
                                .createCriteria();
                        aftermarketOfferingCodeCriteria.andAfterMarketCodeEqualTo(param.getServicePackageCode());
                        // 添加版本匹配条件
                        addVersionCriteria(aftermarketOfferingCodeCriteria, param);

                        aftermarketOfferingCodes = aftermarketOfferingCodeMapper
                                .selectByExample(aftermarketOfferingCodeExample);
                        if (aftermarketOfferingCodes.isEmpty()) {
                            throw new BusinessException(BaseErrorConstant.UN_PERMISSION, "没有导入该商品权限或版本信息不匹配");
                        }
                    }


                    AftermarketOfferingCode aftermarketOfferingCode = aftermarketOfferingCodes.get(0);
                    // 根据接单类型设置订单状态
                    if (AfterMarketOrderResultConstant.AFTER_MARKET_ORDER_ORDER_TAKE_TYPE_OS
                            .equals(aftermarketOfferingCode.getOrderTakeType())) {
                        afterMarketOrder2cInfo.setStatus(
                                aftermarketOfferingCode.getInstallManagerId() == null
                                        ? AfterMarketOrderStatusEnum.DISPATCHING.getStatus()
                                        : AfterMarketOrderStatusEnum.BEFORE_DISPATCHING.getStatus());
                        history.setInnerStatus( aftermarketOfferingCode.getInstallManagerId() == null
                                ? AfterMarketOrderStatusEnum.DISPATCHING.getStatus()
                                : AfterMarketOrderStatusEnum.BEFORE_DISPATCHING.getStatus());
                    }

                    afterMarketOrder2cInfo.setStatusTime(now);
                    // 获取aftermarket_offering_info，支持版本匹配
                    AftermarketOfferingInfoExample aftermarketOfferingInfoExample = new AftermarketOfferingInfoExample();
                    AftermarketOfferingInfoExample.Criteria aftermarketOfferingInfoCriteria = aftermarketOfferingInfoExample
                            .createCriteria();
                    aftermarketOfferingInfoCriteria.andAfterMarketCodeEqualTo(param.getServicePackageCode())
                            .andOperTypeEqualTo("A");


                    List<AftermarketOfferingInfo> aftermarketOfferingInfos = aftermarketOfferingInfoMapper
                            .selectByExample(aftermarketOfferingInfoExample);

                    if (aftermarketOfferingInfos.isEmpty()) {
                        log.error("{}未找到匹配的售后服务包信息，服务包代码: {}, 版本: {}",
                                logPrefix, param.getServicePackageCode(), param.getAfterMarketVersion());
                        throw new BusinessException(BaseErrorConstant.UN_PERMISSION, "售后服务包信息不存在或版本不匹配");
                    }

                    AftermarketOfferingInfo aftermarketOfferingInfo = aftermarketOfferingInfos.get(0);
                    // Convert String price to BigDecimal before multiplication
                    BigDecimal price = new BigDecimal(aftermarketOfferingInfo.getSellPrice());
                    BigDecimal totalPrice = price.multiply(BigDecimal.valueOf(param.getOrderQuantity()));
                    afterMarketOrder2cInfo.setTotalPrice(totalPrice.toString());
                    // 设置预约信息
                    afterMarketOrder2cInfo.setAppointmentName(param.getAppointmentName());
                    afterMarketOrder2cInfo.setAppointmentPhone(param.getAppointmentPhone());
                    afterMarketOrder2cInfo.setAppointmentSubmitType(1); // 客户自主填入

                    // 设置地址信息
                    afterMarketOrder2cInfo.setAddr1(param.getAppointmentProvince());
                    afterMarketOrder2cInfo.setAddr2(param.getAppointmentCity());
                    afterMarketOrder2cInfo.setAddr3(param.getAppointmentDistrict());
                    afterMarketOrder2cInfo.setAddr4(null); // 乡镇信息未提供
                    afterMarketOrder2cInfo.setUsaddr(param.getDetailAddress());
                    // 设置预约地址
                    String appointmentAddress = param.getAppointmentProvince() + param.getAppointmentCity()
                            + param.getAppointmentDistrict() + param.getDetailAddress();
                    afterMarketOrder2cInfo.setAppointmentAddress(appointmentAddress);
                    // 解析预约日期 - 支持多种格式
                    Date appointmentTime = DateTimeUtil.parseAppointmentDate(param.getAppointmentDate());
                    afterMarketOrder2cInfo.setAppointmentTime(appointmentTime);

                    // 设置区域编码
                    afterMarketOrder2cInfo.setBeId(null);
                    afterMarketOrder2cInfo.setRegionId(null);

                    // 设置是否内部订单
                    afterMarketOrder2cInfo.setIsInner(true); // 导入的订单设为内部订单

                    // 设置创建和更新时间
                    afterMarketOrder2cInfo.setCreateTime(now);
                    afterMarketOrder2cInfo.setUpdateTime(now);

                    // 2. 创建售后订单商品信息
                    AfterMarketOrder2cOfferingInfo offeringInfo = new AfterMarketOrder2cOfferingInfo();
                    offeringInfo.setId(BaseServiceUtils.getId());
                    offeringInfo.setServiceOrderId(serviceOrderId);
                    offeringInfo.setAfterMarketCode(param.getServicePackageCode());
                    offeringInfo.setAfterMarketName(aftermarketOfferingInfo.getAfterMarketExternalName());
                    offeringInfo.setAfterMarketType(Integer.parseInt(aftermarketOfferingInfo.getAftermarketType()));
                    offeringInfo.setQuantity(param.getOrderQuantity().longValue());
                    offeringInfo.setOrderTakeType(aftermarketOfferingCode.getOrderTakeType());
                    offeringInfo.setAfterMarketSettlePrice(aftermarketOfferingInfo.getSettlePrice());

                    // 设置版本信息 - 优先使用导入的版本信息，如果没有则使用配置表中的版本信息
                    offeringInfo.setAfterMarketVersion(StringUtils.isNotEmpty(param.getAfterMarketVersion())
                            ? param.getAfterMarketVersion() : aftermarketOfferingCode.getAfterMarketVersion());
                    offeringInfo.setSpuOfferingVersion(aftermarketOfferingCode.getSpuOfferingVersion());
                    offeringInfo.setSkuOfferingVersion(StringUtils.isNotEmpty(param.getSkuOfferingVersion())
                            ? param.getSkuOfferingVersion() : aftermarketOfferingCode.getSkuOfferingVersion());
                    offeringInfo.setAtomOfferingVersion(StringUtils.isNotEmpty(param.getAtomOfferingVersion())
                            ? param.getAtomOfferingVersion() : aftermarketOfferingCode.getAtomOfferingVersion());

                    // 设置商品编码信息 - 优先使用导入的编码信息，如果没有则使用配置表中的编码信息
                    offeringInfo.setSkuOfferingCode(StringUtils.isNotEmpty(param.getSkuOfferingCode())
                            ? param.getSkuOfferingCode() : aftermarketOfferingCode.getSkuOfferingCode());
                    offeringInfo.setAtomOfferingCode(StringUtils.isNotEmpty(param.getAtomOfferingCode())
                            ? param.getAtomOfferingCode() : aftermarketOfferingCode.getOfferingCode());
                    offeringInfo.setSendOrderCompany(null); // 派单时设置
                    offeringInfo.setSpuOfferingCode(aftermarketOfferingCode.getSpuOfferingCode());
                    offeringInfo.setSpuOfferingClass(aftermarketOfferingCode.getSpuOfferingClass());

                    log.info("{}设置商品信息完成，服务订单ID: {}, 使用版本信息: {}", logPrefix, serviceOrderId, getVersionInfo(param));

                    // 设置商品名称 - 使用实际的编码和版本信息进行查询
                    String actualSkuCode = StringUtils.isNotEmpty(param.getSkuOfferingCode())
                            ? param.getSkuOfferingCode() : aftermarketOfferingCode.getSkuOfferingCode();
                    String actualAtomCode = StringUtils.isNotEmpty(param.getAtomOfferingCode())
                            ? param.getAtomOfferingCode() : aftermarketOfferingCode.getOfferingCode();

                    if (StringUtils.isNotEmpty(actualSkuCode) && aftermarketOfferingCode.getSpuOfferingCode() != null) {
                        // 查询SKU商品名称
                        SkuOfferingInfoHistoryExample skuOfferingInfoHistoryExample = new SkuOfferingInfoHistoryExample();
                        SkuOfferingInfoHistoryExample.Criteria skuOfferingInfoHistoryCriteria = skuOfferingInfoHistoryExample
                                .createCriteria();
                        skuOfferingInfoHistoryCriteria
                                .andOfferingCodeEqualTo(actualSkuCode)
                                .andSpuCodeEqualTo(aftermarketOfferingCode.getSpuOfferingCode())
                                .andSpuOfferingVersionEqualTo(aftermarketOfferingCode.getSpuOfferingVersion());

                        // 使用实际的SKU版本信息
                        String actualSkuVersion = StringUtils.isNotEmpty(param.getSkuOfferingVersion())
                                ? param.getSkuOfferingVersion() : aftermarketOfferingCode.getSkuOfferingVersion();
                        if (StringUtils.isNotEmpty(actualSkuVersion)) {
                            skuOfferingInfoHistoryCriteria.andSkuOfferingVersionEqualTo(actualSkuVersion);
                        }

                        List<SkuOfferingInfoHistory> skuOfferingInfoHistories = skuOfferingInfoHistoryMapper
                                .selectByExample(skuOfferingInfoHistoryExample);

                        if (!skuOfferingInfoHistories.isEmpty()) {
                            offeringInfo.setSkuOfferingName(skuOfferingInfoHistories.get(0).getOfferingName());
                            log.info("{}查询到SKU商品名称: {}, 编码: {}, 版本: {}",
                                    logPrefix, skuOfferingInfoHistories.get(0).getOfferingName(), actualSkuCode, actualSkuVersion);
                        } else {
                            log.warn("{}未找到匹配的SKU商品信息，编码: {}, 版本: {}", logPrefix, actualSkuCode, actualSkuVersion);
                        }
                    } else if (StringUtils.isNotEmpty(actualAtomCode)) {
                        // 查询原子商品名称
                        AtomOfferingInfoHistoryExample atomOfferingInfoHistoryExample = new AtomOfferingInfoHistoryExample();
                        AtomOfferingInfoHistoryExample.Criteria atomOfferingInfoHistoryCriteria = atomOfferingInfoHistoryExample
                                .createCriteria();
                        atomOfferingInfoHistoryCriteria.andOfferingCodeEqualTo(actualAtomCode);

                        // 使用实际的原子商品版本信息
                        String actualAtomVersion = StringUtils.isNotEmpty(param.getAtomOfferingVersion())
                                ? param.getAtomOfferingVersion() : aftermarketOfferingCode.getAtomOfferingVersion();
                        if (StringUtils.isNotEmpty(actualAtomVersion)) {
                            atomOfferingInfoHistoryCriteria.andAtomOfferingVersionEqualTo(actualAtomVersion);
                        }

                        List<AtomOfferingInfoHistory> atomOfferingInfoHistories = atomOfferingInfoHistoryMapper
                                .selectByExample(atomOfferingInfoHistoryExample);

                        if (!atomOfferingInfoHistories.isEmpty()) {
                            offeringInfo.setAtomOfferingName(atomOfferingInfoHistories.get(0).getOfferingName());
                            log.info("{}查询到原子商品名称: {}, 编码: {}, 版本: {}",
                                    logPrefix, atomOfferingInfoHistories.get(0).getOfferingName(), actualAtomCode, actualAtomVersion);
                        } else {
                            log.warn("{}未找到匹配的原子商品信息，编码: {}, 版本: {}", logPrefix, actualAtomCode, actualAtomVersion);
                        }
                    }

                    // 设置安装管理信息 - admin_cooperator_id和install_manager_id只会存在一个
                    offeringInfo.setAdminCooperatorId(aftermarketOfferingCode.getAdminCooperatorId());
                    offeringInfo.setInstallManagerId(aftermarketOfferingCode.getInstallManagerId());
                    offeringInfo.setProvinceInstallPlatform(aftermarketOfferingCode.getProvinceInstallPlatform());

                    // 根据存在的ID设置相关人员信息
                    if (StringUtils.isNotEmpty(aftermarketOfferingCode.getAdminCooperatorId())) {
                        // 如果存在admin_cooperator_id，查询用户信息
                        UserExample userExample = new UserExample();
                        UserExample.Criteria userCriteria = userExample.createCriteria();
                        userCriteria.andUserIdEqualTo(aftermarketOfferingCode.getAdminCooperatorId());
                        List<User> users = userMapper.selectByExample(userExample);
                        if (CollectionUtils.isNotEmpty(users)) {
                            offeringInfo.setAdminCooperatorName(users.get(0).getName());
                        }
                    } else if (StringUtils.isNotEmpty(aftermarketOfferingCode.getInstallManagerId())) {
                        // 如果存在install_manager_id，查询装维管理员信息
                        UserExample userExample = new UserExample();
                        UserExample.Criteria userCriteria = userExample.createCriteria();
                        userCriteria.andUserIdEqualTo(aftermarketOfferingCode.getInstallManagerId());
                        List<User> users = userMapper.selectByExample(userExample);
                        if (CollectionUtils.isNotEmpty(users)) {
                            // 这里可以根据业务需要设置相应的字段，比如装维管理员名称等
                            // 由于没有对应的字段，暂时不设置
                        }
                    }

                    // 派单相关信息(派单时设置)
                    offeringInfo.setPresentSendOrderName(null);
                    offeringInfo.setPresentSendOrderPhone(null);
                    offeringInfo.setUserId(null);
                    offeringInfo.setSendOrderTime(null);

                    // 交付相关信息(交付时设置)
                    offeringInfo.setDeliverTime(null);
                    offeringInfo.setDeliverFailedMsg(null);

                    offeringInfo.setCreateTime(now);
                    offeringInfo.setUpdateTime(now);

                    // 3. 保存数据
                    // 保存售后订单主信息
                    afterMarketOrder2cInfoMapper.insertSelective(afterMarketOrder2cInfo);

                    // 保存售后订单商品信息
                    afterMarketOrder2cOfferingInfoMapper.insertSelective(offeringInfo);


                    history.setId(BaseServiceUtils.getId());
                    history.setServiceOrderId(serviceOrderId);
                    history.setOperateType(
                            Integer.valueOf(AfterMarketOrderResultConstant.AFTER_MARKET_ORDER_HISTORY_OPERATE_TYPE_));
                    history.setOperatorId(userId);

                    history.setOperateMessage("卖家于" + DateUtils.dateToStr(new Date(), DateUtils.DATETIME_FORMAT_T)
                            + "导入订单");
                    history.setCreateTime(now);
                    history.setUpdateTime(now);
                    aftermarketOrderHistoryMapper.insertSelective(history);

                }
            }
            // 记录操作日志
            // logService.recordOperateLog(ModuleEnum.ORDER_MANAGE.code,
            // OrderManageOperateEnum.AFTER_SALE_ORDER.code,
            // "导入售后订单，成功:" + successSize + "条，失败:" + failSize + "条",
            // LogResultEnum.LOG_SUCESS.code, null);

        } catch (IOException ioe) {
            log.error("{}读取文件异常，异常描述：{}", logPrefix, ioe);
            throw new BusinessException(BaseErrorConstant.FILE_PARSE_ERROR);
        } catch (ExcelAnalysisException e) {
            log.error("{}Excel解析异常，异常描述：{}", logPrefix, e);
            throw new BusinessException(BaseErrorConstant.RULE_NOT_ALLOW, e.getCause().getMessage());
        } catch (BusinessException e) {
            log.error("{}业务异常，异常描述：{}", logPrefix, e);
            throw e;
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException(BaseErrorConstant.FILE_PARSE_ERROR);

        }
        response.setHeader("statecode","00000");
        return BaseAnswer.success(null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer<Void> updateAdminCooperatorName(UpdateAdminCooperatorParam param) {
        if (org.apache.commons.lang.StringUtils.isEmpty(param.getServiceOrderId()) || org.apache.commons.lang.StringUtils.isEmpty(param.getAdminCooperatorId())) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "参数不能为空");
        }

        // 查询状态为11的售后订单
        AfterMarketOrder2cInfo order = afterMarketOrder2cInfoMapper.selectByPrimaryKey(param.getServiceOrderId());
        if (order == null || 
            (!AfterMarketOrderStatusEnum.BEFORE_DISPATCHING.getStatus().equals(order.getStatus()) 
             && !AfterMarketOrderStatusEnum.DISPATCHING.getStatus().equals(order.getStatus()))) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "售后订单不存在或状态错误");
        }

        if(!validateOrderInfo(order)){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "该导入订单不能分派");
        }

        // 更新关联服务商品的admin_cooperator_id
        AfterMarketOrder2cOfferingInfoExample example = new AfterMarketOrder2cOfferingInfoExample();
        example.createCriteria()
                .andServiceOrderIdEqualTo(param.getServiceOrderId());

        List<AfterMarketOrder2cOfferingInfo> offeringInfos = afterMarketOrder2cOfferingInfoMapper.selectByExample(example);

        if(CollectionUtils.isEmpty(offeringInfos)) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "售后订单不存在");
        }else{
            String existAdminCooperatorId = offeringInfos.get(0).getAdminCooperatorId();
            if(StringUtils.isNotEmpty(existAdminCooperatorId) && existAdminCooperatorId.equals(param.getAdminCooperatorId())){
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "重新分派人员不能和前分派人员是同一个人");
            }
        }

        List<InstallManagerAndPartnerRelation> installManagerAndPartnerRelationList = installManagerAndPartnerRelationMapper.selectByExample(
                new InstallManagerAndPartnerRelationExample().createCriteria()
                        .andInstallManagerIdEqualTo(offeringInfos.get(0).getInstallManagerId())
                        .andUserPartnerIdEqualTo(param.getAdminCooperatorId())
                        .example()
        );

        if(CollectionUtils.isEmpty(installManagerAndPartnerRelationList)){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "装维服务商与装维管理员不匹配");
        }
        UserPartner userPartner = userPartnerMapper.selectByPrimaryKey(
                param.getAdminCooperatorId()
        );
        if (userPartner == null) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "装维服务商不存在");
        }
        AfterMarketOrder2cOfferingInfo updateInfo = new AfterMarketOrder2cOfferingInfo();
        updateInfo.setAdminCooperatorId(param.getAdminCooperatorId());
        updateInfo.setAdminCooperatorName(userPartner.getPartnerName());
        // 看文档的样子来说 重新分派停留时间不变
        updateInfo.setUpdateTime(new Date());
        updateInfo.setSendOrderTime(new Date());
        afterMarketOrder2cOfferingInfoMapper.updateByExampleSelective(updateInfo, example);

        // 更新售后订单表
        order.setStatusTime(new Date());
        order.setStatus(AfterMarketOrderStatusEnum.DISPATCHING.getStatus());
        afterMarketOrder2cInfoMapper.updateByPrimaryKeySelective(order);
        String msg = String.format("接单合作伙伴 %s",
                userPartner.getPartnerName());
        String prev = (StringUtils.isEmpty(offeringInfos.get(0).getAdminCooperatorId())) ? "完成分派。" : "修改分派。";
        afterMarketOrderLogForSync(param.getServiceOrderId(),
                AfterMarketOrderStatusEnum.DISPATCHING.getStatus(), prev + msg);


        return BaseAnswer.success(null);
    }

    private void afterMarketOrderLogForSync(String serviceOrderId, Integer status, String message) {
        AftermarketOrderHistory history = new AftermarketOrderHistory();
        Date now = DateUtils.now();

        history.setId(BaseServiceUtils.getId());
        history.setCreateTime(now);
        history.setUpdateTime(now);
        history.setServiceOrderId(serviceOrderId);
        history.setInnerStatus(status);
        history.setOperateMessage(message);
        history.setOperateType(1);

        aftermarketOrderHistoryMapper.insertSelective(history);
    }
    /**
     * 校验工单信息有效性
     *
     * @param orderInfo 工单信息
     * @return 校验结果，true表示校验通过，false表示校验不通过
     */
    private boolean validateOrderInfo(AfterMarketOrder2cInfo orderInfo) {
        // 校验是否为导入订单
        String logSvcId = (orderInfo.getServiceOrderId() != null) ? orderInfo.getServiceOrderId()
                : null;
        if (orderInfo.getIsInner() == null || !Boolean.TRUE.equals(orderInfo.getIsInner())) {
            log.error("订单 {} 不是一个有效的内部导入订单. 校验不通过。", logSvcId);
            return true;
        }

        // 校验订购数量：至少为1
        // Note: This quantity check currently fetches from DB based on serviceOrderId.
        // For 'update' scenarios, the new quantity is validated before calling this
        // method.
        // This check here acts as a safeguard for the existing/fetched quantity.
        AfterMarketOrder2cOfferingInfoExample offeringInfoExample = new AfterMarketOrder2cOfferingInfoExample();
        AfterMarketOrder2cOfferingInfoExample.Criteria offeringInfoCriteria = offeringInfoExample.createCriteria();
        offeringInfoCriteria.andServiceOrderIdEqualTo(orderInfo.getServiceOrderId());
        List<AfterMarketOrder2cOfferingInfo> offeringInfos = afterMarketOrder2cOfferingInfoMapper
                .selectByExample(offeringInfoExample);

        if (CollectionUtils.isEmpty(offeringInfos) || offeringInfos.get(0).getQuantity() < 1) {
            log.error("订单 {} 的订购数量小于1 (fetched based on serviceOrderId).", logSvcId);
            return false;
        }

        // 校验预约人电话：是否为11位纯数字
        String appointmentPhone = orderInfo.getAppointmentPhone();
        if (StringUtils.isEmpty(appointmentPhone) || appointmentPhone.length() != 11
                || !appointmentPhone.matches("\\d{11}")) {
            log.error("订单 {} 的预约人电话 {} 不是11位纯数字", logSvcId, appointmentPhone);
            return false;
        }

        // 校验预约地址（省市区）：是否为民政部标准行政区域信息
        // 同时校验输入省市区相互关联关系，即各级区域信息与上级是否属于从属关系
        String province = orderInfo.getAddr1();
        String city = orderInfo.getAddr2();
        String district = orderInfo.getAddr3();

        if (StringUtils.isEmpty(province) || StringUtils.isEmpty(city) || StringUtils.isEmpty(district)) {
            log.error("订单 {} 的预约地址省市区信息不完整", logSvcId);
            return false;
        }
        return true;
    }

    /**
     * 检查售后服务订单信息是否有预约人的信息
     *
     * @param afterMarketOrder2cInfo 售后订单信息
     * @return true表示有预约人信息，false表示没有预约人信息
     */
    private boolean hasAppointmentInfo(AfterMarketOrder2cInfo afterMarketOrder2cInfo) {
        if (afterMarketOrder2cInfo == null) {
            log.warn("售后订单信息为空，无法检查预约人信息");
            return false;
        }

        String serviceOrderId = afterMarketOrder2cInfo.getServiceOrderId();
        String appointmentName = afterMarketOrder2cInfo.getAppointmentName();
        String appointmentPhone = afterMarketOrder2cInfo.getAppointmentPhone();
        String appointmentAddress = afterMarketOrder2cInfo.getAppointmentAddress();

        // 检查预约人姓名是否存在且不为空
        boolean hasName = StringUtils.isNotEmpty(appointmentName);

        // 检查预约人电话是否存在且不为空
        boolean hasPhone = StringUtils.isNotEmpty(appointmentPhone);

        // 检查预约地址是否存在且不为空
        boolean hasAddress = StringUtils.isNotEmpty(appointmentAddress);

        // 只有三个字段都存在才认为有完整的预约人信息
        boolean hasCompleteInfo = hasName && hasPhone && hasAddress;

        if (!hasCompleteInfo) {
            log.warn("订单 {} 预约人信息不完整: appointmentName={}, appointmentPhone={}, appointmentAddress={}",
                    serviceOrderId, appointmentName, appointmentPhone, appointmentAddress);
        }

        return hasCompleteInfo;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer updateAfterMarketOrder(AfterMarketOrderUpdateParam orderUpdateParam,
            LoginIfo4Redis loginIfo4Redis, String ip) {
        String serviceOrderId = orderUpdateParam.getServiceOrderId();
        String logPrefix = "修改售后订单-" + serviceOrderId + "-";
        log.info("{}开始, 操作人: {}", logPrefix, loginIfo4Redis.getUserName());

        // 1. 查询订单是否存在
        AfterMarketOrder2cInfo afterMarketOrder2cInfo = afterMarketOrder2cInfoMapper.selectByPrimaryKey(serviceOrderId);
        if (afterMarketOrder2cInfo == null) {
            log.error("{}订单不存在", logPrefix);
            throw new BusinessException(StatusConstant.ORDER_NOT_EXIST);
        }

        // 2. 校验订单状态是否允许修改（未完结）
        Integer currentStatus = afterMarketOrder2cInfo.getStatus();
        if (AfterMarketOrderStatusConstant.DELIVERY_SUCCESS_STATUS.equals(currentStatus) ||
                AfterMarketOrderStatusConstant.DELIVERY_FAIL_STATUS.equals(currentStatus) ||
                AfterMarketOrderStatusConstant.ORDER_CANCELED_STATUS.equals(currentStatus) ||
                AfterMarketOrderStatusConstant.ORDER_FINISH_STATUS.equals(currentStatus) ||
                AfterMarketOrderStatusConstant.ORDER_FAIL_STATUS.equals(currentStatus)) {
            log.error("{}订单状态[{}]不允许修改", logPrefix, currentStatus);
            throw new BusinessException(StatusConstant.ORDER_STATUS_NOT_ALLOW_MODIFY);
        }

        // 3. 数据校验
        // 3.1 校验新订购数量
        if (orderUpdateParam.getOrderQuantity() == null || orderUpdateParam.getOrderQuantity() < 1) {
            log.error("{}新订购数量 {} 无效 (必须大于等于1)", logPrefix, orderUpdateParam.getOrderQuantity());
            throw new BusinessException(StatusConstant.ORDER_INFO_VALIDATE_FAILED);
        }

        // 3.2 校验其他信息 (电话、地址等) 和订单类型
        AfterMarketOrder2cInfo validationData = new AfterMarketOrder2cInfo();
        validationData.setServiceOrderId(serviceOrderId);
        validationData.setIsInner(afterMarketOrder2cInfo.getIsInner());
        validationData.setAppointmentPhone(orderUpdateParam.getAppointmentPhone());
        validationData.setAddr1(orderUpdateParam.getAppointmentProvince());
        validationData.setAddr2(orderUpdateParam.getAppointmentCity());
        validationData.setAddr3(orderUpdateParam.getAppointmentDistrict());
        if (!validateOrderInfo(validationData)) {
            log.error("{}订单信息校验失败 (isInner, phone, or address).详情请查看validateOrderInfo内的日志。", logPrefix);
            throw new BusinessException(StatusConstant.ORDER_INFO_VALIDATE_FAILED);
        }

        // 4. 更新订单商品信息 (主要更新订购数量)
        AfterMarketOrder2cOfferingInfoExample offeringInfoExample = new AfterMarketOrder2cOfferingInfoExample();
        offeringInfoExample.createCriteria().andServiceOrderIdEqualTo(serviceOrderId);
        List<AfterMarketOrder2cOfferingInfo> offeringInfos = afterMarketOrder2cOfferingInfoMapper
                .selectByExample(offeringInfoExample);

        if (CollectionUtils.isEmpty(offeringInfos)) {
            log.error("{}未找到订单关联的商品信息", logPrefix);
            throw new BusinessException(StatusConstant.ORDER_OFFERING_NOT_FOUND);
        }
        AfterMarketOrder2cOfferingInfo offeringInfoToUpdate = offeringInfos.get(0);
        offeringInfoToUpdate.setQuantity(orderUpdateParam.getOrderQuantity().longValue());
        offeringInfoToUpdate.setUpdateTime(new Date());
        afterMarketOrder2cOfferingInfoMapper.updateByPrimaryKeySelective(offeringInfoToUpdate);

        // 5. 计算新的订单总价
        String newTotalPriceString = afterMarketOrder2cInfo.getTotalPrice(); // 默认为旧价格，如果无法计算新价格
        AftermarketOfferingInfoExample aftermarketOfferingInfoExample = new AftermarketOfferingInfoExample();
        aftermarketOfferingInfoExample.createCriteria()
                .andAfterMarketCodeEqualTo(offeringInfoToUpdate.getAfterMarketCode())
                .andOperTypeEqualTo("A");
        List<AftermarketOfferingInfo> currentPricedOfferings = aftermarketOfferingInfoMapper
                .selectByExample(aftermarketOfferingInfoExample);

        if (CollectionUtils.isNotEmpty(currentPricedOfferings)) {
            AftermarketOfferingInfo currentPriceInfo = currentPricedOfferings.get(0);
            BigDecimal price = new BigDecimal(currentPriceInfo.getSellPrice());
            BigDecimal newTotalPrice = price.multiply(BigDecimal.valueOf(orderUpdateParam.getOrderQuantity()));
            newTotalPriceString = newTotalPrice.toString();
        } else {
            log.warn("{}未找到售后服务商品 {} 的有效价格信息，总价可能未正确更新。将沿用旧总价或默认逻辑。", logPrefix,
                    offeringInfoToUpdate.getAfterMarketCode());
        }

        // 6. 准备并执行订单主信息的一次性更新
        AfterMarketOrder2cInfo orderToUpdate = new AfterMarketOrder2cInfo();
        orderToUpdate.setServiceOrderId(serviceOrderId);
        orderToUpdate.setAppointmentName(orderUpdateParam.getAppointmentName());
        orderToUpdate.setAppointmentPhone(orderUpdateParam.getAppointmentPhone());
        orderToUpdate.setAddr1(orderUpdateParam.getAppointmentProvince());
        orderToUpdate.setAddr2(orderUpdateParam.getAppointmentCity());
        orderToUpdate.setAddr3(orderUpdateParam.getAppointmentDistrict());
        orderToUpdate.setUsaddr(orderUpdateParam.getDetailAddress());
        orderToUpdate.setAppointmentAddress(orderUpdateParam.getAppointmentProvince()+orderUpdateParam.getAppointmentCity()+orderUpdateParam.getAppointmentDistrict()+orderUpdateParam.getDetailAddress());
        orderToUpdate.setAppointmentTime(orderUpdateParam.getAppointmentDate());
        orderToUpdate.setTotalPrice(newTotalPriceString); // 设置计算出的新总价
        orderToUpdate.setUpdateTime(new Date());
        afterMarketOrder2cInfoMapper.updateByPrimaryKeySelective(orderToUpdate);

        // 7. 记录操作历史
        AftermarketOrderHistory history = new AftermarketOrderHistory();
        history.setId(BaseServiceUtils.getId());
        history.setServiceOrderId(serviceOrderId);
        history.setOperateType(AfterMarketOrderResultConstant.AFTER_MARKET_ORDER_HISTORY_OPERATE_TYPE_);
        history.setOperatorId(loginIfo4Redis.getUserId());
        history.setInnerStatus(afterMarketOrder2cInfo.getStatus());
        history.setOperateMessage(
                String.format("工单信息已修改。操作人：%s。修改内容：数量 %d, 预约人 %s, 电话 %s, 地址 %s%s%s %s, 预约时间 %s",
                        loginIfo4Redis.getUserName(),
                        orderUpdateParam.getOrderQuantity(), orderUpdateParam.getAppointmentName(),
                        orderUpdateParam.getAppointmentPhone(),
                        orderUpdateParam.getAppointmentProvince(), orderUpdateParam.getAppointmentCity(),
                        orderUpdateParam.getAppointmentDistrict(),
                        orderUpdateParam.getDetailAddress(),
                        DateUtils.dateToStr(orderUpdateParam.getAppointmentDate(), DateUtils.DATETIME_FORMAT_T)
                       ));
        history.setCreateTime(new Date());
        history.setUpdateTime(new Date());
        aftermarketOrderHistoryMapper.insertSelective(history);

        log.info("{}修改成功", logPrefix);
        return BaseAnswer.success(null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer closeAfterMarketOrder(String serviceOrderId, LoginIfo4Redis loginIfo4Redis, String ip) {
        String logPrefix = "关闭售后订单-" + serviceOrderId + "-";
        log.info("{}开始, 操作人: {}", logPrefix, loginIfo4Redis.getUserName());

        // 1. 查询订单是否存在
        AfterMarketOrder2cInfo afterMarketOrder2cInfo = afterMarketOrder2cInfoMapper.selectByPrimaryKey(serviceOrderId);
        if (afterMarketOrder2cInfo == null) {
            log.error("{}订单不存在", logPrefix);
            throw new BusinessException(StatusConstant.ORDER_NOT_EXIST);
        }

        // 2. 校验订单状态是否允许关闭 (仅限手动导入且未完结的工单)
        // 检查是否为已完结状态：已完结（成功）、已完结（失败）、交易完成、交易失败、已撤销、已关闭
        Integer currentStatus = afterMarketOrder2cInfo.getStatus();
        if (currentStatus.equals(AfterMarketOrderStatusConstant.DELIVERY_SUCCESS_STATUS) || // 已完结（成功）
                currentStatus.equals(AfterMarketOrderStatusConstant.DELIVERY_FAIL_STATUS) || // 已完结（失败）
                currentStatus.equals(AfterMarketOrderStatusConstant.ORDER_FINISH_STATUS) || // 交易完成
                currentStatus.equals(AfterMarketOrderStatusConstant.ORDER_FAIL_STATUS) || // 交易失败
                currentStatus.equals(AfterMarketOrderStatusConstant.ORDER_CANCELED_STATUS) || // 已撤销
                currentStatus.equals(AfterMarketOrderStatusConstant.ORDER_CLOSED_STATUS)) { // 已关闭
            log.error("{}订单状态[{}]不允许关闭，仅未完结的工单可关闭", logPrefix, currentStatus);
            throw new BusinessException(StatusConstant.ORDER_STATUS_NOT_ALLOW_CLOSE);
        }

        // TODO: 这里需要添加检查是否为手动导入工单的逻辑
        // 可以通过查询订单历史记录中是否有导入操作来判断
        // 或者在订单表中添加标识字段来区分手动导入和系统同步的订单
        if(!afterMarketOrder2cInfo.getIsInner()==true){
            log.error("{}订单不是手动导入工单，不允许关闭", logPrefix);
            throw new BusinessException(StatusConstant.ORDER_NOT_ALLOW_CLOSE);

        }
        // 3. 执行关闭操作
        // 3.1 更新订单状态为"已关闭"
        Date now = new Date();
        AfterMarketOrder2cInfo updateOrder = new AfterMarketOrder2cInfo();
        updateOrder.setServiceOrderId(serviceOrderId);
        updateOrder.setStatus(AfterMarketOrderStatusConstant.ORDER_CLOSED_STATUS);
        updateOrder.setUpdateTime(now);

        int updatedRows = afterMarketOrder2cInfoMapper.updateByPrimaryKeySelective(updateOrder);
        if (updatedRows == 0) {
            log.error("{}更新订单状态失败", logPrefix);
            throw new BusinessException(StatusConstant.INTERNAL_ERROR, "更新订单状态失败");
        }
        log.info("{}订单状态已更新为已关闭", logPrefix);

        // 3.2 记录操作历史到处理信息中
        AftermarketOrderHistory history = new AftermarketOrderHistory();
        history.setId(BaseServiceUtils.getId());
        history.setServiceOrderId(serviceOrderId);
        history.setOperateType(AfterMarketOrderResultConstant.AFTER_MARKET_ORDER_HISTORY_OPERATE_TYPE_);
        history.setOperatorId(loginIfo4Redis.getUserId());
        history.setInnerStatus(AfterMarketOrderStatusConstant.ORDER_CLOSED_STATUS);
        history.setOperateMessage(
                String.format("工单已关闭。操作人：%s (%s)。关闭时间：%s",
                        loginIfo4Redis.getUserName(),
                        loginIfo4Redis.getUserId(),
                        DateTimeUtil.formatDate(now, DateTimeUtil.DEFAULT_DATE_DEFAULT)));
        history.setCreateTime(now);
        history.setUpdateTime(now);
        aftermarketOrderHistoryMapper.insertSelective(history);
        log.info("{}操作历史记录已添加", logPrefix);

        // 4. 记录操作日志
        logService.recordOperateLog(ModuleEnum.ORDER_MANAGE.code,
                OrderManageOperateEnum.AFTER_SALE_ORDER.code,
                String.format("关闭售后工单: %s, 操作人: %s (%s)", serviceOrderId, loginIfo4Redis.getUserName(),
                        loginIfo4Redis.getUserId()),
                LogResultEnum.LOG_SUCESS.code,
                null);

        log.info("{}关闭成功", logPrefix);
        return BaseAnswer.success(null);
    }

    @Override
    public BaseAnswer<AfterMarketOrderStatsVO> getAfterMarketOrderStats(LoginIfo4Redis loginIfo4Redis) {
        AfterMarketOrderStatsVO statsVO = new AfterMarketOrderStatsVO();
        AfterMarketOrderQueryParam param = new AfterMarketOrderQueryParam();

        // 设置查询参数
        String roleType = loginIfo4Redis.getRoleType();
        if(roleType.equals(BaseConstant.PARTNER_INSTALL_MANAGER_ROLE)){
            param.setInstallManagerId(loginIfo4Redis.getUserId());
        } else if(roleType.equals(BaseConstant.PARTNER_INSTALL_LORD_ROLE)){
            param.setAdminCooperatorId(loginIfo4Redis.getUserId());
        } else if(roleType.equals(BaseConstant.PARTNER_INSTALL_SUB_ROLE)){
            UserPartner userPartner = userPartnerMapper.selectByPrimaryKey(loginIfo4Redis.getUserId());
            List<UserPartner> userPartner1 = userPartnerMapper.selectByExample(
                    new UserPartnerExample().createCriteria()
                            .andRoleIdEqualTo("1376576697935507457")
                            .andPartnerNameEqualTo(userPartner.getPartnerName())
                            .andIsLogoffEqualTo(false)
                            .example()
            );
            param.setAdminCooperatorId(userPartner1.get(0).getUserId());
        }

        // 1. 工单总数
        List<AfterMarketOrderStatusItemVO> afterMarketOrderStatusItemVOList = afterMarkOrder2cOfferingInfoMapperExt.getAfterMarketOrderInfoStatus(param);
        Integer totalCount = afterMarketOrderStatusItemVOList.size();
        statsVO.setTotalCount(totalCount);

        // 2. 未分派工单数（只对装维管理员显示）
        if(roleType.equals(BaseConstant.PARTNER_INSTALL_MANAGER_ROLE)){
            statsVO.setUnassignedCount(afterMarketOrderStatusItemVOList.stream()
                    .filter(item->item.getStatus().equals(AfterMarketOrderStatusEnum.BEFORE_DISPATCHING.getStatus()))
                    .collect(Collectors.toList()).size());
        }

        // 3. 未派单工单数
        statsVO.setUndispatchedCount(afterMarketOrderStatusItemVOList.stream()
                .filter(item->item.getStatus().equals(AfterMarketOrderStatusEnum.DISPATCHING.getStatus()))
                .collect(Collectors.toList()).size());

        // 4. 未施工工单数（已派单+已签到）
        statsVO.setUncompletedCount(afterMarketOrderStatusItemVOList.stream()
                .filter(item->item.getStatus().equals(AfterMarketOrderStatusEnum.DISPATCHED.getStatus())
                        || item.getStatus().equals(AfterMarketOrderStatusEnum.SIGN_IN.getStatus())
                )
                .collect(Collectors.toList()).size());

        // 5. 超时工单数（已派单且派单时间超过72小时）
        Date overtimeThreshold = new Date(System.currentTimeMillis() - 72 * 60 * 60 * 1000);
//        Date overtimeThreshold = new Date(System.currentTimeMillis() - 10 * 60 * 1000);
        statsVO.setOvertimeCount(afterMarketOrderStatusItemVOList.stream()
            .filter(item -> item.getStatus().equals(AfterMarketOrderStatusEnum.DISPATCHED.getStatus()))
            .filter(item -> item.getSendOrderTime() != null && item.getSendOrderTime().before(overtimeThreshold))
                        .collect(Collectors.toList()).size());

        // 6. 已完结工单数（包括成功/失败）
        statsVO.setFinishedCount(afterMarketOrderStatusItemVOList.stream()
                .filter(item-> item.getStatus().equals(AfterMarketOrderStatusEnum.DELIVERY_SUCCESS.getStatus())
                        || item.getStatus().equals(AfterMarketOrderStatusEnum.DELIVERY_FAIL.getStatus())
                )
                .collect(Collectors.toList()).size());

        // 7. 交付成功率（交付成功数/订单总量）
        if(totalCount > 0) {
            Integer successCount = afterMarketOrderStatusItemVOList.stream()
                    .filter(item->item.getStatus().equals(AfterMarketOrderStatusEnum.ORDER_FINISH.getStatus())
                            || item.getStatus().equals(AfterMarketOrderStatusEnum.DELIVERY_SUCCESS.getStatus())
                    )
                    .collect(Collectors.toList()).size();
            double successRate = (double) successCount / totalCount * 100;
            statsVO.setDeliverySuccessRate(String.format("%.2f%%", successRate));
        } else {
            statsVO.setDeliverySuccessRate("0%");
        }

        // 8. 订单退款率（退款成功数/工单总量）

        if(totalCount > 0) {
            Integer failCount = afterMarketOrderStatusItemVOList.stream()
                    .filter(item-> item.getStatus().equals(AfterMarketOrderStatusEnum.ORDER_FAIL.getStatus())
                            || item.getStatus().equals(AfterMarketOrderStatusEnum.DELIVERY_FAIL.getStatus())
                    )
                    .collect(Collectors.toList()).size();
            double refundRate = (double) failCount / totalCount * 100;
            statsVO.setRefundRate(String.format("%.2f%%", refundRate));
        } else {
            statsVO.setRefundRate("0%");
        }

        return BaseAnswer.success(statsVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer<Void> manualSyncToProvince(String serviceOrderId, LoginIfo4Redis loginIfo4Redis, String ip) {
        // 参数校验
        if (StringUtils.isEmpty(serviceOrderId)) {
            throw new BusinessException(StatusConstant.INTERNAL_ERROR, "售后服务订单ID不能为空");
        }

        // 查询售后订单信息
        AfterMarketOrder2cInfo afterMarketOrder2cInfo = afterMarketOrder2cInfoMapper.selectByPrimaryKey(serviceOrderId);
        if (afterMarketOrder2cInfo == null) {
            throw new BusinessException(StatusConstant.INTERNAL_ERROR, "售后订单不存在：" + serviceOrderId);
        }

        // 查询主订单信息
        Order2cInfo order2cInfo = order2cInfoMapper.selectByPrimaryKey(afterMarketOrder2cInfo.getOfferingOrderId());
        if (order2cInfo == null) {
            throw new BusinessException(StatusConstant.INTERNAL_ERROR, "关联的主订单不存在：" + afterMarketOrder2cInfo.getOfferingOrderId());
        }

        // 查询售后订单商品信息
        AfterMarketOrder2cOfferingInfoExample offeringInfoExample = new AfterMarketOrder2cOfferingInfoExample();
        AfterMarketOrder2cOfferingInfoExample.Criteria offeringInfoCriteria = offeringInfoExample.createCriteria();
        offeringInfoCriteria.andServiceOrderIdEqualTo(serviceOrderId);
        List<AfterMarketOrder2cOfferingInfo> order2cOfferingInfos = afterMarketOrder2cOfferingInfoMapper
                .selectByExample(offeringInfoExample);

        if (CollectionUtils.isEmpty(order2cOfferingInfos)) {
            throw new BusinessException(StatusConstant.INTERNAL_ERROR, "售后订单商品信息不存在");
        }

        // 检查是否已经同步到省测（通过查询历史记录判断是否已派单）
        boolean hasDispatched = checkIfAlreadySynced(serviceOrderId);
        if (hasDispatched) {
            throw new BusinessException(StatusConstant.INTERNAL_ERROR, "同步失败！请确认该订单是否已同步");
        }

        // 判断是否为省测接单
        boolean provinceTake = order2cOfferingInfos.stream()
                .anyMatch(item -> item.getOrderTakeType() != null && item.getOrderTakeType() == 2);

        if (!provinceTake) {
            throw new BusinessException(StatusConstant.INTERNAL_ERROR, "该订单不是省测接单订单，无需同步到省测");
        }

        // 检查订单状态是否符合同步条件
        if (!(order2cInfo.getStatus() == 1 || order2cInfo.getStatus() == 3
                || isOrderReceivedByLogistics(afterMarketOrder2cInfo.getOfferingOrderId()))) {
            throw new BusinessException(StatusConstant.INTERNAL_ERROR, "订单状态不符合同步条件");
        }
        //检查售后服务订单信息是否有预约人的信息
       Boolean hasAppointmentInfo =   hasAppointmentInfo(afterMarketOrder2cInfo);
        if(!hasAppointmentInfo){
            throw new BusinessException(StatusConstant.INTERNAL_ERROR, "预约人信息为空，请先完善预约人信息");
        }
        try {
            // 根据省份平台进行同步
            AfterMarketOrder2cOfferingInfo offeringInfo = order2cOfferingInfos.get(0);
            String provinceInstallPlatform = offeringInfo.getProvinceInstallPlatform();

            if ("henan".equals(provinceInstallPlatform)) {
                // 河南专用同步
                orderHenanZwService.sync2Province(order2cInfo, afterMarketOrder2cInfo);
            } else {
                // 通用省侧同步
                orderHenanZwService.sync2ProvinceCommon(order2cInfo, afterMarketOrder2cInfo);
            }

            // 记录操作日志
//            String content = "【手动推送到省测】\n"
//                    .concat("售后服务订单号：").concat(serviceOrderId)
//                    .concat("，操作人：").concat(loginIfo4Redis.getUserName());
//            logService.recordOperateLog(ModuleEnum.ORDER_MANAGE.code,
//                    OrderManageOperateEnum.AFTER_SALE_ORDER.code,
//                    content, LogResultEnum.LOG_SUCESS.code, null);

            return BaseAnswer.success(null);

        } catch (Exception e) {
            log.error("手动推送售后订单到省测失败，订单ID：{}，错误信息：{}", serviceOrderId, e.getMessage(), e);

            // 记录失败日志
//            String content = "【手动推送到省测失败】\n"
//                    .concat("售后服务订单号：").concat(serviceOrderId)
//                    .concat("，操作人：").concat(loginIfo4Redis.getUserName())
//                    .concat("，失败原因：").concat(e.getMessage());
//            logService.recordOperateLog(ModuleEnum.ORDER_MANAGE.code,
//                    OrderManageOperateEnum.AFTER_SALE_ORDER.code,
//                    content, LogResultEnum.LOG_FAIL.code, e.getMessage());

            throw new BusinessException(StatusConstant.SYNC_PROVINCE_FAILED, "同步失败：" + e.getMessage());
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateDeliveryStatus(AfterMarketOrderH5DeliveryParam deliveryParam,
                                     AfterMarketOrder2cOfferingInfo afterMarketOrder2cOfferingInfo,
                                     LoginIfo4Redis loginIfo4Redis) {
        Date date = new Date();
        String userId = loginIfo4Redis.getUserId();
        String roleType = loginIfo4Redis.getRoleType();
        String serviceOrderId = deliveryParam.getServiceOrderId();
        String failureReason = deliveryParam.getFailureReason();
        Integer deliveryStatus = deliveryParam.getDeliveryStatus();
        // 更新交付时间
        AfterMarketOrder2cOfferingInfo offeringInfo = new AfterMarketOrder2cOfferingInfo();
        offeringInfo.setDeliverTime(date);
        offeringInfo.setUpdateTime(date);
        if (deliveryStatus == 1) {
            offeringInfo.setDeliverFailedMsg(failureReason);
        }

        AfterMarketOrder2cOfferingInfoExample offeringInfoExample = new AfterMarketOrder2cOfferingInfoExample();
        AfterMarketOrder2cOfferingInfoExample.Criteria criteria = offeringInfoExample.createCriteria();
        criteria.andServiceOrderIdEqualTo(serviceOrderId);
        // 非超管、运管只能查询自己的处理单子
        if (!BaseConstant.ADMIN_ROLE.equals(roleType) &&
                !BaseConstant.OPERATOR_ROLE.equals(roleType)) {
            criteria.andUserIdEqualTo(userId);
        }
        afterMarketOrder2cOfferingInfoMapper.updateByExampleSelective(offeringInfo, offeringInfoExample);

        // 更新状态
        AfterMarketOrder2cInfoExample order2cInfoExample = new AfterMarketOrder2cInfoExample();
        order2cInfoExample.createCriteria().andServiceOrderIdEqualTo(serviceOrderId)
                .andStatusEqualTo(AfterMarketOrderStatusEnum.DISPATCHED.getStatus());
        AfterMarketOrder2cInfo order2cInfo = afterMarketOrder2cInfoMapper.selectByPrimaryKey(serviceOrderId);
        if (deliveryStatus == 0) {
            order2cInfo.setStatus(AfterMarketOrderStatusEnum.DELIVERY_SUCCESS.getStatus());
        } else {
            order2cInfo.setStatus(AfterMarketOrderStatusEnum.DELIVERY_FAIL.getStatus());
        }
        order2cInfo.setUpdateTime(date);
        order2cInfo.setStatusTime(date);
        afterMarketOrder2cInfoMapper.updateByPrimaryKeySelective(order2cInfo);
        //交付成功时，保存交付图片
        if (deliveryStatus == 0) {
            List<UpResult> imageList = deliveryParam.getImageList();
            if (CollectionUtils.isNotEmpty(imageList)) {
                Date now = new Date();
                List<AftermarketOrderDeliveryAttachments> attacments = imageList.stream().map(upResult -> {
                    AftermarketOrderDeliveryAttachments attacment = new AftermarketOrderDeliveryAttachments();
                    attacment.setId(BaseServiceUtils.getId());
                    attacment.setServiceOrderId(serviceOrderId);
                    attacment.setFileName(upResult.getFileName());
                    attacment.setFileKey(upResult.getKey());
                    attacment.setFileUrl(upResult.getOuterUrl());
                    attacment.setCreateTime(now);
                    attacment.setUpdateTime(now);
                    return attacment;
                }).collect(Collectors.toList());
                aftermarketOrderDeliveryAttachmentsMapper.batchInsert(attacments);
            }

        }
        // 添加历史记录
        AftermarketOrderHistory history = new AftermarketOrderHistory();
        history.setId(BaseServiceUtils.getId());
        history.setServiceOrderId(serviceOrderId);
        history.setOperateType(AfterMarketOrderResultConstant.AFTER_MARKET_ORDER_HISTORY_OPERATE_TYPE_);
        String operateMessage = "完成交付。交付结果 ";
        if (deliveryStatus == 0) {
            history.setInnerStatus(AfterMarketOrderStatusEnum.DELIVERY_SUCCESS.getStatus());
            operateMessage = operateMessage.concat("成功");
        } else {
            history.setInnerStatus(AfterMarketOrderStatusEnum.DELIVERY_FAIL.getStatus());
            operateMessage = operateMessage.concat("失败");
        }
        history.setOperatorId(userId);
        history.setOperateMessage(operateMessage);
        history.setCreateTime(date);
        history.setUpdateTime(date);
        aftermarketOrderHistoryMapper.insertSelective(history);

        // 同步到商城
        if(!order2cInfo.getIsInner()){
            ServiceOrderResultRequest resultRequest = new ServiceOrderResultRequest();
            resultRequest.setOrderId(serviceOrderId);
            resultRequest.setOprType("2");
            ServiceOrderResultRequest.DeliverInfo deliverInfo = new ServiceOrderResultRequest.DeliverInfo();
            if (deliveryStatus == 0) {
                deliverInfo.setDeliverResult("1");
            } else {
                deliverInfo.setDeliverResult("2");
                deliverInfo.setReason(failureReason);
            }
            resultRequest.setDeliverInfo(deliverInfo);
            orderHenanZwService.sendServiceOrderResult(resultRequest);
        }
        //添加日志
        logService.recordOperateLog(ModuleEnum.ORDER_MANAGE.code,
                OrderManageOperateEnum.AFTER_SALE_ORDER.code,
                IotLogUtil.updateDeliveryStatusFromParam(deliveryParam), LogResultEnum.LOG_SUCESS.code, null);
        // 删除服务码
        String redisContractValue = RedisLockConstant.AFTER_ORDER_DISPATCH_INSTALL_REDIS_KEY.concat(serviceOrderId)
                .concat(afterMarketOrder2cOfferingInfo.getPresentSendOrderPhone());
        redisTemplate.delete(redisContractValue);
    }

    /**
     * 添加版本匹配条件到查询条件中
     * @param criteria 查询条件
     * @param param 导入参数
     */
    private void addVersionCriteria(AftermarketOfferingCodeExample.Criteria criteria, AfterMarketOrderImportParam param) {
        if (StringUtils.isNotEmpty(param.getAfterMarketVersion())) {
            criteria.andAfterMarketVersionEqualTo(param.getAfterMarketVersion());
        }
        if (StringUtils.isNotEmpty(param.getSkuOfferingCode())) {
            criteria.andSkuOfferingCodeEqualTo(param.getSkuOfferingCode());
        }
        if (StringUtils.isNotEmpty(param.getSkuOfferingVersion())) {
            criteria.andSkuOfferingVersionEqualTo(param.getSkuOfferingVersion());
        }
        if (StringUtils.isNotEmpty(param.getAtomOfferingCode())) {
            criteria.andOfferingCodeEqualTo(param.getAtomOfferingCode());
        }
        if (StringUtils.isNotEmpty(param.getAtomOfferingVersion())) {
            criteria.andAtomOfferingVersionEqualTo(param.getAtomOfferingVersion());
        }
    }

    /**
     * 获取版本信息字符串，用于日志记录
     * @param param 导入参数
     * @return 版本信息字符串
     */
    private String getVersionInfo(AfterMarketOrderImportParam param) {
        StringBuilder versionInfo = new StringBuilder();
        if (StringUtils.isNotEmpty(param.getAfterMarketVersion())) {
            versionInfo.append("售后服务包版本:").append(param.getAfterMarketVersion()).append(", ");
        }
        if (StringUtils.isNotEmpty(param.getSkuOfferingCode())) {
            versionInfo.append("sku商品编码:").append(param.getSkuOfferingCode()).append(", ");
        }
        if (StringUtils.isNotEmpty(param.getSkuOfferingVersion())) {
            versionInfo.append("sku商品版本:").append(param.getSkuOfferingVersion()).append(", ");
        }
        if (StringUtils.isNotEmpty(param.getAtomOfferingCode())) {
            versionInfo.append("原子商品编码:").append(param.getAtomOfferingCode()).append(", ");
        }
        if (StringUtils.isNotEmpty(param.getAtomOfferingVersion())) {
            versionInfo.append("原子商品版本:").append(param.getAtomOfferingVersion()).append(", ");
        }

        String result = versionInfo.toString();
        return result.endsWith(", ") ? result.substring(0, result.length() - 2) : result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer<Void> deliveryAfterMarketOrder(AfterMarketOrderDeliveryParam deliveryParam, LoginIfo4Redis loginIfo4Redis) {

        if(!loginIfo4Redis.getRoleType().equals(BaseConstant.PARTNER_INSTALL_MANAGER_ROLE)){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "只有装维管理员可以进行此操作");
        }
        AfterMarketOrder2cInfo order = afterMarketOrder2cInfoMapper.selectByPrimaryKey(deliveryParam.getServiceOrderId());
        if (order == null ||
                !AfterMarketOrderStatusEnum.BEFORE_DISPATCHING.getStatus().equals(order.getStatus())) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "售后订单不存在或状态错误");
        }

//        if(!validateOrderInfo(order)){
//            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "该导入订单不能分派");
//        }

        Integer deliverStatus = deliveryParam.getDeliveryStatus() ?
                AfterMarketOrderStatusEnum.DELIVERY_SUCCESS.getStatus()
                : AfterMarketOrderStatusEnum.DELIVERY_FAIL.getStatus() ;

        order.setStatus(deliverStatus);
        order.setStatusTime(new Date());
        order.setUpdateTime(new Date());

        afterMarketOrder2cInfoMapper.updateByPrimaryKeySelective(order);


        return BaseAnswer.success(null);
    }
}
