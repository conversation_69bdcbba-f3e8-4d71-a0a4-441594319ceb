package com.chinamobile.export.pojo.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.util.Date;

/**
 * created by l<PERSON>xia<PERSON> on 2022/3/30 16:02
 */
@Data
public class OrderExportDTO {

    @ExcelProperty(value = "操作员编码")
    /**
     * 操作员编码
     */
    private String createOperCode;

    @ExcelProperty(value = "操作员省工号")
    /**
     * 操作员省工号
     */
    private String employeeNum;

    /**
     * 操作员姓名
     */
    @ExcelProperty(value = "操作员姓名")
    private String custMgName;

    /**
     * 操作员电话
     */
    @ExcelProperty(value = "操作员电话")
    private String custMgPhone;

    /**
     * 网格市
     */
    @ExcelProperty(value = "网格市")
    private String griddingCity;

    /**
     * 网格区
     */
    @ExcelProperty(value = "网格区")
    private String griddingArea;


    /**
     * 网格名称
     */
    @ExcelProperty(value = "网格名称")
    private String griddingName;


    /**
     * 一级分销员电话
     */
    @ExcelProperty(value = "一级分销员手机号")
    private String distributorPhone1;

    /**
     * 二级分销员电话
     */
    @ExcelProperty(value = "二级分销员手机号")
    private String distributorPhone2;

    /**
     * 渠道商id
     */
    @ExcelProperty(value = "渠道商id")
    private String distributorChannelId;

    /**
     * 渠道商名称
     */
    @ExcelProperty(value = "渠道商名称")
    private String distributorChannelName;

    /**
     * 渠道商全称
     */
    @ExcelProperty(value = "渠道商全称")
    private String agentName;

    /**
     * 渠道商编码
     */
    @ExcelProperty(value = "渠道商编码")
    private String agentNumber;

    /**
     * 渠道商标签清洗
     */
    @ExcelProperty(value = "渠道商标签")
    private String agentLabelWash;

    /**
     * 渠道商类别清洗
     */
    @ExcelProperty(value = "渠道商类别")
    private String agentCategoryWash;

    /**
     * 渠道商手机号
     */
    @ExcelProperty(value = "渠道商手机号")
    private String agentPhone;



    @ExcelProperty(value = "客户编码")
    /**
     * 客户编码
     */
    private String custCode;


    @ExcelProperty(value = "客户名称")
    /**
     * 客户名称
     */
    private String custName;


    /**
     * 客户custId(手机号)
     */
    @ExcelProperty(value = "客户手机号")
    private String custId;

    @ExcelProperty(value = "个人客户所属省份")
    /**
     * 个人客户省份
     */
    private String province;

    @ExcelProperty(value = "个人客户所属地市")
    /**
     * 个人客户所属归属地市编码
     */
    private String location;


    @ExcelProperty(value = "个人客户所属区县")
    /**
     * 个人客户所属归属区县
     */
    private String regionID;

    /**
     * 订单收入归属省;
     */
    @ExcelProperty(value = "订单收入归属省")
    private String orgProvince;

    /**
     * 订单收入归地市;
     */
    @ExcelProperty(value = "订单收入归属地市")
    private String orgLocation;

    /**
     * 订单收入归区县;
     */
    @ExcelProperty(value = "订单收入归属区县")
    private String orgRegion;

    /**
     * 订单收入归属营业厅;
     */
//    @ExcelProperty(value = "订单收入归属营业厅")
//    private String orgHall;


    /**
     * 下单时间
     */
    @ExcelProperty(value = "下单时间")
    private String createTime;

    /**
     * 接单时间
     */
    @ExcelProperty(value = "接单时间")
    private String receiveOrderTime;

    /**
     * 发货时间
     */
    @ExcelProperty(value = "发货时间")
    private String sendGoodsTime;

    /**
     * 待出账时间(代客下单为待出账时间，自主下单为下单时间)
     */
    @ExcelProperty(value = "待出账时间")
    private String valetOrderCompleteTime;

    /**
     * 已收货时间
     */
    private String completeTime;

    /**
     * 订单号
     */
    @ExcelProperty(value = "订单号")
    private String orderId;
    /**
     * 业务编码
     */
    @ExcelProperty(value = "业务编码")
    private String businessCode;

    /**
     * 订购渠道名称
     */
    @ExcelProperty(value = "订购渠道名称")
    private String orderingChannelName;
    /**
     * 访问方式
     */
    @ExcelProperty(value = "访问方式")
    private String ssoTerminalType;
    /**
     * 订单状态变更时间
     */
    @ExcelProperty(value = "订单状态变更时间")
    private String orderStatusTime;

    /**
     * 订单状态描述
     */
    @ExcelProperty(value = "订单状态")
    private String orderStatusDescribe;

    /**
     * 订单类型
     */
    @ExcelProperty(value = "订单类型")
    private String orderType;

    /**
     * 订单完成时间
     */
    @ExcelProperty(value = "订单完成时间")
    private String finishTime;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remarks;

    /**
     * 订单总金额
     */
    @ExcelProperty(value = "订单总金额")
    private Double orderTotalPrice;

    /**
     * 订单抵扣金额(单位元)
     */
    @ExcelProperty(value = "订单抵扣金额/元")
    private Double orderDeductPrice;

    /**
     * 关联领货码
     */
    @ExcelProperty(value = "关联领货码")
    private String couponInfo;

    /**
     * 收货地区(省份)
     */
    @ExcelProperty(value = "收货地区")
    private String deliveryArea;

    @ExcelProperty(value = "收货人姓名")
    /**
     * 收货人姓名
     */
    private String contactPersonName;

    @ExcelProperty(value = "收货人地址")
    /**
     * 收货人省（加密）
     */
    private String address;

    /**
     * 收货人电话
     */
    @ExcelProperty(value = "收货人电话")
    private String receiverPhone;


    @ExcelProperty(value = "物流单号")
    /**
     * 物流单号，多个逗号分隔
     */
    private String logisCode;

    @ExcelProperty(value = "设备sn")
    /**
     * 设备sn，多个逗号分隔
     */
    private String sn;

    /**
     * 商品组/销售商品名称
     */
    @ExcelProperty(value = "商品组/销售商品名称")
    private String spuOfferingName;

    /**
     * 商品组/销售商品编码
     */
    @ExcelProperty(value = "商品组/销售商品编码")
    private String spuOfferingCode;

    /**
     * SPU一级销售目录
     */
    @ExcelProperty(value = "商品类型")
    private String spuOfferingClass;

    /**
     * spu版本号
     */
    @ExcelProperty(value = "销售商品版本号")
    private String spuOfferingVersion;
    /**
     * 商品名称(规格)
     */
    @ExcelProperty(value = "商品名称(规格)")
    private String skuOfferingName;

    /**
     * 商品规格编码
     */
    @ExcelProperty(value = "商品编码（规格）")
    private String skuOfferingCode;

    /**
     * 订购数量（规格）
     */
    @ExcelProperty(value = "订购数量（规格）")
    private Long skuQuantity;

    /**
     * 建议零售价（规格）
     */
//    @ExcelProperty(value = "建议零售价（规格）")
//    private Double recommendPrice;

    /**
     * 销售目录价（规格）
     */
    @ExcelProperty(value = "销售目录价（规格）")
    private Double price;

    @ExcelProperty(value = "供应商名称")
    private String supplierName;

    /**
     * sku版本号
     */
    @ExcelProperty(value = "规格商品版本号")
    private String skuOfferingVersion;

    /**
     * 营销案名称
     */
//    @ExcelProperty(value = "营销案名称")
//    private String marketName;

    /**
     * 营销案编码
     */
//    @ExcelProperty(value = "营销案编码")
//    private String marketCode;

    /**
     * 原子商品名称
     */
    @ExcelProperty(value = "原子商品名称")
    private String atomOfferingName;
    /**
     * 原子商品编码
     */
    @ExcelProperty(value = "原子商品编码")
    private String atomOfferingCode;
    /**
     * 原子商品类型
     */
    @ExcelProperty(value = "原子商品类型")
    private String atomOfferingClass;
    /**
     * 型号
     */
//    @ExcelProperty(value = "型号")
//    private String model;


//    @ExcelProperty(value = "原子商品计量单位")
//    /**
//     * 计量单位
//     */
//    private String unit;

    /**
     * 颜色
     */
//    @ExcelProperty(value = "颜色")
//    private String color;

    /**
     * 数量（原子商品） skuQuantity*atomQuantity
     */
    @ExcelProperty(value = "数量（原子商品）")
    private Integer quantity;

    /**
     * 销售目录价（原子商品）
     */
//    @ExcelProperty(value = "销售目录价（原子商品）")
//    private Double atomPrice;

    /**
     * 销售金额（原子商品）
     */
    @ExcelProperty(value = "销售金额（原子商品）")
    private Double atomTotalPrice;

    @ExcelProperty(value = "结算单价（原子商品）")
    /**
     * 结算单价（原子商品）
     */
    private Double settlePrice;

    /**
     * 结算金额（原子商品）
     */
    @ExcelProperty(value = "结算金额（原子商品）")
    private Double totalSettlePrice;

    /**
     * 专合结算价
     */
    @ExcelProperty(value = "专合结算价")
    private String specialCooperativeSettlementPrice;

    /**
     * 原子商品版本号
     */
    @ExcelProperty(value = "原子商品版本号")
    private String atomOfferingVersion;

    /**
     * 原子订单抵扣金额(单位厘，已加密)
     */
//    @ExcelProperty(value = "原子订单抵扣金额/元")
//    private Double atomDeductPrice;

    /**
     * 合作伙伴名
     */
    @ExcelProperty(value = "合作伙伴名称")
    private String partnerName;
    /**
     * 合作伙伴姓名
     */
    @ExcelProperty(value = "合作伙伴联系人")
    private String cooperatorName;

    /**
     * 实质产品名称
     */
    @ExcelProperty(value = "核心部件")
    private String realProductName;

    /**
     * 标准服务编码
     */
    private String standardServiceCode;
    /**
     * 标准服务名称
     */
    private String standardServiceName;
    /**
     * 产品部门
     */
    private String department;
    /**
     * 产品属性
     */
    private String property;

    private String remark1;

    private String remark2;

    /**
     * 物料编码
     */
    private String materialNum;

    /**
     * 项目账名称
     */
    private String chargeName;

    /**
     * 产品类型名称
     */
    private String productTypeName;

    /**
     * 计收时间
     */
    private String billNoTime;

    /**
     * 业务员编码
     */
    private String couponSalesmanCode;

    /**
     * 业务员手机号
     */
    private String couponSalesmanPhone;

    /**
     * 业务员省工号
     */
    private String couponEmployeeId;

    /**
     * 业务员姓名
     */
    private String couponSalesmanName;

    /**
     * 账目项编码
     */
    private String chargeCode;

    /**
     * 账目项ID
     */
    private String chargeId;

    /**
     * 销售订单类型，0-标准产品省框，1-DICT服务包，2-统结标准产品
     */
    private String saleOrderType;

    /**
     * SCM采购订单编号
     */
    private String scmOrderNum;

    /**
     * 结算状态，0-销售草稿（锁定），1-销售草稿， 2-销售审批中， 3-销售已审批，
     * 4-计收完成， 5-订单取消 7--草稿单 8--审批中 9--已审批 10--取消中 11--已取消
     */
    private Integer settleStatus;

    private String settleStatusName;

    /**
     * 在线结算采购订单结算状态 6--待发起 7--草稿单 8--审批中 9--已审批 10--取消中 11--已取消  12--同步失败
     */
    private Integer onlineSettleStatus;

    private String onlineSettleStatusName;
}
