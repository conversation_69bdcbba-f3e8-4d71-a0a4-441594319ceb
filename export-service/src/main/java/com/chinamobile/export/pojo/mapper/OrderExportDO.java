package com.chinamobile.export.pojo.mapper;

import lombok.Data;

import java.util.Date;

/**
 * created by l<PERSON><PERSON><PERSON> on 2022/3/30 16:28
 * 导出订单列表数据的查询时，接收数据库查询结果
 */
@Data
public class OrderExportDO {

    /**
     * 订单ID
     */
    private String id;
    /**
     * 下单时间
     */
    private String createTime;
    /**
     * 订单号
     */
    private String orderId;

    /**
     * 商品组/销售商品名称
     */
    private String spuOfferingName;

    /**
     * 商品组/销售商品编码
     */
    private String spuOfferingCode;

    /**
     * 商品名称(规格)
     */
    private String skuOfferingName;

    /**
     * 商品规格编码
     */
    private String skuOfferingCode;

    /**
     * 原子商品名称
     */
    private String atomOfferingName;
    /**
     * 原子商品编码
     */
    private String atomOfferingCode;
    /**
     * 原子商品类型
     */
    private String atomOfferingClass;
    /**
     * 供应商名称
     */
    private String supplierName;
    /**
     * 型号
     */
    private String model;
    /**
     * 颜色
     */
    private String color;
    /**
     * 订购数量
     */
    private Integer quantity;
    /**
     * 原子商品结算价 单价
     */
    private Long atomPrice;
    /**
     * 合作伙伴ID
     */
    private String cooperatorId;
    /**
     * 订单完结合作伙伴ID
     */
    private String finishCooperatorId;
    /**
     * 订单完结合作伙伴名称
     */
    private String finishCooperatorName;
    /**
     * 合作伙伴姓名
     */
    private String cooperatorName;
    /**
     * 合作伙伴名
     */
    private String partnerName;
    /**
     * 订单状态 0待发货 1待收货 2已完成
     */
    private Integer orderStatus;
    /**
     * SPU一级销售目录
     */
    private String spuOfferingClass;

    /**
     * 订单生成月数据的主键id
     */
    private String syncK3Id;

    /**
     * 是否经过特殊的售后处理标识  0--未经过  1--经过
     */
    private Integer specialAfterMarketHandle;

    /**
     * 特殊的售后状态：1-待退款 2-退款中 3-退款成功 4-退款取消
     */
    private String specialAfterStatus;

    /**
     * 特殊的售后截止时间
     */
    private String specialAfterLatestTime;

    /**
     * 收货地区(省份)
     */
    private String deliveryArea;

    /**
     * 收货人电话
     */
    private String receiverPhone;

    /**
     * 计量单位
     */
    private String unit;

    /**
     * 原子结算单价
     */
    private Long settlePrice;

    /**
     * 操作员编码
     */
    private String createOperCode;

    /**
     * 操作员省工号
     */
    private String employeeNum;

    /**
     * 操作员姓名
     */
    private String custMgName;

    /**
     * 操作员电话
     */
    private String custMgPhone;

    /**
     * 客户编码
     */
    private String custCode;

    /**
     * 客户名称
     */
    private String custName;

    /**
     * 个人客户省份(后缀是移动)
     */
    private String province;

    /**
     * 个人客户所属归属地市编码
     */
    private String location;

    /**
     * 个人客户所属归属区县
     */
    private String regionID;

    /**
     * 物流单号，多个逗号分隔
     */
    private String logisCode;

    /**
     * 设备sn，多个逗号分隔
     */
    private String sn;

    /**
     * 收货人姓名
     */
    private String contactPersonName;

    /**
     * 收货人省（加密）
     */
    private String addr1;


    /**
     * 收货人市（加密）
     */
    private String addr2;

    /**
     * 收货人区（加密）
     */
    private String addr3;

    /**
     * 收货人乡镇（加密）
     */
    private String addr4;

    /**
     * 非结构地址(加密）
     */
    private String usaddr;

    /**
     * 组织机构名称，"-"分隔 ： 省-地市-区县-网格
     */
    private String orgName;

    /**
     * 订单抵扣金额(单位厘，已加密)
     */
    private String orderDeductPrice;

    /**
     * 原子订单抵扣金额(单位厘，已加密)
     */
    private String atomDeductPrice;

    /**
     * 订购数量（规格）
     */
    private Long skuQuantity;

    /**
     * 建议零售价（规格）
     */
    private Long recommendPrice;

    /**
     * 销售目录价（规格）
     */
    private Long price;

    /**
     * 营销案名称
     */
    private String marketName;

    /**
     * 营销案编码
     */
    private String marketCode;

    /**
     * 订单总金额（接口带来的，已加密）
     */
    private String orderTotalPrice;
    /**
     * 业务编码
     */
    private String businessCode;

    /**
     * 订单完成时间
     */
    private Date finishTime;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 关联领货码
     */
    private String couponInfo;

    /**
     * 订单类型  01-- 自主下单 00-- 代客下单
     */
    private String orderType;

    /**
     * 接单时间
     */
    private String receiveOrderTime;
    /**
     * 订购渠道名称
     */
    private String orderingChannelName;
    /**
     * 访问方式
     */
    private String ssoTerminalType;
    /**
     * 订单状态变更时间
     */
    private String orderStatusTime;
    /**
     * 实质产品名称
     */
    private String realProductName;

    /**
     * 标准服务编码
     */
    private String standardServiceCode;
    /**
     * 标准服务名称
     */
    private String standardServiceName;
    /**
     * 产品部门
     */
    private String department;
    /**
     * 产品属性
     */
    private String property;

    private String remark1;

    private String remark2;

    /**
     * 发货时间
     */
    private String sendGoodsTime;

    /**
     * 待出账时间(仅代客下单有值)
     */
    private String valetOrderCompleteTime;

    /**
     * 已收货时间
     */
    private String completeTime;

    /**
     * 销售商品版本号
     */
    private String spuOfferingVersion;

    /**
     * 规格商品版本号
     */
    private String skuOfferingVersion;

    /**
     * 原子商品版本号
     */
    private String atomOfferingVersion;

    /**
     * 一级分销员电话
     */
    private String distributorPhone1;

    /**
     * 二级分销员电话
     */
    private String distributorPhone2;

    /**
     * 一级分销员用户id
     */
    private String distributorUserId1;

    /**
     * 二级分销员用户id
     */
    private String distributorUserId2;

    /**
     * 渠道商id
     */
    private String distributorChannelId;

    /**
     * 渠道商名称
     */
    private String distributorChannelName;

    /**
     * 渠道商全称
     */
    private String agentName;

    /**
     * 渠道商编码
     */
    private String agentNumber;

    /**
     * 渠道商手机号
     */
    private String agentPhone;

    /**
     * 渠道商标签清洗
     */
    private String agentLabelWash;

    /**
     * 渠道商类别清洗
     */
    private String agentCategoryWash;


    /**
     * 网格市
     */
    private String griddingCity;

    /**
     * 网格区
     */
    private String griddingArea;


    /**
     * 网格名称
     */
    private String griddingName;

    /**
     * 物料编码
     */
    private String materialNum;

    /**
     * 项目账名称
     */
    private String chargeName;

    /**
     * 产品类型名称
     */
    private String productTypeName;


    /**
     * 专合结算价
     */
    private String specialCooperativeSettlementPrice;


    /**
     * 计收时间
     */
    private String billNoTime;

    /**
     * 业务员编码
     */
    private String couponSalesmanCode;

    /**
     * 业务员手机号
     */
    private String couponSalesmanPhone;

    /**
     * 业务员省工号
     */
    private String couponEmployeeId;

    /**
     * 业务员姓名
     */
    private String couponSalesmanName;

    /**
     * 账目项编码
     */
    private String chargeCode;

    /**
     * 账目项ID
     */
    private String chargeId;

    /**
     * 销售订单类型，0-标准产品省框，1-DICT服务包，2-统结标准产品
     */
    private String saleOrderType;

    /**
     * SCM采购订单编号
     */
    private String scmOrderNum;

    /**
     * 结算状态，0-销售草稿（锁定），1-销售草稿， 2-销售审批中， 3-销售已审批，
     * 4-计收完成， 5-订单取消 7--草稿单 8--审批中 9--已审批 10--取消中 11--已取消
     */
    private Integer settleStatus;

    /**
     * 线上结算采购订单结算状态 6--待发起 7--草稿单 8--审批中 9--已审批 10--取消中 11--已取消  12--同步失败
     */
    private Integer onlineSettleStatus;
}
