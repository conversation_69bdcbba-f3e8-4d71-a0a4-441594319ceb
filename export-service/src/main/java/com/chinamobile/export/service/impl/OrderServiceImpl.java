package com.chinamobile.export.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.chinamobile.export.config.ProvinceCityConfig;
import com.chinamobile.export.config.RegionAndLocationConstant;
import com.chinamobile.export.config.ServiceConfig;
import com.chinamobile.export.dao.ext.Order2cAtomInfoMapperExt;
import com.chinamobile.export.dao.ext.ShopCustomerInfoMapperExt;
import com.chinamobile.export.enums.*;
import com.chinamobile.export.exception.StatusConstant;
import com.chinamobile.export.pojo.dto.OrderExportDTO;
import com.chinamobile.export.pojo.dto.OrderScreenExportDTO;
import com.chinamobile.export.pojo.mapper.*;
import com.chinamobile.export.pojo.param.OrderExportParam;
import com.chinamobile.export.pojo.param.OrderScreenExportParam;
import com.chinamobile.export.service.IStorageService;
import com.chinamobile.export.service.OrderService;
import com.chinamobile.export.util.IOTEncodeUtils;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.BaseConstant;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.common.KafkaTopic;
import com.chinamobile.iot.sc.common.utils.RegexUtil;
import com.chinamobile.iot.sc.entity.ByteArrayUpload;
import com.chinamobile.iot.sc.entity.Msg4Request;
import com.chinamobile.iot.sc.entity.UpResult;
import com.chinamobile.iot.sc.enums.AtomOfferingClassEnum;
import com.chinamobile.iot.sc.enums.OnlineSettleStatusEnum;
import com.chinamobile.iot.sc.enums.SPUOfferingClassEnum;
import com.chinamobile.iot.sc.enums.SettleStatusEnum;
import com.chinamobile.iot.sc.enums.log.LogResultEnum;
import com.chinamobile.iot.sc.enums.log.ModuleEnum;
import com.chinamobile.iot.sc.enums.log.OrderManageOperateEnum;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.feign.IotFeignClient;
import com.chinamobile.iot.sc.feign.SmsFeignClient;
import com.chinamobile.iot.sc.feign.UserFeignClient;
import com.chinamobile.iot.sc.mode.AddMessageParam;
import com.chinamobile.iot.sc.mode.Data4User;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.service.LogService;
import com.chinamobile.iot.sc.util.DateTimeUtil;
import com.chinamobile.iot.sc.util.DateUtils;
import com.chinamobile.iot.sc.util.DesensitizationUtils;
import com.chinamobile.iot.sc.util.excel.EasyExcelUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.chinamobile.iot.sc.common.BaseConstant.ORDER_EXPORT_WITHOUT_PRICE_SO;
import static com.chinamobile.iot.sc.common.BaseConstant.ORDER_EXPORT_WITHOUT_RECEIVER;
import static com.chinamobile.iot.sc.exceptions.BaseErrorConstant.SUCCESS;

/**
 * created by liuxiang on 2023/4/17 17:09
 */
@Slf4j
@Service
public class OrderServiceImpl implements OrderService {

    private ExecutorService executorService = new ThreadPoolExecutor(8, 16, 1, TimeUnit.MINUTES, new LinkedBlockingQueue<>(100000));

    @Resource
    private Order2cAtomInfoMapperExt order2cAtomInfoMapperExt;

    @Autowired
    private UserFeignClient userFeignClient;

    @Autowired
    private ServiceConfig serviceConfig;

    @Autowired
    private LogService logService;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private IotFeignClient iotFeignClient;

    @Autowired
    private IStorageService storageService;

    @Autowired
    private SmsFeignClient smsFeignClient;

    @Autowired
    private KafkaTemplate<String, byte[]> kafkaTemplate;

    @Resource
    private ShopCustomerInfoMapperExt shopCustomerInfoMapperExt;

    @Autowired
    private ProvinceCityConfig provinceCityConfig;

    public static final String SHAN_DONG = "531";


    @Override
    @Transactional
    public void exportOrder(OrderExportParam param, String userId, LoginIfo4Redis loginIfo4Redis, String ip, String funcAuthJsonString) {
        String traceId = String.valueOf(System.currentTimeMillis());
        try {
            log.info("{}订单导出接口开始:{}", traceId, JSON.toJSONString(param));
            List<String> dataPermissionCodes = (List<String>) redisTemplate.opsForValue().get(Constant.REDIS_KEY_ROLE_DATA_PERMISSION_CODE + loginIfo4Redis.getRoleId());
            /*if (ObjectUtils.isEmpty(dataPermissionCodes) || (
                    !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_ORDER_SYSTEM)
                            && !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_ORDER_COMPANY)
                            && !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_ORDER_PERSONAL)
            )) {
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.ORDER_MANAGE.code, OrderManageOperateEnum.MALL_SYNC_ORDER.code,
                            exportContentLogFromRequest(param), userId, ip, LogResultEnum.LOG_FAIL.code, StatusConstant.UN_PERMISSION.getMessage());

                });
                throw new BusinessException(StatusConstant.UN_PERMISSION);
            }*/
            String orderId = param.getOrderId();
            List<Integer> orderStatus = param.getOrderStatus();
            String startTime = param.getStartTime();
            String endTime = param.getEndTime();
            List<String> spuOfferingClass = param.getSpuOfferingClass();
            String skuOfferingName = param.getSkuOfferingName();
            String skuOfferingCode = param.getSkuOfferingCode();
            String atomOfferingName = param.getAtomOfferingName();
            List<Integer> rocStatus = param.getRocStatus();
            List<String> deliveryArea = param.getDeliveryArea();
            String receiverPhone = param.getReceiverPhone();
            String finishStartTime = param.getFinishStartTime();
            String finishEndTime = param.getFinishEndTime();
            List<String> businessCode = param.getBusinessCode();
            Integer specialAfterMarketHandle = param.getSpecialAfterMarketHandle();
            String specialAfterStatus = param.getSpecialAfterStatus();
            String orderType = param.getOrderType();
            String valetOrderCompleteTimeStart = param.getValetOrderCompleteTimeStart();
            String valetOrderCompleteTimeEnd = param.getValetOrderCompleteTimeEnd();
            List<String> beId = param.getBeId();
            Integer onlineSettleStatus = param.getOnlineSettleStatus();

            /*Date startDate = null;
            Date endDate = null;
            Date finishStartDate = null;
            Date finishEndDate = null;
            Date valetOrderCompleteDateStart = null;
            Date valetOrderCompleteDateEnd = null;
            try {


                if (StringUtils.isNotEmpty(startTime) && StringUtils.isNotEmpty(endTime)) {
                    startDate = DateUtils.strToDate(startTime, DateUtils.DEFAULT_DATETIME_FORMAT);
                    endDate = DateUtils.strToDate(endTime, DateUtils.DEFAULT_DATETIME_FORMAT);
                } else if (StringUtils.isEmpty(valetOrderCompleteTimeEnd) && StringUtils.isEmpty(finishEndTime)) {
                    //没选择待出帐时间和订单完成时间的情况下，才有默认下单时间
                    endDate = new Date();
                    // 默认取过去一年
                    startDate = DateUtils.addMonth(endDate, -12);
                }

                if (StringUtils.isNotEmpty(finishStartTime) && StringUtils.isNotEmpty(finishEndTime)) {
                    finishStartDate = DateUtils.strToDate(finishStartTime, DateUtils.DEFAULT_DATETIME_FORMAT);
                    finishEndDate = DateUtils.strToDate(finishEndTime, DateUtils.DEFAULT_DATETIME_FORMAT);
                }

                if (StringUtils.isNotEmpty(valetOrderCompleteTimeStart) && StringUtils.isNotEmpty(valetOrderCompleteTimeEnd)) {
                    valetOrderCompleteDateEnd = DateUtils.strToDate(valetOrderCompleteTimeEnd, DateUtils.DEFAULT_DATETIME_FORMAT);
                    valetOrderCompleteDateStart = DateUtils.strToDate(valetOrderCompleteTimeStart, DateUtils.DEFAULT_DATETIME_FORMAT);
                }

            } catch (Exception e) {
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.ORDER_MANAGE.code, OrderManageOperateEnum.MALL_SYNC_ORDER.code,
                            exportContentLogFromRequest(param), userId, ip, LogResultEnum.LOG_FAIL.code, "日期格式错误");

                });
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "日期格式错误");
            }


            if(startDate != null && endDate != null){
                *//*if (endDate.getTime() - startDate.getTime() > 200 * 24 * 3600 * 1000L) {
                    executorService.execute(() -> {
                        logService.recordOperateLogAsync(ModuleEnum.ORDER_MANAGE.code, OrderManageOperateEnum.MALL_SYNC_ORDER.code,
                                exportContentLogFromRequest(param), userId,ip, LogResultEnum.LOG_FAIL.code,"下单时间跨度不能大于200天");

                    });
                    throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "下单时间跨度不能大于200天");
                }*//*
                long betweenMonth = DateUtil.betweenMonth(startDate, endDate, true);
                if (betweenMonth>12){
                    executorService.execute(() -> {
                        logService.recordOperateLogAsync(ModuleEnum.ORDER_MANAGE.code, OrderManageOperateEnum.MALL_SYNC_ORDER.code,
                                exportContentLogFromRequest(param), userId,ip, LogResultEnum.LOG_FAIL.code,"下单时间跨度不能大于200天");

                    });
                    throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "下单时间跨度不能大于1年");
                }
                if (startDate.after(endDate)) {
                    executorService.execute(() -> {
                        logService.recordOperateLogAsync(ModuleEnum.ORDER_MANAGE.code, OrderManageOperateEnum.MALL_SYNC_ORDER.code,
                                exportContentLogFromRequest(param), userId, ip, LogResultEnum.LOG_FAIL.code, "下单开始时间不能大于结束时间");

                    });
                    throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "下单开始时间不能大于结束时间");

                }
                startTime = DateTimeUtil.getDbTimeStr(startDate);
                endTime = DateTimeUtil.getDbTimeStr(endDate);
            }

            if (finishStartDate != null && finishEndDate != null) {
                if (finishStartDate.after(finishEndDate)) {
                    executorService.execute(() -> {
                        logService.recordOperateLogAsync(ModuleEnum.ORDER_MANAGE.code, OrderManageOperateEnum.MALL_SYNC_ORDER.code,
                                exportContentLogFromRequest(param), userId, ip, LogResultEnum.LOG_FAIL.code, "订单完成开始时间不能大于结束时间");

                    });
                    throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "订单完成开始时间不能大于结束时间");
                }
                if (finishEndDate.getTime() - finishStartDate.getTime() > 200 * 24 * 3600 * 1000L) {
                    executorService.execute(() -> {
                        logService.recordOperateLogAsync(ModuleEnum.ORDER_MANAGE.code, OrderManageOperateEnum.MALL_SYNC_ORDER.code,
                                exportContentLogFromRequest(param), userId,ip, LogResultEnum.LOG_FAIL.code,"订单完成时间跨度不能大于200天");

                    });
                    throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "订单完成时间跨度不能大于200天");
                }
            }

            if(valetOrderCompleteDateStart != null && valetOrderCompleteDateEnd != null){
                if (valetOrderCompleteDateStart.after(valetOrderCompleteDateEnd)) {
                    executorService.execute(() -> {
                        logService.recordOperateLogAsync(ModuleEnum.ORDER_MANAGE.code, OrderManageOperateEnum.MALL_SYNC_ORDER.code,
                                exportContentLogFromRequest(param), userId,ip, LogResultEnum.LOG_FAIL.code,"待出帐开始时间不能大于结束时间");

                    });
                    throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "待出帐开始时间不能大于结束时间");
                }
                if (valetOrderCompleteDateEnd.getTime() - valetOrderCompleteDateStart.getTime() > 200 * 24 * 3600 * 1000L) {
                    executorService.execute(() -> {
                        logService.recordOperateLogAsync(ModuleEnum.ORDER_MANAGE.code, OrderManageOperateEnum.MALL_SYNC_ORDER.code,
                                exportContentLogFromRequest(param), userId,ip, LogResultEnum.LOG_FAIL.code,"待出帐时间跨度不能大于200天");

                    });
                    throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "待出帐时间跨度不能大于200天");
                }
                valetOrderCompleteTimeStart = DateTimeUtil.getDbTimeStr(valetOrderCompleteDateStart);
                valetOrderCompleteTimeEnd = DateTimeUtil.getDbTimeStr(valetOrderCompleteDateEnd);
            }*/

            Date finishStartDate = DateUtils.strToDate(finishStartTime, DateUtils.DEFAULT_DATETIME_FORMAT);
            Date finishEndDate = DateUtils.strToDate(finishEndTime, DateUtils.DEFAULT_DATETIME_FORMAT);

            List<String> userIdList = new ArrayList<>();
            List<OrderExportDO> list = null;
            String roleType = loginIfo4Redis.getRoleType();
            final List<String> funcAuthCodes = JSON.parseArray(funcAuthJsonString, String.class);
            //超级管理员和运营管理员获取所有人订单,客服管理员也一样

            //用户手机号现在加密存储，所以涉及到手机号的匹配查询需要加密之后做完全匹配查询
            if (StringUtils.isNotBlank(receiverPhone)) {
                receiverPhone = IOTEncodeUtils.encryptSM4(receiverPhone,serviceConfig.getSm4Key(),serviceConfig.getSm4Iv());
            }
            String userBeId = "";
            String userLocation = "";
            if (dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_ORDER_SYSTEM)) {
                list = order2cAtomInfoMapperExt.selectOrderExportList(null,
                        orderId, orderStatus, startTime, endTime,
                        spuOfferingClass, skuOfferingName, skuOfferingCode,
                        atomOfferingName, rocStatus, receiverPhone,
                        businessCode, roleType, finishStartDate, finishEndDate,
                        specialAfterMarketHandle, specialAfterStatus, orderType,valetOrderCompleteTimeStart,
                        valetOrderCompleteTimeEnd,beId,userBeId,userLocation,onlineSettleStatus);
            } else if (dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_ORDER_COMPANY)) {
                if (roleType.equals(BaseConstant.PARTNER_LORD_ROLE)){
                    //合作伙伴主账号获取其下所有从账号的订单
                    BaseAnswer<List<String>> downUserIds = userFeignClient.getDownUserIds(userId);
                    if (CollectionUtil.isNotEmpty(downUserIds.getData())) {
                        userIdList = downUserIds.getData();
                    }
                    userIdList.add(userId);
                }else if (roleType.equals(BaseConstant.PARTNER_PROVINCE)){
                    // 省管配置权限为单位权限
                    BaseAnswer<Data4User> data4UserBaseAnswer = userFeignClient.userInfoById(userId);
                    if (data4UserBaseAnswer == null || !SUCCESS.getStateCode().equals(data4UserBaseAnswer.getStateCode())) {
                        throw new BusinessException("10004", "合作伙伴省管账号错误");
                    }

                    Data4User data4User = data4UserBaseAnswer.getData();
                    String companyType = data4User.getCompanyType();
                    boolean isProvinceUser = org.apache.commons.lang3.StringUtils.isNotEmpty(companyType) && "2".equals(companyType);
                    String currentUserLocation = data4User.getLocationIdPartner();
                    if (isProvinceUser){
                        if ("all".equals(currentUserLocation)) {
                            userBeId = data4User.getBeIdPartner();
                        } else {
                            userLocation = currentUserLocation;
                        }
                    }else {
                        throw new BusinessException(BaseErrorConstant.UN_PERMISSION);
                    }
                }else{
                    throw new BusinessException(BaseErrorConstant.UN_PERMISSION);
                }

                list = order2cAtomInfoMapperExt.selectOrderExportList(userIdList,
                        orderId, orderStatus, startTime, endTime,
                        spuOfferingClass, skuOfferingName, skuOfferingCode,
                        atomOfferingName, rocStatus, receiverPhone,
                        businessCode, roleType, finishStartDate, finishEndDate,
                        specialAfterMarketHandle, specialAfterStatus,orderType,
                        valetOrderCompleteTimeStart,valetOrderCompleteTimeEnd, beId,
                        userBeId,userLocation, onlineSettleStatus);
                // 将操作人电话和姓名置空
//                setCustNull(list);
                // 将版本号置空
                setVersionNull(list);
            } else if (dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_ORDER_PERSONAL)) {
                //合作伙伴从账号获取自己的订单
                userIdList.add(userId);
                list = order2cAtomInfoMapperExt.selectOrderExportList(userIdList,
                        orderId, orderStatus, startTime, endTime,
                        spuOfferingClass, skuOfferingName, skuOfferingCode,
                        atomOfferingName, rocStatus, receiverPhone,
                        businessCode, roleType, finishStartDate, finishEndDate,
                        specialAfterMarketHandle, specialAfterStatus, orderType,
                        valetOrderCompleteTimeStart,valetOrderCompleteTimeEnd, beId,
                        userBeId,userLocation,onlineSettleStatus);
                // 将操作人电话和姓名置空
//                setCustNull(list);
                // 将版本号置空
                setVersionNull(list);
            } else {
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.ORDER_MANAGE.code, OrderManageOperateEnum.MALL_SYNC_ORDER.code,
                            exportContentLogFromRequest(param), userId,ip, LogResultEnum.LOG_FAIL.code,StatusConstant.UN_PERMISSION.getMessage());

                });
                throw new BusinessException(BaseErrorConstant.UN_PERMISSION);
            }
            log.info("{}订单导出接口查询完毕",traceId);
            List<OrderExportDTO> exportList = new ArrayList<>();
            List<String> a11OrderIds = list.stream().filter(x->SPUOfferingClassEnum.A11.getSpuOfferingClass().equals(x.getSpuOfferingClass())).map(OrderExportDO::getOrderId).distinct().collect(Collectors.toList());

            // 获取2级分销员的渠道信息
            List<String> distributorUserId2List = list.stream()
                    .filter(orderExportDO -> StringUtils.isNotEmpty(orderExportDO.getDistributorUserId2()))
                    .map(OrderExportDO::getDistributorUserId2).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(distributorUserId2List)){
                List<DistributorChannelDO> distributorChannelInfo2List = shopCustomerInfoMapperExt.getDistributorChannelInfo(distributorUserId2List);
                Map<String,DistributorChannelDO> distributorChannelInfo2Map = new HashMap();
                if (CollectionUtil.isNotEmpty(distributorChannelInfo2List)){
                    for (DistributorChannelDO distributorChannelDO : distributorChannelInfo2List) {
                        distributorChannelInfo2Map.put(distributorChannelDO.getUserId(),distributorChannelDO);
                    }
                    list.stream().forEach(orderExportDO -> {
                        String distributorUserId2 = orderExportDO.getDistributorUserId2();
                        if (StringUtils.isNotEmpty(distributorUserId2)){
                            DistributorChannelDO distributorChannelDO = distributorChannelInfo2Map.get(distributorUserId2);
                            if (Optional.ofNullable(distributorChannelDO).isPresent()){
                                orderExportDO.setDistributorChannelId(distributorChannelDO.getDistributorChannelId());
                                orderExportDO.setDistributorChannelName(distributorChannelDO.getDistributorChannelName());
                            }
                        }
                    });
                }
            }

            // 获取1级分销员的渠道信息
            List<String> distributorUserId1List = list.stream()
                    .filter(orderExportDO -> StringUtils.isEmpty(orderExportDO.getDistributorUserId2()) && StringUtils.isNotEmpty(orderExportDO.getDistributorUserId1()))
                    .map(OrderExportDO::getDistributorUserId2).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(distributorUserId1List)){
                List<DistributorChannelDO> distributorChannelInfo1List = shopCustomerInfoMapperExt.getDistributorChannelInfo(distributorUserId1List);
                Map<String,DistributorChannelDO> distributorChannelInfo1Map = new HashMap();
                if (CollectionUtil.isNotEmpty(distributorChannelInfo1List)){
                    for (DistributorChannelDO distributorChannelDO : distributorChannelInfo1List) {
                        distributorChannelInfo1Map.put(distributorChannelDO.getUserId(),distributorChannelDO);
                    }
                    list.stream().forEach(orderExportDO -> {
                        String distributorUserId1 = orderExportDO.getDistributorUserId1();
                        String distributorUserId2 = orderExportDO.getDistributorUserId2();
                        if (StringUtils.isEmpty(distributorUserId2) &&
                                StringUtils.isNotEmpty(distributorUserId1)){
                            DistributorChannelDO distributorChannelDO = distributorChannelInfo1Map.get(distributorUserId1);
                            if (Optional.ofNullable(distributorChannelDO).isPresent()){
                                orderExportDO.setDistributorChannelId(distributorChannelDO.getDistributorChannelId());
                                orderExportDO.setDistributorChannelName(distributorChannelDO.getDistributorChannelName());
                            }
                        }
                    });
                }
            }

            // 判断是否需要脱敏操作
            Integer desensitizationStatus = param.getDesensitizationStatus();
            boolean isDesensitizationStatus = desensitizationStatus != null && desensitizationStatus == 1;

            list.forEach(orderExportHandle -> {
                OrderExportDTO dto = new OrderExportDTO();
                BeanUtils.copyProperties(orderExportHandle, dto);
                //数据库的日期格式是 yyyyMMddHHmmss,转换为 yyyy-MM-dd HH:mm:ss
                dto.setCreateTime(DateUtil.formatDateTime(DateUtil.parse(dto.getCreateTime())));
                dto.setReceiveOrderTime(DateUtil.formatDateTime(DateUtil.parse(dto.getReceiveOrderTime())));
                dto.setValetOrderCompleteTime(DateUtil.formatDateTime(DateUtil.parse(dto.getValetOrderCompleteTime())));

                //订购渠道名称转换
                List<String> channelOrderList = new ArrayList<>();
                channelOrderList.add("560100000002813154");
                channelOrderList.add("560100000002799148");
                channelOrderList.add("560100000002814151");
                channelOrderList.add("560100000002823146");
                channelOrderList.add("560100000002829141");
                channelOrderList.add("560100000002858167");
                channelOrderList.add("560100000002859155");
                channelOrderList.add("560100000002810152");
                channelOrderList.add("560100000002813153");
                channelOrderList.add("560100000002794161");
                channelOrderList.add("560100000002792161");
                channelOrderList.add("560100000002795148");
                channelOrderList.add("560100000002820148");
                channelOrderList.add("560100000002798171");
                channelOrderList.add("560100000002798170");
                if(channelOrderList.contains(dto.getOrderId())){
                    dto.setOrderingChannelName("省公司渠道");
                }

                if("其他".equals(dto.getOrderingChannelName())){
                    dto.setOrderingChannelName("省公司渠道");
                }

                //联合销售、合同履约,软件服务无接单环节，接单时间显示为下单时间。
                // 卡+x接单操作 20250608已加入订单操作历史记录表
                String spuClass = orderExportHandle.getSpuOfferingClass();
                if (SPUOfferingClassEnum.A06.getSpuOfferingClass().equals(spuClass) ||
                        SPUOfferingClassEnum.A07.getSpuOfferingClass().equals(spuClass) ||
//                        SPUOfferingClassEnum.A11.getSpuOfferingClass().equals(spuClass) ||
                        SPUOfferingClassEnum.A13.getSpuOfferingClass().equals(spuClass)
                       ) {
                    dto.setReceiveOrderTime(dto.getCreateTime());
                }

                //解析业务编码
                dto.setBusinessCode(BusinessCodeEnum.getChnName(dto.getBusinessCode()));
                Double atomPrice = orderExportHandle.getAtomPrice() != null ? orderExportHandle.getAtomPrice().doubleValue() : null;
//                dto.setAtomPrice(atomPrice);
                //计算原子总价总价，厘 转化为 元避免SQL语句复杂化
                if (atomPrice != null) {
//                    dto.setAtomPrice(dto.getAtomPrice() / 1000);
                    dto.setAtomTotalPrice((atomPrice / 1000) * dto.getQuantity());
                }
                dto.setSettlePrice(orderExportHandle.getSettlePrice() != null ? orderExportHandle.getSettlePrice().doubleValue() : null);
                if (dto.getSettlePrice() != null) {
                    dto.setSettlePrice(dto.getSettlePrice() / 1000);
                    dto.setTotalSettlePrice(dto.getSettlePrice() * dto.getQuantity());
                }

                dto.setSpuOfferingClass(SPUOfferingClassEnum.getDisplay(spuClass));
                dto.setOrderStatusDescribe(OrderStatusInnerEnum.getDescribe(orderExportHandle.getOrderStatus()));
                if (AtomOfferingClassEnum.S.name().equals(orderExportHandle.getAtomOfferingClass())) {
                    if(SPUOfferingClassEnum.A13.getSpuOfferingClass().equals(dto.getSpuOfferingClass())){
                        dto.setAtomOfferingClass("软件功能费");
                    }else{
                        dto.setAtomOfferingClass("软件");
                    }
                } else if (AtomOfferingClassEnum.H.name().equals(orderExportHandle.getAtomOfferingClass())) {
                    if (SPUOfferingClassEnum.A07.getSpuOfferingClass().equals(spuClass)) {
                        dto.setAtomOfferingClass("合同履约类硬件");
                    } else {
                        dto.setAtomOfferingClass("代销类硬件");
                    }

                } else if (AtomOfferingClassEnum.O.name().equals(orderExportHandle.getAtomOfferingClass())) {
//                    dto.setPartnerName(orderExportHandle.getSupplierName());
                    dto.setAtomOfferingClass(AtomOfferingClassEnum.O.getDescribe());
                } else if (AtomOfferingClassEnum.D.name().equals(orderExportHandle.getAtomOfferingClass())) {
//                    dto.setPartnerName(orderExportHandle.getSupplierName());
                    dto.setAtomOfferingClass(AtomOfferingClassEnum.D.getDescribe());
                } else if (AtomOfferingClassEnum.P.name().equals(orderExportHandle.getAtomOfferingClass())) {
//                    dto.setPartnerName(orderExportHandle.getSupplierName());
                    dto.setAtomOfferingClass(AtomOfferingClassEnum.P.getDescribe());
                } else if (AtomOfferingClassEnum.F.name().equals(orderExportHandle.getAtomOfferingClass())) {
//                    dto.setPartnerName(orderExportHandle.getSupplierName());
                    dto.setAtomOfferingClass(AtomOfferingClassEnum.F.getDescribe());
                } else if (AtomOfferingClassEnum.K.name().equals(orderExportHandle.getAtomOfferingClass())) {
//                    dto.setPartnerName(orderExportHandle.getSupplierName());
                    dto.setAtomOfferingClass(AtomOfferingClassEnum.K.getDescribe());
                } else if (AtomOfferingClassEnum.C.name().equals(orderExportHandle.getAtomOfferingClass())) {
//                    dto.setPartnerName(orderExportHandle.getSupplierName());
                    dto.setAtomOfferingClass(AtomOfferingClassEnum.C.getDescribe());
                } else if (AtomOfferingClassEnum.X.name().equals(orderExportHandle.getAtomOfferingClass())) {
//                    dto.setPartnerName(orderExportHandle.getSupplierName());
                    dto.setAtomOfferingClass(AtomOfferingClassEnum.X.getDescribe());
                }
                else if (AtomOfferingClassEnum.M.name().equals(orderExportHandle.getAtomOfferingClass())) {
                    dto.setAtomOfferingClass(AtomOfferingClassEnum.M.getDescribe());
                }
                else if (AtomOfferingClassEnum.A.name().equals(orderExportHandle.getAtomOfferingClass())) {

                    dto.setAtomOfferingClass(AtomOfferingClassEnum.A.getDescribe());
                }
                else if (AtomOfferingClassEnum.B.name().equals(orderExportHandle.getAtomOfferingClass())) {
                    dto.setAtomOfferingClass(AtomOfferingClassEnum.B.getDescribe());
                }
                else if (AtomOfferingClassEnum.E.name().equals(orderExportHandle.getAtomOfferingClass())) {
                    dto.setAtomOfferingClass(AtomOfferingClassEnum.E.getDescribe());
                }
                else if (AtomOfferingClassEnum.G.name().equals(orderExportHandle.getAtomOfferingClass())) {
                    dto.setAtomOfferingClass(AtomOfferingClassEnum.G.getDescribe());
                }

                //解密客户编码和名称
                dto.setCustCode(IOTEncodeUtils.decryptSM4(dto.getCustCode(), serviceConfig.getSm4Key(),serviceConfig.getSm4Iv()));
                dto.setCustName(IOTEncodeUtils.decryptSM4(dto.getCustName(), serviceConfig.getSm4Key(),serviceConfig.getSm4Iv()));
                //解密收货人电话
                dto.setReceiverPhone(IOTEncodeUtils.decryptSM4(dto.getReceiverPhone(), serviceConfig.getSm4Key(),serviceConfig.getSm4Iv()));
                //解密并过滤地区
                String encryptedArea = dto.getDeliveryArea();
                String decryptedArea = IOTEncodeUtils.decryptSM4(encryptedArea, serviceConfig.getSm4Key(),serviceConfig.getSm4Iv());
                dto.setDeliveryArea(decryptedArea);
                if (StringUtils.isNotEmpty(decryptedArea)){
                    if (CollectionUtils.isEmpty(deliveryArea)) {
                        exportList.add(dto);
                    } else if (deliveryArea.stream().anyMatch(x -> decryptedArea.contains(x) || x.contains(decryptedArea))) {
                        exportList.add(dto);
                    }
                }else{
                    if (CollectionUtils.isEmpty(deliveryArea)) {
                        exportList.add(dto);
                    }
                }
                if (orderExportHandle.getFinishCooperatorId() != null
                        && !orderExportHandle.getFinishCooperatorId().equals(orderExportHandle.getCooperatorId())) {
                    if (OrderStatusInnerEnum.ORDER_SUCCESS.getStatus().equals(orderExportHandle.getOrderStatus())
                            || OrderStatusInnerEnum.ORDER_FAIL.getStatus().equals(orderExportHandle.getOrderStatus())) {
                        dto.setCooperatorName(orderExportHandle.getFinishCooperatorName());
                    }
                }
                //个人客户所属省份，去掉“移动”后缀
                String province = dto.getProvince();
                if (province != null && province.contains("移动")) {
                    dto.setProvince(province.substring(0, province.indexOf("移动")));
                }
                // 超管，运管，合作伙伴主从账号，客服 显示收货人姓名和地址，其他不显示
                String contactPersonName = dto.getContactPersonName();
                if (!CollectionUtil.contains(funcAuthCodes, ORDER_EXPORT_WITHOUT_RECEIVER)) {
                    //解析加密的省市区镇，获得收货地址
                    String decryptedAddr1 = IOTEncodeUtils.decryptSM4(orderExportHandle.getAddr1(), serviceConfig.getSm4Key(),serviceConfig.getSm4Iv());
                    String decryptedAddr2 = IOTEncodeUtils.decryptSM4(orderExportHandle.getAddr2(), serviceConfig.getSm4Key(),serviceConfig.getSm4Iv());
                    String decryptedAddr3 = IOTEncodeUtils.decryptSM4(orderExportHandle.getAddr3(), serviceConfig.getSm4Key(),serviceConfig.getSm4Iv());
                    String decryptedAddr4 = IOTEncodeUtils.decryptSM4(orderExportHandle.getAddr4(), serviceConfig.getSm4Key(),serviceConfig.getSm4Iv());
                    String decryptedUsaddr = IOTEncodeUtils.decryptSM4(orderExportHandle.getUsaddr(), serviceConfig.getSm4Key(),serviceConfig.getSm4Iv());
                    String address = (StringUtils.isEmpty(decryptedAddr1) ? "" : decryptedAddr1) +
                            (StringUtils.isEmpty(decryptedAddr2) ? "" : decryptedAddr2) +
                            (StringUtils.isEmpty(decryptedAddr3) ? "" : decryptedAddr3) +
                            (StringUtils.isEmpty(decryptedAddr4) ? "" : decryptedAddr4) +
                            (StringUtils.isEmpty(decryptedUsaddr) ? "" : decryptedUsaddr);
                    dto.setAddress(address);

                    //解密收货人姓名
                    dto.setContactPersonName(IOTEncodeUtils.decryptSM4(contactPersonName, serviceConfig.getSm4Key(),serviceConfig.getSm4Iv()));
                } else {
                    dto.setAddress("-");
                    dto.setContactPersonName("-");
                }

                //根据编码获取地市和区县名称
                dto.setRegionID((String) RegionAndLocationConstant.REGION_MAP.get(dto.getRegionID()));
                dto.setLocation((String) RegionAndLocationConstant.LOCATION_MAP.get(dto.getLocation()));

                //抵扣金额解密和赋值
                String orderDeductPrice = IOTEncodeUtils.decryptSM4(orderExportHandle.getOrderDeductPrice(), serviceConfig.getSm4Key(),serviceConfig.getSm4Iv());
//                String atomDeductPrice = IOTEncodeUtils.decryptSM4()(orderExportHandle.getAtomDeductPrice(), encodeKey);
                if (StringUtils.isNotEmpty(orderDeductPrice)) {
                    dto.setOrderDeductPrice(Double.parseDouble(orderDeductPrice) / 1000);
                }
                /*if (StringUtils.isNotEmpty(atomDeductPrice)) {
                    dto.setAtomDeductPrice(Double.parseDouble(atomDeductPrice) / 1000);
                }*/

                //订单计收信息
                String orgName = orderExportHandle.getOrgName();
                if (StringUtils.isNotEmpty(orgName)) {
                    String[] split = orgName.split("-");
                    dto.setOrgProvince(split[0]);
                    if (split.length >= 2) {
                        dto.setOrgLocation(split[1]);
                        if (split.length >= 3) {
                            dto.setOrgRegion(split[2]);
//                            if (split.length >= 4) {
//                                dto.setOrgHall(split[3]);
//                            }
                        }
                    }
                }

//                dto.setRecommendPrice(orderExportHandle.getRecommendPrice() == null ? null : orderExportHandle.getRecommendPrice().doubleValue());
                dto.setPrice(orderExportHandle.getPrice() == null ? null : orderExportHandle.getPrice().doubleValue());
//                if (dto.getRecommendPrice() != null) {
//                    dto.setRecommendPrice(dto.getRecommendPrice() / 1000);
//                }
                if (dto.getPrice() != null) {
                    dto.setPrice(dto.getPrice() / 1000);
                }
                if (StringUtils.isNotEmpty(orderExportHandle.getOrderTotalPrice())) {
                    dto.setOrderTotalPrice(Double.valueOf(IOTEncodeUtils.decryptSM4(orderExportHandle.getOrderTotalPrice(), serviceConfig.getSm4Key(),serviceConfig.getSm4Iv())) / 1000);
                }

                // “专合结算价”，【标准服务编码】、【标准服务名称】、【产品部门】、【产品属性】、【备注1】、【备注2】
                // 导出后仅OS管理员显示字段值，合作伙伴不显示字段值。
                if (!dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_ORDER_SYSTEM)) {
                    dto.setSpecialCooperativeSettlementPrice("");
                    dto.setStandardServiceCode("");
                    dto.setStandardServiceName("");
                    dto.setDepartment("");
                    dto.setProperty("");
                    dto.setRemark1("");
                    dto.setRemark2("");
                }

                //有些字段只有超管，运管和客服可以查看
                if (CollectionUtil.contains(funcAuthCodes, ORDER_EXPORT_WITHOUT_PRICE_SO)) {
                    /*dto.setOrgProvince(null);
                    dto.setOrgLocation(null);
                    dto.setOrgRegion(null);*/
//                    dto.setOrgHall(null);
                    dto.setOrderTotalPrice(null);
                    dto.setOrderDeductPrice(null);
                    dto.setSkuQuantity(null);
//                    dto.setRecommendPrice(null);
                    dto.setPrice(null);
                    dto.setReceiveOrderTime(null);
                    dto.setOrderingChannelName(null);
                    dto.setSsoTerminalType(null);
                    dto.setOrderStatusTime(null);
                    dto.setSupplierName(null);
                    dto.setValetOrderCompleteTime(null);
//                    dto.setMarketCode(null);
//                    dto.setMarketName(null);
//                    dto.setAtomDeductPrice(null);
                }

                // 订单完成时间
                Date finishTime = orderExportHandle.getFinishTime();
                if (Optional.ofNullable(finishTime).isPresent()) {
                    dto.setFinishTime(DateUtils.dateToStr(finishTime, DateUtils.DEFAULT_DATETIME_FORMAT));
                }
                OrderTypeEnum orderTypeEnum = OrderTypeEnum.fromCode(dto.getOrderType());
                dto.setOrderType(orderTypeEnum == null ? OrderTypeEnum.normal.desc : orderTypeEnum.desc);
                if (StringUtils.isEmpty(dto.getSsoTerminalType())) {
                    //默认是PC登录方式
                    dto.setSsoTerminalType(SsoTerminalTypeEnum.PC.code);
                }
                dto.setSsoTerminalType(SsoTerminalTypeEnum.getDesc(dto.getSsoTerminalType()));

                Integer settleStatusDTO = dto.getSettleStatus();
                if (settleStatusDTO != null){
                    dto.setSettleStatusName(SettleStatusEnum.getDescByCode(settleStatusDTO));
                }

                Integer onlineSettleStatusDTO = dto.getOnlineSettleStatus();
                if (onlineSettleStatusDTO != null){
                    dto.setOnlineSettleStatusName(OnlineSettleStatusEnum.getDescByStatus(onlineSettleStatusDTO+""));
                }

                // 如果需要脱敏操作
                if (isDesensitizationStatus){
                    // 收货人姓名
                    if (StringUtils.isNotEmpty(contactPersonName)
                     && !"-".equals(contactPersonName)){
                        dto.setContactPersonName(DesensitizationUtils.smartDesensitizeName(contactPersonName));
                    }

                    // 收货人地址
                    String address = dto.getAddress();
                    if (StringUtils.isNotEmpty(address)
                    && !"-".equals(address)){
                        dto.setAddress(DesensitizationUtils.maskAddress(address));
                    }

                    // 收货人电话
                    String receiverPhoneDTO = dto.getReceiverPhone();
                    if (StringUtils.isNotEmpty(receiverPhoneDTO)
                    && !"-".equals(receiverPhoneDTO)){
                        dto.setReceiverPhone(DesensitizationUtils.replaceWithStar(receiverPhoneDTO));
                    }

                    // 操作员姓名
                    String custMgName = dto.getCustMgName();
                    if (StringUtils.isNotEmpty(custMgName)
                    && !"-".equals(custMgName)){
                        dto.setCustMgName(DesensitizationUtils.smartDesensitizeName(custMgName));
                    }

                    // 操作员电话
                    String custMgPhone = dto.getCustMgPhone();
                    if (StringUtils.isNotEmpty(custMgPhone)
                    && !"-".equals(custMgPhone)){
                        dto.setCustMgPhone(DesensitizationUtils.replaceWithStar(custMgPhone));
                    }

                    // 渠道商手机号
                    String agentPhone = dto.getAgentPhone();
                    if (StringUtils.isNotEmpty(agentPhone)
                    && !"-".equals(agentPhone)){
                        dto.setAgentPhone(DesensitizationUtils.replaceWithStar(agentPhone));
                    }

                    // 业务人员手机号
                    String couponSalesmanPhone = dto.getCouponSalesmanPhone();
                    if (StringUtils.isNotEmpty(couponSalesmanPhone)
                    && !"-".equals(couponSalesmanPhone)){
                        String[] couponSalesmanPhoneSplit = couponSalesmanPhone.split(",");
                        if (couponSalesmanPhoneSplit.length > 1){
                            String desensitizationCouponSalesmanPhone = "";
                            for (int i = 0; i < couponSalesmanPhoneSplit.length; i++) {
                                desensitizationCouponSalesmanPhone.concat(DesensitizationUtils.replaceWithStar(couponSalesmanPhoneSplit[i]));
                                if (i < couponSalesmanPhoneSplit.length){
                                    desensitizationCouponSalesmanPhone.concat(",");
                                }
                            }
                        }else{
                            dto.setCouponSalesmanPhone(DesensitizationUtils.replaceWithStar(couponSalesmanPhone));
                        }
                    }

                    // 业务人员姓名
                    String couponSalesmanName = dto.getCouponSalesmanName();
                    if (StringUtils.isNotEmpty(couponSalesmanName)
                    && !"-".equals(couponSalesmanName)){
                        String[] couponSalesmanNameSplit = couponSalesmanName.split(",");
                        if (couponSalesmanNameSplit.length > 1){
                            String desensitizationCouponSalesmanName = "";
                            for (int i = 0; i < couponSalesmanNameSplit.length; i++) {
                                desensitizationCouponSalesmanName.concat(DesensitizationUtils.smartDesensitizeName(couponSalesmanNameSplit[i]));
                                if (i < couponSalesmanNameSplit.length){
                                    desensitizationCouponSalesmanName.concat(",");
                                }
                            }
                        }else{
                            dto.setCouponSalesmanName(DesensitizationUtils.smartDesensitizeName(couponSalesmanName));
                        }
                    }

                    // 客户名称
                    String custName = dto.getCustName();
                    if (StringUtils.isNotEmpty(custName)
                    && !"-".equals(custName)){
                        String[] custNameSplit = custName.split(",");
                        if (custNameSplit.length > 1){
                            String desensitizationCustName = "";
                            for (int i = 0; i < custNameSplit.length; i++) {
                                desensitizationCustName.concat(DesensitizationUtils.smartDesensitizeName(custNameSplit[i]));
                                if (i < custNameSplit.length){
                                    desensitizationCustName.concat(",");
                                }
                            }
                        }
                        dto.setCustName(DesensitizationUtils.smartDesensitizeName(custName));
                    }

                }

            });


            //(只有页面上选择山东，此数据才取值)拼装客户custId（手机号）,由于订单表custCode是加密保存，不能联表查询
            //TODO 现在所有的都展示个人客户手机号
          //  if(SHAN_DONG.equals(beId)){
                Map<String,String> custCodeCustIdMap = new HashMap<>();
                Set<String> custCodeSet = exportList.stream().map(e -> {
                    return e.getCustCode();
                }).collect(Collectors.toSet());
                List<CustCodeCustIdDO> custCodeCustIdList = shopCustomerInfoMapperExt.custCodeCustIdList(new ArrayList<>(custCodeSet));
                if(CollectionUtils.isNotEmpty(custCodeCustIdList)){
                    custCodeCustIdList.forEach(c -> {
                        custCodeCustIdMap.put(c.getCustCode(),c.getCustId());
                    });
                    for (OrderExportDTO dto : exportList) {
                        String custId = custCodeCustIdMap.get(dto.getCustCode());
                        if (StringUtils.isNotEmpty(custId) && !"-".equals(custId)){
                            String [] custIdSplit = custId.split(",");
                            if (custIdSplit.length > 1){
                                String desensitizationCustId = "";
                                for (int i = 0; i < custIdSplit.length; i++) {
                                    if (isDesensitizationStatus){
                                        desensitizationCustId = desensitizationCustId.concat(DesensitizationUtils.replaceWithStar(custIdSplit[i]));
                                    }else {
                                        desensitizationCustId = desensitizationCustId.concat(custIdSplit[i]);
                                    }
                                    if (i < custIdSplit.length){
                                        desensitizationCustId = desensitizationCustId.concat(",");
                                    }
                                }
                                dto.setCustId(desensitizationCustId);
                            }else{
                                if (isDesensitizationStatus){
                                    dto.setCustId(DesensitizationUtils.replaceWithStar(custId));
                                }else{
                                    dto.setCustId(custId);
                                }
                            }
                        }
                    }
                }
          //  }



            if (exportList.isEmpty()) {
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.ORDER_MANAGE.code, OrderManageOperateEnum.MALL_SYNC_ORDER.code,
                            exportContentLogFromRequest(param), userId, ip, LogResultEnum.LOG_FAIL.code, StatusConstant.ORDER_NOT_EXIST.getMessage());

                });
                throw new BusinessException(StatusConstant.ORDER_NOT_EXIST);
            }

            final List<OrderExportDTO> exportWithCard = new ArrayList<>();
            boolean hasCard = false;
            //查询订单中A11订单的增值服务包信息

            if (CollectionUtils.isNotEmpty(a11OrderIds)) {
                //添加增值服务包作为原子订单
                List<CardValueAddedInfoDO> cardValueAddedInfoDOS = order2cAtomInfoMapperExt.selectCardValueAddedExportList(a11OrderIds);
                if (CollectionUtils.isNotEmpty(cardValueAddedInfoDOS)) {
                    hasCard= true;
                    List<OrderExportDTO> sortExportList = exportList.stream().sorted(Comparator.comparing(OrderExportDTO::getOrderId).thenComparing(OrderExportDTO::getAtomOfferingClass)).collect(Collectors.toList());
                    Map<String, List<OrderExportDTO>> orderMap = sortExportList.stream().collect(Collectors.groupingBy(OrderExportDTO::getOrderId, LinkedHashMap::new, Collectors.toList()));
                    Map<String, List<CardValueAddedInfoDO>> cardMap = cardValueAddedInfoDOS.stream().collect(Collectors.groupingBy(CardValueAddedInfoDO::getOrderId, LinkedHashMap::new, Collectors.toList()));
                    orderMap.forEach((x,orderList) -> {
                        List<CardValueAddedInfoDO> cards = cardMap.get(x);
                        if (CollectionUtils.isNotEmpty(cards)) {
//                            Double totalAdd = cards.stream().filter(c-> c.getExpensesPrice() !=null && c.getExpensesTerm()!=null && c.getOrderQuantity()!=null)
//                            .mapToDouble(c -> Double.parseDouble(c.getExpensesPrice())
//                                    * Double.parseDouble(c.getExpensesTerm()) * Double.parseDouble(c.getOrderQuantity()) ).sum() / 1000;
                            Double cardPrice = cards.stream().filter(c-> c.getExpensesPrice() !=null && c.getExpensesTerm()!=null && c.getOrderQuantity()!=null)
                            .mapToDouble(c -> Double.parseDouble(c.getExpensesPrice())
                                    * Double.parseDouble(c.getExpensesTerm())  ).sum() /1000;
                      //      orderList.forEach(o -> o.setPrice(o.getPrice() != null ? o.getPrice() + totalAdd : totalAdd));
                            orderList.forEach(o -> o.setPrice(o.getPrice() != null ? o.getPrice() + cardPrice : cardPrice));
                            cards.forEach(c -> {
                                OrderExportDTO orderExportDTO = new OrderExportDTO();
                                BeanUtils.copyProperties(orderList.get(0),orderExportDTO);
                                orderExportDTO.setAtomOfferingName(c.getOfferingName());
                                orderExportDTO.setAtomOfferingClass(c.getOfferingType());
                                orderExportDTO.setQuantity(Integer.parseInt(c.getOrderQuantity()));
                                if (c.getExpensesPrice() !=null && c.getExpensesTerm()!=null && c.getOrderQuantity()!=null) {
                                    orderExportDTO.setAtomTotalPrice(Double.parseDouble(c.getExpensesPrice())
                                            * Double.parseDouble(c.getExpensesTerm()) * Double.parseDouble(c.getOrderQuantity()) / 1000);
                                }
                                orderExportDTO.setAtomOfferingCode(null);
                                orderExportDTO.setSettlePrice(null);
                                orderExportDTO.setAtomOfferingVersion(null);
                                orderExportDTO.setMaterialNum(null);
                                orderExportDTO.setChargeName(null);
                                orderExportDTO.setProductTypeName(null);
                                orderExportDTO.setTotalSettlePrice(null);

                                orderList.add(orderExportDTO);
                            });
                        }
                        exportWithCard.addAll(orderList);
                    });
                }
            }

            //导出excel
            ByteArrayOutputStream bytearrayOutputStream = new ByteArrayOutputStream();
            ClassPathResource classPathResource = new ClassPathResource("template/order-export-template.xlsx");
            if (dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_ORDER_SYSTEM)) {

            }
            // 合作伙伴主账号或省管
            /*if (dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_ORDER_COMPANY)) {
                classPathResource = new ClassPathResource("template/order-export-primary-cooperator-template.xlsx");
            }

            // 合作伙伴从账号
            if (dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_ORDER_PERSONAL)) {
                classPathResource = new ClassPathResource("template/order-export-down-cooperator-template.xlsx");
            }*/
            InputStream inputStream = classPathResource.getInputStream();
            String password = param.getExportPwd();
            try {
                log.info("{}订单导出准备构造和加密excel", traceId);
                EasyExcelUtils.exportExcel2OutputStream(bytearrayOutputStream, "list", hasCard? exportWithCard : exportList, null, inputStream, 0, "订单导出", null, null, password,false);
                log.info("{}订单导出接口构造和加密excel完毕", traceId);
            } catch (Exception e) {
                log.error("{}构建excel出错", traceId, e);
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.ORDER_MANAGE.code, OrderManageOperateEnum.MALL_SYNC_ORDER.code,
                            exportContentLogFromRequest(param), userId, ip, LogResultEnum.LOG_FAIL.code, "构建excel出错,请联系管理员");

                });
                throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, "构建excel出错,请联系管理员");
            }

            //上传文件到对象存储
            String fileName = "orderExport" + new Date().getTime() + ".xlsx";
            ByteArrayUpload byteArrayUpload = new ByteArrayUpload();
            byteArrayUpload.setFileName(fileName);
            byteArrayUpload.setBytes(bytearrayOutputStream.toByteArray());
            BaseAnswer<UpResult> resultBaseAnswer = storageService.uploadByte(byteArrayUpload);
            if (!"00000".equals(resultBaseAnswer.getStateCode())) {
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.ORDER_MANAGE.code, OrderManageOperateEnum.MALL_SYNC_ORDER.code,
                            exportContentLogFromRequest(param), userId, ip, LogResultEnum.LOG_FAIL.code, "上传文件出错,请联系管理员");

                });
                throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, "上传文件出错,请联系管理员");
            }
            log.info("{}订单导出接口上传excel完毕", traceId);

            //消息中心提醒用户
            UpResult upResult = resultBaseAnswer.getData();
            String outerUrl = upResult.getOuterUrl();
            String fileKey = upResult.getKey();
            AddMessageParam messageParam = new AddMessageParam();
            messageParam.setModule(ModuleEnum.ORDER_MANAGE.name);
            messageParam.setContent("订单导出成功，请点击下载\n下载有效期" + serviceConfig.getOrderExportExcelExpireDays() + "天，请尽快处理");
            messageParam.setType("订单导出");
            messageParam.setUserId(userId);
            messageParam.setFileKey(fileKey);
            messageParam.setUrl(outerUrl);
            messageParam.setSource(1);
            iotFeignClient.addMessage(messageParam);

            //通过kafka发送websocket提示前端更新消息未读数
            ProducerRecord<String, byte[]> record = new ProducerRecord<>(KafkaTopic.SUPPLY_CHAIN_WEBSOCKET.getTopic(), "/message/monitor".getBytes());
            kafkaTemplate.send(record);

            //发送短信提示用户
            String phone = loginIfo4Redis.getPhone();
            if (StringUtils.isNotEmpty(phone) && RegexUtil.regexPhone(phone)) {
                Msg4Request msg4Request = new Msg4Request();
                msg4Request.setTemplateId(serviceConfig.getNewMessageSms());
                List<String> mobiles = new ArrayList<>();
                mobiles.add(phone);
                msg4Request.setMobiles(mobiles);
                smsFeignClient.asySendMessage(msg4Request);
            }
           /* File file = new File("aa.xlsx");
            FileOutputStream fileOutputStream = new FileOutputStream(file);
            InputStream bufferedInputStream = new BufferedInputStream(new ByteArrayInputStream(bytearrayOutputStream.toByteArray()));
            byte[] buff = new byte[1024];
            for (int i = bufferedInputStream.read(buff); i != -1; i = bufferedInputStream.read(buff)) {
                fileOutputStream.write(buff, 0, buff.length);
                fileOutputStream.flush();
            }
            fileOutputStream.close();
            bufferedInputStream.close();*/
            //记录订单导出日志,异步进行
            executorService.execute(() -> {
                logService.recordOperateLogAsync(ModuleEnum.ORDER_MANAGE.code, OrderManageOperateEnum.MALL_SYNC_ORDER.code,
                        exportContentLogFromRequest(param), userId, ip, LogResultEnum.LOG_SUCESS.code, null);
                log.info("{}订单导出接口记录日志完毕", traceId);
            });
        } catch (Exception e) {
            String content = null;
            //出现异常时，保存到消息中心
            if (e instanceof BusinessException) {
                BusinessException businessException = (BusinessException) e;
                content = "导出订单失败:" + businessException.getStatus().getMessage();
            } else {
                log.error("{}导出订单发生异常,", traceId, e);
                content = "导出订单失败,请联系管理员";
            }
            AddMessageParam messageParam = new AddMessageParam();
            messageParam.setModule(ModuleEnum.ORDER_MANAGE.name);
            messageParam.setContent(content);
            messageParam.setType("订单导出");
            messageParam.setUserId(userId);
            messageParam.setSource(1);
            iotFeignClient.addMessage(messageParam);

            //通过kafka发送websocket提示前端更新消息未读数
            ProducerRecord<String, byte[]> record = new ProducerRecord<>(KafkaTopic.SUPPLY_CHAIN_WEBSOCKET.getTopic(), "/message/monitor".getBytes());
            kafkaTemplate.send(record);
        }
    }

    @Override
    public Integer getOrderExportCount(OrderExportParam param, String userId,
                                       LoginIfo4Redis loginIfo4Redis, String ip) {

        List<String> dataPermissionCodes = (List<String>) redisTemplate.opsForValue().get(Constant.REDIS_KEY_ROLE_DATA_PERMISSION_CODE + loginIfo4Redis.getRoleId());
        if (ObjectUtils.isEmpty(dataPermissionCodes) || (
                !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_ORDER_SYSTEM)
                        && !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_ORDER_COMPANY)
                        && !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_ORDER_PERSONAL)
        )) {
           /* executorService.execute(() -> {
                logService.recordOperateLogAsync(ModuleEnum.ORDER_MANAGE.code, OrderManageOperateEnum.MALL_SYNC_ORDER.code,
                        exportContentLogFromRequest(param), userId, ip, LogResultEnum.LOG_FAIL.code, StatusConstant.UN_PERMISSION.getMessage());

            });*/
            throw new BusinessException(StatusConstant.UN_PERMISSION);
        }
        String orderId = param.getOrderId();
        List<Integer> orderStatus = param.getOrderStatus();
        String startTime = param.getStartTime();
        String endTime = param.getEndTime();
        List<String> spuOfferingClass = param.getSpuOfferingClass();
        String skuOfferingName = param.getSkuOfferingName();
        String skuOfferingCode = param.getSkuOfferingCode();
        String atomOfferingName = param.getAtomOfferingName();
        List<Integer> rocStatus = param.getRocStatus();
        List<String> deliveryArea = param.getDeliveryArea();
        String receiverPhone = param.getReceiverPhone();
        String finishStartTime = param.getFinishStartTime();
        String finishEndTime = param.getFinishEndTime();
        List<String> businessCode = param.getBusinessCode();
        Integer specialAfterMarketHandle = param.getSpecialAfterMarketHandle();
        String specialAfterStatus = param.getSpecialAfterStatus();
        String orderType = param.getOrderType();
        String valetOrderCompleteTimeStart = param.getValetOrderCompleteTimeStart();
        String valetOrderCompleteTimeEnd = param.getValetOrderCompleteTimeEnd();
        List<String> beId = param.getBeId();
        Integer onlineSettleStatus = param.getOnlineSettleStatus();

        Date startDate = null;
        Date endDate = null;
        Date finishStartDate = null;
        Date finishEndDate = null;
        Date valetOrderCompleteDateStart = null;
        Date valetOrderCompleteDateEnd = null;
        try {


            if (StringUtils.isNotEmpty(startTime) && StringUtils.isNotEmpty(endTime)) {
                startDate = DateUtils.strToDate(startTime, DateUtils.DEFAULT_DATETIME_FORMAT);
                endDate = DateUtils.strToDate(endTime, DateUtils.DEFAULT_DATETIME_FORMAT);
            } else if (StringUtils.isEmpty(valetOrderCompleteTimeEnd) && StringUtils.isEmpty(finishEndTime)) {
                //没选择待出帐时间和订单完成时间的情况下，才有默认下单时间
                endDate = new Date();
                // 默认取过去一年
                startDate = DateUtils.addMonth(endDate, -12);
            }

            if (StringUtils.isNotEmpty(finishStartTime) && StringUtils.isNotEmpty(finishEndTime)) {
                finishStartDate = DateUtils.strToDate(finishStartTime, DateUtils.DEFAULT_DATETIME_FORMAT);
                finishEndDate = DateUtils.strToDate(finishEndTime, DateUtils.DEFAULT_DATETIME_FORMAT);
            }

            if (StringUtils.isNotEmpty(valetOrderCompleteTimeStart) && StringUtils.isNotEmpty(valetOrderCompleteTimeEnd)) {
                valetOrderCompleteDateEnd = DateUtils.strToDate(valetOrderCompleteTimeEnd, DateUtils.DEFAULT_DATETIME_FORMAT);
                valetOrderCompleteDateStart = DateUtils.strToDate(valetOrderCompleteTimeStart, DateUtils.DEFAULT_DATETIME_FORMAT);
            }

        } catch (Exception e) {
            /*executorService.execute(() -> {
                logService.recordOperateLogAsync(ModuleEnum.ORDER_MANAGE.code, OrderManageOperateEnum.MALL_SYNC_ORDER.code,
                        exportContentLogFromRequest(param), userId, ip, LogResultEnum.LOG_FAIL.code, "日期格式错误");

            });*/
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "日期格式错误");
        }


        if(startDate != null && endDate != null){
            /*if (endDate.getTime() - startDate.getTime() > 200 * 24 * 3600 * 1000L) {
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.ORDER_MANAGE.code, OrderManageOperateEnum.MALL_SYNC_ORDER.code,
                            exportContentLogFromRequest(param), userId,ip, LogResultEnum.LOG_FAIL.code,"下单时间跨度不能大于200天");

                });
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "下单时间跨度不能大于200天");
            }*/
            long betweenMonth = DateUtil.betweenMonth(startDate, endDate, true);
            if (betweenMonth>12){
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "下单时间跨度不能大于1年");
            }
            if (startDate.after(endDate)) {
                /*executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.ORDER_MANAGE.code, OrderManageOperateEnum.MALL_SYNC_ORDER.code,
                            exportContentLogFromRequest(param), userId, ip, LogResultEnum.LOG_FAIL.code, "下单开始时间不能大于结束时间");

                });*/
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "下单开始时间不能大于结束时间");

            }
            startTime = DateTimeUtil.getDbTimeStr(startDate);
            endTime = DateTimeUtil.getDbTimeStr(endDate);
        }

        if (finishStartDate != null && finishEndDate != null) {
            if (finishStartDate.after(finishEndDate)) {
               /* executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.ORDER_MANAGE.code, OrderManageOperateEnum.MALL_SYNC_ORDER.code,
                            exportContentLogFromRequest(param), userId, ip, LogResultEnum.LOG_FAIL.code, "订单完成开始时间不能大于结束时间");

                });*/
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "订单完成开始时间不能大于结束时间");
            }
            if (finishEndDate.getTime() - finishStartDate.getTime() > 200 * 24 * 3600 * 1000L) {
                /*executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.ORDER_MANAGE.code, OrderManageOperateEnum.MALL_SYNC_ORDER.code,
                            exportContentLogFromRequest(param), userId,ip, LogResultEnum.LOG_FAIL.code,"订单完成时间跨度不能大于200天");

                });*/
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "订单完成时间跨度不能大于200天");
            }
        }

        if(valetOrderCompleteDateStart != null && valetOrderCompleteDateEnd != null){
            if (valetOrderCompleteDateStart.after(valetOrderCompleteDateEnd)) {
                /*executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.ORDER_MANAGE.code, OrderManageOperateEnum.MALL_SYNC_ORDER.code,
                            exportContentLogFromRequest(param), userId,ip, LogResultEnum.LOG_FAIL.code,"待出帐开始时间不能大于结束时间");

                });*/
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "待出帐开始时间不能大于结束时间");
            }
            if (valetOrderCompleteDateEnd.getTime() - valetOrderCompleteDateStart.getTime() > 200 * 24 * 3600 * 1000L) {
                /*executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.ORDER_MANAGE.code, OrderManageOperateEnum.MALL_SYNC_ORDER.code,
                            exportContentLogFromRequest(param), userId,ip, LogResultEnum.LOG_FAIL.code,"待出帐时间跨度不能大于200天");

                });*/
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "待出帐时间跨度不能大于200天");
            }
            valetOrderCompleteTimeStart = DateTimeUtil.getDbTimeStr(valetOrderCompleteDateStart);
            valetOrderCompleteTimeEnd = DateTimeUtil.getDbTimeStr(valetOrderCompleteDateEnd);
        }

        List<String> userIdList = new ArrayList<>();
        Integer exportOrderCount = 0;
        String roleType = loginIfo4Redis.getRoleType();
        //超级管理员和运营管理员获取所有人订单,客服管理员也一样

        //用户手机号现在加密存储，所以涉及到手机号的匹配查询需要加密之后做完全匹配查询
        if (StringUtils.isNotBlank(receiverPhone)) {
            receiverPhone = IOTEncodeUtils.encryptSM4(receiverPhone,serviceConfig.getSm4Key(),serviceConfig.getSm4Iv());
        }
        String userBeId = "";
        String userLocation = "";
        if (dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_ORDER_SYSTEM)) {
            exportOrderCount = order2cAtomInfoMapperExt.getOrderExportCount(null,
                    orderId, orderStatus, startTime, endTime,
                    spuOfferingClass, skuOfferingName, skuOfferingCode,
                    atomOfferingName, rocStatus, receiverPhone,
                    businessCode, roleType, finishStartDate, finishEndDate,
                    specialAfterMarketHandle, specialAfterStatus, orderType,valetOrderCompleteTimeStart,
                    valetOrderCompleteTimeEnd,beId,userBeId,userLocation,onlineSettleStatus);
        } else if (dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_ORDER_COMPANY)) {
            if (roleType.equals(BaseConstant.PARTNER_LORD_ROLE)){
                //合作伙伴主账号获取其下所有从账号的订单
                BaseAnswer<List<String>> downUserIds = userFeignClient.getDownUserIds(userId);
                if (CollectionUtil.isNotEmpty(downUserIds.getData())) {
                    userIdList = downUserIds.getData();
                }
                userIdList.add(userId);
            }else if (roleType.equals(BaseConstant.PARTNER_PROVINCE)){
                // 省管配置权限为单位权限
                BaseAnswer<Data4User> data4UserBaseAnswer = userFeignClient.userInfoById(userId);
                if (data4UserBaseAnswer == null || !SUCCESS.getStateCode().equals(data4UserBaseAnswer.getStateCode())) {
                    throw new BusinessException("10004", "合作伙伴省管账号错误");
                }

                Data4User data4User = data4UserBaseAnswer.getData();
                String companyType = data4User.getCompanyType();
                boolean isProvinceUser = org.apache.commons.lang3.StringUtils.isNotEmpty(companyType) && "2".equals(companyType);
                String currentUserLocation = data4User.getLocationIdPartner();
                if (isProvinceUser){
                    if ("all".equals(currentUserLocation)) {
                        userBeId = data4User.getBeIdPartner();
                    } else {
                        userLocation = currentUserLocation;
                    }
                }else {
                    throw new BusinessException(BaseErrorConstant.UN_PERMISSION);
                }
            }else{
                throw new BusinessException(BaseErrorConstant.UN_PERMISSION);
            }

            exportOrderCount = order2cAtomInfoMapperExt.getOrderExportCount(userIdList,
                    orderId, orderStatus, startTime, endTime,
                    spuOfferingClass, skuOfferingName, skuOfferingCode,
                    atomOfferingName, rocStatus, receiverPhone,
                    businessCode, roleType, finishStartDate, finishEndDate,
                    specialAfterMarketHandle, specialAfterStatus,orderType,
                    valetOrderCompleteTimeStart,valetOrderCompleteTimeEnd, beId,
                    userBeId,userLocation, onlineSettleStatus);
        } else if (dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_ORDER_PERSONAL)) {
            //合作伙伴从账号获取自己的订单
            userIdList.add(userId);
            exportOrderCount = order2cAtomInfoMapperExt.getOrderExportCount(userIdList,
                    orderId, orderStatus, startTime, endTime,
                    spuOfferingClass, skuOfferingName, skuOfferingCode,
                    atomOfferingName, rocStatus, receiverPhone,
                    businessCode, roleType, finishStartDate, finishEndDate,
                    specialAfterMarketHandle, specialAfterStatus, orderType,
                    valetOrderCompleteTimeStart,valetOrderCompleteTimeEnd, beId,
                    userBeId,userLocation,onlineSettleStatus);
        } else {
            /*executorService.execute(() -> {
                logService.recordOperateLogAsync(ModuleEnum.ORDER_MANAGE.code, OrderManageOperateEnum.MALL_SYNC_ORDER.code,
                        exportContentLogFromRequest(param), userId,ip, LogResultEnum.LOG_FAIL.code,StatusConstant.UN_PERMISSION.getMessage());

            });*/
            throw new BusinessException(BaseErrorConstant.UN_PERMISSION);
        }
        return exportOrderCount;
    }

    @Override
    public void exportOrderDataScreen(OrderScreenExportParam param, LoginIfo4Redis loginIfo4Redis) {
        String startTime = param.getStartTime();
        String endTime = param.getEndTime();
        List<String> areaCode = param.getAreaCode();
        List<String> locationCode = param.getLocationCode();
        String valetOrderCompleteStartTime = param.getValetOrderCompleteStartTime();
        String valetOrderCompleteEndTime = param.getValetOrderCompleteEndTime();
        String userId = loginIfo4Redis.getUserId();
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        Date valetOrderCompleteStartDate = null;
        Date valetOrderCompleteEndDate = null;
        Date startDate = null;
        Date endDate = null;
        log.info("hwftest areaCode = {}, locationCode = {}",areaCode, locationCode);
        try {
            BaseAnswer<Data4User> data4UserBaseAnswer = userFeignClient.userInfo(userId);
            Data4User data = data4UserBaseAnswer.getData();

            //临时处理：只导出用户默认省份的订单
            //暂时用给用户配置的省份进行订单导出
            /*if ("screen".equals(data.getSystem()) && StringUtils.isEmpty(data.getBeId())) {
                //连默认省份都没有的直接报错
                throw new BusinessException(StatusConstant.ORDER_PARAM_BID);
            }

            List<String> provinces = new ArrayList<>();
            List<String> locations = new ArrayList<>();
            //获取配置的省份
            Map<String,String> provinceMap = new HashMap<>();
            String beIdStr = data.getBeId();
            if("所有省".equals(beIdStr)){
                provinces = null;
                locations = null;
            }else{
                List<String> beIds = Arrays.asList(beIdStr.split(","));
                for(String beid : beIds){
                    provinceMap.put(beid,"init");
                }
                //获取配置的地市
                String locationStr = data.getLocation();
                if(StringUtils.isNotEmpty(locationStr)){
                    locations = Arrays.asList(locationStr.split(","));
                }
                //获取地市的省份
                Map<String, String> cityProvinceMap = provinceCityConfig.getCityProvinceMap();
                for(String loc: locations){
                    //反查地市省份
                    String provinceCode = cityProvinceMap.get(loc);
                    provinceMap.remove(provinceCode);
                }

                //获取没有地市的省份
                for(Map.Entry<String, String> entry: provinceMap.entrySet()){
                    provinces.add(entry.getKey());
                }
                log.info("hwftest 处理完成后 provinceList = {}", provinces);
                log.info("hwftest 处理完成后 cityList = {}", locations);
            }*/

            //大屏系统订单导出
            if ("screen".equals(data.getSystem()) && CollectionUtils.isEmpty(areaCode) && CollectionUtils.isEmpty(locationCode)) {
                throw new BusinessException(StatusConstant.ORDER_PARAM_MUST);
            }
//           /* String userId = request.getHeader(Constant.HEADER_KEY_USER_ID);
//            LoginIfo4Redis loginIfo4Redis = (LoginIfo4Redis) redisTemplate.opsForValue().get(Constant.REDIS_KEY_USER_TOKEN + userId);
//            if (loginIfo4Redis == null) {
//                throw new BusinessException(BaseErrorConstant.UN_LOGIN);
//            }
//            List<String> dataPermissionCodes = (List<String>) redisTemplate.opsForValue().get(Constant.REDIS_KEY_ROLE_DATA_PERMISSION_CODE + loginIfo4Redis.getRoleId());
//            if (ObjectUtils.isEmpty(dataPermissionCodes) || (!dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_SCREEN_SECOND_ALL) && !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_SCREEN_SECOND_PART))) {
//                throw new BusinessException(BaseErrorConstant.UN_PERMISSION);
//            }*/

            //增加校验，如果传的是省code，判断是否有地市
            if(areaCode == null){
                areaCode = new ArrayList<>();
            }
            if(locationCode == null){
                locationCode = new ArrayList<>();
            }
            String clocations = data.getLocation();
            List<String> areaCodes = new ArrayList<>();
            areaCodes.addAll(areaCode);
            if(StringUtils.isNotEmpty(clocations)){
                for(String pcode : areaCode){
                    List<String> citys = provinceCityConfig.getProvinceCityMap().get(pcode);
                    List<String> configedLocation = Arrays.asList(clocations.split(","));
                    for(String ccode : configedLocation){
                        if(citys.contains(ccode)){
                            //传了省份的，不用省份，以默认地市为准（选了省份，必有地市）
                            areaCodes.remove(pcode);
                            locationCode.addAll(configedLocation);
                            break;
                        }
                    }
                }
            }


            try {
                //如果待出账时间,或者起始截止时间不是空的 就不用给起始结束时间赋值
                if (StringUtils.isEmpty(startTime) && StringUtils.isEmpty(endTime)
                        && StringUtils.isEmpty(valetOrderCompleteStartTime) && StringUtils.isEmpty(valetOrderCompleteEndTime)){
                    endDate = new Date();
                    // 默认取过去60天
                    startDate = DateUtils.addDay(endDate, -200);
                }
                if (StringUtils.isNotEmpty(startTime) && StringUtils.isNotEmpty(endTime)){
                    endDate = DateUtils.strToDate(endTime, DateUtils.DEFAULT_DATETIME_FORMAT);
                  //  endTime = DateTimeUtil.getDbTimeStr(endDate);
                    startDate = DateUtils.strToDate(startTime, DateUtils.DEFAULT_DATETIME_FORMAT);
                   // startTime = DateTimeUtil.getDbTimeStr(startDate);
                }

            if(StringUtils.isNotEmpty(startTime) && StringUtils.isNotEmpty(endTime)){
                if (endDate.getTime() - startDate.getTime() > 200 * 24 * 3600 * 1000L) {
                    throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "下单时间跨度不能大于200天");
                }
                if (startDate.after(endDate)) {
                    throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "下单开始时间不能大于结束时间");
                }
            }
            if (startDate != null && endDate != null){
            startTime = DateTimeUtil.getDbTimeStr(startDate);
            endTime = DateTimeUtil.getDbTimeStr(endDate);
            }
            if(StringUtils.isNotEmpty(valetOrderCompleteStartTime)){
                valetOrderCompleteStartDate = DateUtils.strToDate(valetOrderCompleteStartTime, DateUtils.DEFAULT_DATETIME_FORMAT);
                valetOrderCompleteStartTime = DateTimeUtil.getDbTimeStr(valetOrderCompleteStartDate);
            }
            if(StringUtils.isNotEmpty(valetOrderCompleteEndTime)){
                valetOrderCompleteEndDate = DateUtils.strToDate(valetOrderCompleteEndTime, DateUtils.DEFAULT_DATETIME_FORMAT);
                valetOrderCompleteEndTime = DateTimeUtil.getDbTimeStr(valetOrderCompleteEndDate);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "下单日期格式错误");
        }

        if(StringUtils.isNotEmpty(valetOrderCompleteStartTime) && StringUtils.isNotEmpty(valetOrderCompleteEndTime)){
            if (valetOrderCompleteEndDate.getTime() - valetOrderCompleteStartDate.getTime() > 200 * 24 * 3600 * 1000L) {
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "待出账下单时间跨度不能大于200天");
            }
            if (Long.parseLong(valetOrderCompleteStartTime) > Long.parseLong(valetOrderCompleteEndTime)) {
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "待出账下单开始时间不能大于结束时间");
            }
        }
//        log.info("testhwf starttime = {}, endtime = {}, valetOrderCompleteStartTime = {}, valetOrderCompleteEndTime = {}, provinces = {}, locations = {}"
//                ,startTime, endTime, valetOrderCompleteStartTime, valetOrderCompleteEndTime, provinces, locations);
//            List<OrderScreenExportDO> orderScreenExportS = order2cAtomInfoMapperExt.selectOrderScreenExportList(startTime, endTime, provinces,locations,valetOrderCompleteStartTime,valetOrderCompleteEndTime);
//            if(CollectionUtils.isNotEmpty(orderScreenExportS)){
//                log.info("hwftest orderScreenExportS size = {}",orderScreenExportS.size());
//            }else{
//                log.info("hwftest orderScreenExportS size = 0!!!");
//            }
            List<OrderScreenExportDO> orderScreenExportS = order2cAtomInfoMapperExt.selectOrderScreenExportList(startTime, endTime, areaCodes,locationCode,valetOrderCompleteStartTime,valetOrderCompleteEndTime);
            List<OrderScreenExportDTO> exportList = new ArrayList<>();
            List<String> a11OrderIds = orderScreenExportS.stream().filter(x->SPUOfferingClassEnum.A11.getSpuOfferingClass().equals(x.getSpuOfferingClass())).map(OrderScreenExportDO::getOrderId).distinct().collect(Collectors.toList());
            log.info("hwftest orderScreenExportS size = {}",orderScreenExportS.size());
         /*   List<OrderScreenExportDO> collect = orderScreenExportS.stream().filter(orderScreenExportDO -> StringUtils.isNotEmpty(orderScreenExportDO.getDistributorPhone())).collect(Collectors.toList());
            log.info("hwftest collect size = {}",collect.size());
            //同一个分销订单包含软硬件订单，
            List<OrderScreenExportDO> distributionLevelOrder = new ArrayList<>();
            Map<String, List<OrderScreenExportDO>> listMap = collect.stream().collect(Collectors.groupingBy(OrderScreenExportDO::getOrderId));
            //筛选出查询的订单同时有一级和二级分销的订单
            for (Map.Entry<String, List<OrderScreenExportDO>> entry : listMap.entrySet()) {
                List<OrderScreenExportDO> entryValue = entry.getValue();
                //筛选出有1 2级关联的订单 取一级订单数据
                if (entryValue.size() > 1) {
                    Map<String, List<OrderScreenExportDO>> collect1 = entryValue.stream().collect(Collectors.groupingBy(OrderScreenExportDO::getAtomOfferingClass));
                    for (Map.Entry<String, List<OrderScreenExportDO>> stringListEntry : collect1.entrySet()) {
                        List<OrderScreenExportDO> value = stringListEntry.getValue();

                        //判断是否是软硬件造成的分销订单重复的
                        long count = value.stream().map(OrderScreenExportDO::getDistributorLevel).distinct().count();
                        if (count > 1) {
                            List<OrderScreenExportDO> firstLevel = value.stream().filter(orderScreenExportDO -> orderScreenExportDO.getDistributorLevel().equals("1")).collect(Collectors.toList());
                            distributionLevelOrder.addAll(firstLevel);
                        }
                    }

                }
            }
            log.info("hwftest distributionLevelOrder size = {}",distributionLevelOrder.size());
            //删除查询结果 分销有一 二 级的订单里的关联一级的订单 值统计二级的订单
            orderScreenExportS = orderScreenExportS.stream().filter(o -> !distributionLevelOrder.contains(o)).collect(Collectors.toList());
            log.info("hwftest orderScreenExportS1 size = {}",orderScreenExportS.size());*/

            // 获取2级分销员的渠道信息
            List<String> distributorUserId2List = orderScreenExportS.stream()
                    .filter(orderScreenExportDO -> StringUtils.isNotEmpty(orderScreenExportDO.getDistributorUserId2()))
                    .map(OrderScreenExportDO::getDistributorUserId2).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(distributorUserId2List)){
                List<DistributorChannelDO> distributorChannelInfo2List = shopCustomerInfoMapperExt.getDistributorChannelInfo(distributorUserId2List);
                Map<String,DistributorChannelDO> distributorChannelInfo2Map = new HashMap();
                if (CollectionUtil.isNotEmpty(distributorChannelInfo2List)){
                    for (DistributorChannelDO distributorChannelDO : distributorChannelInfo2List) {
                        distributorChannelInfo2Map.put(distributorChannelDO.getUserId(),distributorChannelDO);
                    }
                    orderScreenExportS.stream().forEach(orderScreenExportDO -> {
                        String distributorUserId2 = orderScreenExportDO.getDistributorUserId2();
                        if (StringUtils.isNotEmpty(distributorUserId2)){
                            DistributorChannelDO distributorChannelDO = distributorChannelInfo2Map.get(distributorUserId2);
                            if (Optional.ofNullable(distributorChannelDO).isPresent()){
                                orderScreenExportDO.setDistributorChannelId(distributorChannelDO.getDistributorChannelId());
                                orderScreenExportDO.setDistributorChannelName(distributorChannelDO.getDistributorChannelName());
                            }
                        }
                    });
                }
            }

            // 获取1级分销员的渠道信息
            List<String> distributorUserId1List = orderScreenExportS.stream()
                    .filter(orderScreenExportDO -> StringUtils.isEmpty(orderScreenExportDO.getDistributorUserId2()) && StringUtils.isNotEmpty(orderScreenExportDO.getDistributorUserId1()))
                    .map(OrderScreenExportDO::getDistributorUserId2).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(distributorUserId1List)){
                List<DistributorChannelDO> distributorChannelInfo1List = shopCustomerInfoMapperExt.getDistributorChannelInfo(distributorUserId1List);
                Map<String,DistributorChannelDO> distributorChannelInfo1Map = new HashMap();
                if (CollectionUtil.isNotEmpty(distributorChannelInfo1List)){
                    for (DistributorChannelDO distributorChannelDO : distributorChannelInfo1List) {
                        distributorChannelInfo1Map.put(distributorChannelDO.getUserId(),distributorChannelDO);
                    }
                    orderScreenExportS.stream().forEach(orderScreenExportDO -> {
                        String distributorUserId1 = orderScreenExportDO.getDistributorUserId1();
                        String distributorUserId2 = orderScreenExportDO.getDistributorUserId2();
                        if (StringUtils.isEmpty(distributorUserId2) &&
                                StringUtils.isNotEmpty(distributorUserId1)){
                            DistributorChannelDO distributorChannelDO = distributorChannelInfo1Map.get(distributorUserId1);
                            if (Optional.ofNullable(distributorChannelDO).isPresent()){
                                orderScreenExportDO.setDistributorChannelId(distributorChannelDO.getDistributorChannelId());
                                orderScreenExportDO.setDistributorChannelName(distributorChannelDO.getDistributorChannelName());
                            }
                        }
                    });
                }
            }

            orderScreenExportS.forEach(orderScreenExportDO -> {
                OrderScreenExportDTO dto = new OrderScreenExportDTO();
                BeanUtils.copyProperties(orderScreenExportDO, dto);
                //订单完结后，商品合作伙伴即使变更也展示完结时合作伙伴
                if (orderScreenExportDO.getFinishCooperatorId() != null
                        && !orderScreenExportDO.getFinishCooperatorId().equals(orderScreenExportDO.getCooperatorId())) {
                    if (OrderStatusInnerEnum.ORDER_SUCCESS.getStatus().equals(orderScreenExportDO.getOrderStatus())
                            || OrderStatusInnerEnum.ORDER_FAIL.getStatus().equals(orderScreenExportDO.getOrderStatus())) {
                        dto.setCooperatorName(orderScreenExportDO.getFinishCooperatorName());
                    }
                }
                //解析业务编码
                dto.setBusinessCode(BusinessCodeEnum.getChnName(dto.getBusinessCode()));
                Double atomPrice = orderScreenExportDO.getAtomPrice() != null ? orderScreenExportDO.getAtomPrice().doubleValue() : null;
                Double atomSalePrice = orderScreenExportDO.getAtomSalePrice() != null ? orderScreenExportDO.getAtomSalePrice().doubleValue() : null;
                //计算原子总价总价，厘 转化为 元避免SQL语句复杂化
                if (atomPrice != null) {
                    dto.setAtomTotalPrice((atomPrice / 1000) * dto.getQuantity());
                }
                if (atomSalePrice != null) {
                    dto.setAtomSalePrice(atomSalePrice / 1000);
                }

                dto.setSettlePrice(orderScreenExportDO.getSettlePrice() != null ? orderScreenExportDO.getSettlePrice().doubleValue() : null);
                if (dto.getSettlePrice() != null) {
                    dto.setSettlePrice(dto.getSettlePrice() / 1000);
                    dto.setTotalSettlePrice(dto.getSettlePrice() * dto.getQuantity());
                }
                //数据库的日期格式是 yyyyMMddHHmmss,转换为 yyyy-MM-dd HH:mm:ss
                Date date = DateUtil.parse(dto.getCreateTime());
                dto.setCreateTime(DateUtil.formatDateTime(date));
                dto.setSpuOfferingClass(SPUOfferingClassEnum.getDisplay(orderScreenExportDO.getSpuOfferingClass()));
                dto.setOrderStatusDescribe(OrderStatusInnerEnum.getDescribe(orderScreenExportDO.getOrderStatus()));
                if (AtomOfferingClassEnum.S.name().equals(orderScreenExportDO.getAtomOfferingClass())) {
                    if(SPUOfferingClassEnum.A13.getSpuOfferingClass().equals(dto.getSpuOfferingClass())){
                        dto.setAtomOfferingClass("软件功能费");
                    }else{
                        dto.setAtomOfferingClass("软件");
                    }
                } else if (AtomOfferingClassEnum.H.name().equals(orderScreenExportDO.getAtomOfferingClass())) {
                    if (SPUOfferingClassEnum.A07.getSpuOfferingClass().equals(orderScreenExportDO.getSpuOfferingClass())) {
                        dto.setAtomOfferingClass("合同履约类硬件");
                    } else {
                        dto.setAtomOfferingClass("代销类硬件");
                    }
                } else if (AtomOfferingClassEnum.O.name().equals(orderScreenExportDO.getAtomOfferingClass())) {
                    dto.setAtomOfferingClass(AtomOfferingClassEnum.O.getDescribe());
                } else if (AtomOfferingClassEnum.D.name().equals(orderScreenExportDO.getAtomOfferingClass())) {
                    dto.setAtomOfferingClass(AtomOfferingClassEnum.D.getDescribe());
                } else if (AtomOfferingClassEnum.P.name().equals(orderScreenExportDO.getAtomOfferingClass())) {
                    dto.setAtomOfferingClass(AtomOfferingClassEnum.P.getDescribe());
                } else if (AtomOfferingClassEnum.F.name().equals(orderScreenExportDO.getAtomOfferingClass())) {
                    dto.setAtomOfferingClass(AtomOfferingClassEnum.F.getDescribe());
                } else if (AtomOfferingClassEnum.K.name().equals(orderScreenExportDO.getAtomOfferingClass())) {
                    dto.setAtomOfferingClass(AtomOfferingClassEnum.K.getDescribe());
                } else if (AtomOfferingClassEnum.C.name().equals(orderScreenExportDO.getAtomOfferingClass())) {
                    dto.setAtomOfferingClass(AtomOfferingClassEnum.C.getDescribe());
                } else if (AtomOfferingClassEnum.X.name().equals(orderScreenExportDO.getAtomOfferingClass())) {
                    dto.setAtomOfferingClass(AtomOfferingClassEnum.X.getDescribe());
                } else if (AtomOfferingClassEnum.M.name().equals(orderScreenExportDO.getAtomOfferingClass())) {
                    dto.setAtomOfferingClass(AtomOfferingClassEnum.M.getDescribe());
                }else if (AtomOfferingClassEnum.A.name().equals(orderScreenExportDO.getAtomOfferingClass())) {
                    dto.setAtomOfferingClass(AtomOfferingClassEnum.A.getDescribe());
                }else if (AtomOfferingClassEnum.B.name().equals(orderScreenExportDO.getAtomOfferingClass())) {
                    dto.setAtomOfferingClass(AtomOfferingClassEnum.B.getDescribe());
                }else if (AtomOfferingClassEnum.E.name().equals(orderScreenExportDO.getAtomOfferingClass())) {
                    dto.setAtomOfferingClass(AtomOfferingClassEnum.E.getDescribe());
                }else if (AtomOfferingClassEnum.G.name().equals(orderScreenExportDO.getAtomOfferingClass())) {
                    dto.setAtomOfferingClass(AtomOfferingClassEnum.G.getDescribe());
                }
                //个人客户所属省份，去掉“移动”后缀
                String province = orderScreenExportDO.getProvince();
                if (province != null && province.contains("移动")) {
                    dto.setProvince(province.substring(0, province.indexOf("移动")));
                }
                //根据编码获取地市和区县名称
                dto.setLocation((String) RegionAndLocationConstant.LOCATION_MAP.get(dto.getLocation()));
                dto.setRegionID((String) RegionAndLocationConstant.REGION_MAP.get(dto.getRegionID()));
                //抵扣金额解密和赋值
                String orderDeductPrice = IOTEncodeUtils.decryptSM4(orderScreenExportDO.getOrderDeductPrice(), serviceConfig.getSm4Key(),serviceConfig.getSm4Iv());
                if (StringUtils.isNotEmpty(orderDeductPrice)) {
                    dto.setOrderDeductPrice(Double.parseDouble(orderDeductPrice) / 1000);
                }

                //订单计收信息
                String orgName = orderScreenExportDO.getOrgName();
                if (StringUtils.isNotEmpty(orgName)) {
                    String[] split = orgName.split("-");
                    dto.setOrgProvince(split[0]);
                    if (split.length >= 2) {
                        dto.setOrgLocation(split[1]);
                        if (split.length >= 3) {
                            dto.setOrgRegion(split[2]);
                        }
                    }
                }
                dto.setPrice(orderScreenExportDO.getPrice() == null ? null : orderScreenExportDO.getPrice().doubleValue());
                if (dto.getPrice() != null) {
                    dto.setPrice(dto.getPrice() / 1000);
                }
                if (StringUtils.isNotEmpty(orderScreenExportDO.getOrderTotalPrice())) {
                    dto.setOrderTotalPrice(Double.valueOf(IOTEncodeUtils.decryptSM4(orderScreenExportDO.getOrderTotalPrice(), serviceConfig.getSm4Key(),serviceConfig.getSm4Iv())) / 1000);
                }

                // 订单完成时间
                Date finishTime = orderScreenExportDO.getFinishTime();
                if (Optional.ofNullable(finishTime).isPresent()) {
                    dto.setFinishTime(DateUtils.dateToStr(finishTime, DateUtils.DEFAULT_DATETIME_FORMAT));
                }
                String orderType = dto.getOrderType();
                if (StringUtils.isNotEmpty(orderType)){
                    dto.setOrderType(OrderTypeEnum.getDescByCode(orderType));
                }

                dto.setCalculationCollectionPrice(orderScreenExportDO.getCalculationCollectionPrice() == null ? null : orderScreenExportDO.getCalculationCollectionPrice().doubleValue());
                if (dto.getCalculationCollectionPrice() != null) {
                    dto.setCalculationCollectionPrice(dto.getCalculationCollectionPrice() / 1000);
                }

                if (StringUtils.isEmpty(dto.getSsoTerminalType())) {
                    //默认是PC登录方式
                    dto.setSsoTerminalType(SsoTerminalTypeEnum.PC.code);
                }
                dto.setSsoTerminalType(SsoTerminalTypeEnum.getDesc(dto.getSsoTerminalType()));

                //客户信息解密
                dto.setCustCode(IOTEncodeUtils.decryptSM4(orderScreenExportDO.getCustCode(), serviceConfig.getSm4Key(),serviceConfig.getSm4Iv()));
                dto.setCustName(IOTEncodeUtils.decryptSM4(orderScreenExportDO.getCustName(), serviceConfig.getSm4Key(),serviceConfig.getSm4Iv()));

                Integer settleStatus = orderScreenExportDO.getSettleStatus();
                if (settleStatus != null){
                    dto.setSettleStatusName(SettleStatusEnum.getDescByCode(settleStatus));
                }

                exportList.add(dto);
            });
            log.info("hwftest exportList size = {}",exportList.size());
            if (exportList.isEmpty()) {
                throw new BusinessException(StatusConstant.ORDER_NOT_EXIST);
            }

            //查询订单中A11订单的增值服务包信息
            final List<OrderScreenExportDTO> exportWithCard = new ArrayList<>();
            boolean hasCard = false;
            if (CollectionUtils.isNotEmpty(a11OrderIds)) {
                //添加增值服务包作为原子订单
                List<CardValueAddedInfoDO> cardValueAddedInfoDOS = order2cAtomInfoMapperExt.selectCardValueAddedExportList(a11OrderIds);
                if (CollectionUtils.isNotEmpty(cardValueAddedInfoDOS)) {
                    hasCard = true;
                    Map<String, List<OrderScreenExportDTO>> orderMap = exportList.stream().collect(Collectors.groupingBy(OrderScreenExportDTO::getOrderId, LinkedHashMap::new, Collectors.toList()));
                    Map<String, List<CardValueAddedInfoDO>> cardMap = cardValueAddedInfoDOS.stream().collect(Collectors.groupingBy(CardValueAddedInfoDO::getOrderId, LinkedHashMap::new, Collectors.toList()));
                    orderMap.forEach((x,orderList) -> {
                        List<CardValueAddedInfoDO> cards = cardMap.get(x);
                        if (CollectionUtils.isNotEmpty(cards)) {
//                            Double totalAdd = cards.stream().filter(c-> c.getExpensesPrice() !=null && c.getExpensesTerm()!=null && c.getOrderQuantity()!=null)
//                                    .mapToDouble(c -> Double.parseDouble(c.getExpensesPrice()) * Double.parseDouble(c.getExpensesTerm())
//                                            * Double.parseDouble(c.getOrderQuantity()) ).sum() / 1000;
                            Double cardPrice = cards.stream().filter(c-> c.getExpensesPrice() !=null && c.getExpensesTerm()!=null && c.getOrderQuantity()!=null)
                                    .mapToDouble(c -> Double.parseDouble(c.getExpensesPrice())
                                            * Double.parseDouble(c.getExpensesTerm())  ).sum() /1000;
                            orderList.forEach(o -> o.setPrice(o.getPrice() != null ? o.getPrice() + cardPrice : cardPrice));
//                            orderList.forEach(o -> o.setPrice(o.getPrice() != null ? o.getPrice() + totalAdd : totalAdd));
                            cards.forEach(c -> {
                                OrderScreenExportDTO orderExportDTO = new OrderScreenExportDTO();
                                BeanUtils.copyProperties(orderList.get(0),orderExportDTO);
                                orderExportDTO.setAtomOfferingName(c.getOfferingName());
                                orderExportDTO.setAtomOfferingClass(c.getOfferingType());
                                orderExportDTO.setQuantity(Integer.parseInt(c.getOrderQuantity()));
                                if (c.getExpensesPrice() !=null && c.getExpensesTerm()!=null && c.getOrderQuantity()!=null) {
                                    Double atomTotalPrice = Double.parseDouble(c.getExpensesPrice())
                                            * Double.parseDouble(c.getExpensesTerm()) * Double.parseDouble(c.getOrderQuantity()) / 1000;
                                    orderExportDTO.setAtomTotalPrice(atomTotalPrice);
                                    orderExportDTO.setCalculationCollectionPrice(atomTotalPrice);
                                }
                                orderExportDTO.setAtomOfferingCode(null);
                                orderExportDTO.setAtomSalePrice(null);
                                orderExportDTO.setSettlePrice(null);
                                orderExportDTO.setAtomOfferingVersion(null);
                                orderExportDTO.setMaterialNum(null);
                                orderExportDTO.setChargeName(null);
                                orderExportDTO.setProductTypeName(null);
                                orderExportDTO.setTotalSettlePrice(null);
//                                orderExportDTO.setCalculationCollectionPrice(null);

                                orderList.add(orderExportDTO);
                            });
                        }
                        exportWithCard.addAll(orderList);
                    });
                }
            }

            String fileName = "运营数据-订单导出" + new Date().getTime() + ".xlsx";
            ClassPathResource classPathResource = new ClassPathResource("template/order_screen_data.xlsx");
            InputStream templateFileName = classPathResource.getInputStream();
            EasyExcelUtils.exportExcel2OutputStream(outputStream, "list", hasCard? exportWithCard : exportList, null, templateFileName,
                    0, "订单导出", null, null, null,false);

            //上传文件到对象存储
            ByteArrayUpload byteArrayUpload = new ByteArrayUpload();
            byteArrayUpload.setFileName(fileName);
            byteArrayUpload.setBytes(outputStream.toByteArray());
            BaseAnswer<UpResult> resultBaseAnswer = storageService.uploadByte(byteArrayUpload);
            if (!"00000".equals(resultBaseAnswer.getStateCode())) {
                throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, "上传文件出错,请联系管理员");
            }
            log.info("运营数据订单导出接口上传excel完毕");

            //消息中心提醒用户
            UpResult upResult = resultBaseAnswer.getData();
            String outerUrl = upResult.getOuterUrl();
            String fileKey = upResult.getKey();
            AddMessageParam messageParam = new AddMessageParam();
            messageParam.setContent("导出成功");
            messageParam.setType("运营数据-订单导出");
            messageParam.setUserId(userId);
            messageParam.setFileKey(fileKey);
            messageParam.setUrl(outerUrl);
            messageParam.setSource(2);
            iotFeignClient.addMessage(messageParam);

            //通过kafka发送websocket提示前端更新消息未读数
            ProducerRecord<String, byte[]> record = new ProducerRecord<>(KafkaTopic.SUPPLY_CHAIN_WEBSOCKET.getTopic(), "/message/monitor".getBytes());
            kafkaTemplate.send(record);

            //发送短信提示用户
            String phone = loginIfo4Redis.getPhone();
            if (StringUtils.isNotEmpty(phone) && RegexUtil.regexPhone(phone)) {
                Msg4Request msg4Request = new Msg4Request();
                msg4Request.setTemplateId(serviceConfig.getNewMessageSms());
                List<String> mobiles = new ArrayList<>();
                mobiles.add(phone);
                msg4Request.setMobiles(mobiles);
                smsFeignClient.asySendMessage(msg4Request);
            }

        } catch (Exception e) {
            String content = null;
            //出现异常时，保存到消息中心
            if (e instanceof BusinessException) {
                BusinessException businessException = (BusinessException) e;
                content = "导出失败:" + businessException.getStatus().getMessage();
            } else {
                log.error("导出订单发生异常,", e);
                content = "导出失败:请联系管理员";
            }
            AddMessageParam messageParam = new AddMessageParam();
            messageParam.setContent(content);
            messageParam.setType("运营数据-订单导出");
            messageParam.setUserId(userId);
            messageParam.setSource(2);
            iotFeignClient.addMessage(messageParam);
        } finally {
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 将操作人电话和姓名置空
     *
     * @param orderExportHandleList
     */
    private void setCustNull(List<OrderExportDO> orderExportHandleList) {
        orderExportHandleList.stream().forEach(orderExportHandle -> {
            orderExportHandle.setCustMgName("");
            orderExportHandle.setCustMgPhone("");
        });
    }

    /**
     * 构造订单导出日志内容
     */
    @Override
    public String exportContentLogFromRequest(OrderExportParam request) {
        StringBuilder result = new StringBuilder();
        result.append("【导出订单信息表】\n");
        if (StringUtils.isNotEmpty(request.getOrderId())) {
            result.append("订单编号").append(request.getOrderId()).append("\n");
        }

        if (StringUtils.isNotEmpty(request.getStartTime())
                && StringUtils.isNotEmpty(request.getEndTime())) {
            result.append("下单时间").append(request.getStartTime()).append("至")
                    .append(request.getEndTime()).append("\n");
        } else if (StringUtils.isNotEmpty(request.getStartTime())
                && StringUtils.isEmpty(request.getEndTime())) {
            result.append("下单时间").append(request.getStartTime()).append("开始").append("\n");
        } else if (StringUtils.isEmpty(request.getStartTime())
                && StringUtils.isNotEmpty(request.getEndTime())) {
            result.append("下单时间").append(request.getEndTime()).append("截止").append("\n");
        }

        if (request.getOrderStatus() != null) {
            /**
             * 0 待发货、1 待收货、2 已收货、3 开票、4 退款中、
             * 5 退货退款中、6 换货中、7 交易完成、8 交易失败
             * */
            String[] orderStatusStrs = {"0待发货", "1待收货", "2已收货", "3开票", "4退款中", "5退货退款中", "6换货中", "7交易完成", "8交易失败"};
            StringBuilder builder = new StringBuilder();
            request.getOrderStatus().forEach(x -> builder.append(orderStatusStrs[x]).append(","));
            result.append("订单状态").append(builder.substring(0,builder.length() - 1)).append("\n");
        }

        if (CollectionUtils.isNotEmpty(request.getSpuOfferingClass())) {
            Map<String, String> typeMap = new LinkedHashMap<String, String>() {
                {
                    put("A01", "基础产品");
                    put("A02", "行业应用");
                    put("A03", "硬件终端");
                    put("A06", "代销");
                    put("A07", "合同履约");
                    put("A04", "（DICT）产品增值服务包");
                    put("A08", "OneNET独立服务");
                    put("A09", "OnePark独立服务");
                    put("A12", "行车卫士标准产品");
                    put("A13", "软件服务");
                    put("A14", "OneCyber标准产品");
                }
            };
            StringBuilder builder = new StringBuilder();
            request.getSpuOfferingClass().forEach(x -> builder.append(typeMap.get(x)).append(","));
            result.append("商品类型").append(builder.substring(0,builder.length() - 1)).append("\n");
        }

        if (StringUtils.isNotEmpty(request.getSkuOfferingName())) {
            result.append("商品规格名称").append(request.getSkuOfferingName()).append("\n");
        }

        if (StringUtils.isNotEmpty(request.getSkuOfferingCode())) {
            result.append("商品规格编码").append(request.getSkuOfferingCode()).append("\n");
        }

        if (StringUtils.isNotEmpty(request.getAtomOfferingName())) {
            result.append("原子商品名称").append(request.getAtomOfferingName()).append("\n");
        }

        if (request.getRocStatus() != null) {
            StringBuilder builder = new StringBuilder();
            request.getRocStatus().forEach(x -> builder.append(OrderRocTypeEnum.getDescribe(x)).append(","));
            result.append("售后状态").append(builder.substring(0,builder.length() - 1)).append("\n");
        }

        if (CollectionUtils.isNotEmpty(request.getDeliveryArea())) {
            StringBuilder builder = new StringBuilder();
            request.getDeliveryArea().forEach(x -> builder.append(x).append(","));
            result.append("收货地区").append(builder.substring(0,builder.length() - 1)).append("\n");
        }

        if (StringUtils.isNotEmpty(request.getReceiverPhone())) {
            result.append("收货人手机号").append(LogService.replaceWithStar(request.getReceiverPhone())).append("\n");
        }

        if (CollectionUtils.isNotEmpty(request.getBusinessCode())) {
            Map<String, String> typeMap = new LinkedHashMap<String, String>() {
                {
                    put("SyncGrpOrderInfo", "集团订单同步");
                    put("SyncIndividualOrderInfo", "个人客户订单同步");
                }
            };
            StringBuilder builder = new StringBuilder();
            request.getBusinessCode().forEach(x -> builder.append(typeMap.get(x)).append(","));
            result.append("业务编码").append(builder.substring(0,builder.length() - 1)).append("\n");
        }

        return result.toString();
    }

    /**
     * 将版本号置空
     *
     * @param orderExportHandleList
     */
    private void setVersionNull(List<OrderExportDO> orderExportHandleList) {
        orderExportHandleList.stream().forEach(orderExportHandle -> {
            orderExportHandle.setAtomOfferingVersion("");
            orderExportHandle.setSkuOfferingVersion("");
            orderExportHandle.setSpuOfferingVersion("");
        });
    }

}
