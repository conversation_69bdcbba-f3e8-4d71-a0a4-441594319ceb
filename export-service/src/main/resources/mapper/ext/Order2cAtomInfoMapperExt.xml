<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.export.dao.ext.Order2cAtomInfoMapperExt">
    <select id="selectOrderExportList" resultType="com.chinamobile.export.pojo.mapper.OrderExportDO">
        SELECT
        oa.id as id,
        oa.create_time AS createTime,
        oa.order_id AS orderId,
        si.offering_code AS spuOfferingCode,
        si.offering_name AS spuOfferingName,
        oa.sku_offering_name AS skuOfferingName,
        oa.sku_offering_code AS skuOfferingCode,
        oa.atom_offering_name AS atomOfferingName,
        oa.atom_offering_code AS atomOfferingCode,
        oa.atom_offering_class AS atomOfferingClass,
        oa.atom_quantity * oa.sku_quantity AS quantity,
        oa.atom_price as atomPrice,
        oa.order_status AS orderStatus,
        oi.spu_offering_class AS spuOfferingClass,
        cp.partner_name partnerName,
        cp.user_name cooperatorName,
        cp.cooperator_id cooperatorId,
        cph.user_name finishCooperatorName,
        cph.cooperator_id finishCooperatorId,
        oi.contact_phone AS receiverPhone,
        ifnull(oa.supplier_name,sku.supplier_name) as supplierName,
        oi.addr1 AS deliveryArea,
        oa.atom_settle_price settlePrice,
        oi.create_oper_code createOperCode,
        oi.employee_num employeeNum,
        oi.cust_code custCode,
        oi.cust_name custName,
        p.province_company province,
        oi.location,
        oi.region_ID regionID,
        oi.contact_person_name contactPersonName,
        oi.addr1,
        oi.addr2,
        oi.addr3,
        oi.addr4,
        oi.usaddr,
        ( SELECT GROUP_CONCAT( logis_code ) FROM logistics_info WHERE order_id = oa.order_id AND order_atom_info_id = oa.id AND logistics_type = 0 ) logisCode,
        ( SELECT GROUP_CONCAT( sn ) FROM order_2c_atom_sn WHERE atom_order_id = oa.id ) sn,
        IF((oi.org_name is null or oi.org_name=''), oi.province_org_name, oi.org_name) orgName,
        --         oi.org_name orgName,
        oi.deduct_price orderDeductPrice,
        oa.sku_quantity  skuQuantity,
        ifnull(oa.sku_price,sku.price) as price,
        oi.total_price orderTotalPrice,
        oi.business_code businessCode,
        oi.cust_mg_name custMgName,
        oi.cust_mg_phone custMgPhone,
        oi.remarks,
        oi.order_type orderType,
        oa.atom_offering_version as atomOfferingVersion,
        oa.spu_offering_version  as spuOfferingVersion,
        oa.sku_offering_version  as skuOfferingVersion,
        case
        when oi.status in (3,4) then oi.order_status_time
        else null
        end finishTime,
        (SELECT GROUP_CONCAT(CONCAT('(',coupon_code,',',coupon_amount),')') FROM coupon_info WHERE order_id = oa.order_id GROUP BY order_id) couponInfo,
        (SELECT GROUP_CONCAT( salesman_code ) FROM coupon_info WHERE order_id = oa.order_id GROUP BY order_id) couponSalesmanCode,
        (SELECT GROUP_CONCAT( salesman_phone ) FROM coupon_info WHERE order_id = oa.order_id GROUP BY order_id) couponSalesmanPhone,
        (SELECT GROUP_CONCAT( employee_id ) FROM coupon_info WHERE order_id = oa.order_id GROUP BY order_id) couponEmployeeId,
        (SELECT GROUP_CONCAT( salesman_name ) FROM coupon_info WHERE order_id = oa.order_id GROUP BY order_id) couponSalesmanName,
        case
        when oi.spu_offering_class = 'A11' then (select create_time from order_2c_atom_history where atom_order_id = oa.id and order_id = oa.order_id and operate_type=1 and inner_status=62)
        else IF(oi.pay_time is null,  DATE_FORMAT(oi.create_time,'%Y-%m-%d %H:%i:%s'),DATE_FORMAT(oi.pay_time,'%Y-%m-%d %H:%i:%s'))
        end receiveOrderTime,
        oi.ordering_channel_name orderingChannelName,
        oi.sso_terminal_type ssoTerminalType,
        oi.order_status_time orderStatusTime,
        std.real_product_name realProductName,
        atom.special_cooperative_settlement_price specialCooperativeSettlementPrice,
        std.id standardServiceCode,
        std.`name` standardServiceName,
        d.short_name department,
        pp.`name` property,
        std.remark1,
        std.remark2,
        sendGoodsTime,
        case
        when (oa.order_type = '00' or oa.order_type = '02' or oa.order_type = '03') then oa.valet_order_complete_time
        when oa.order_type = '01' then oa.create_time
        end valetOrderCompleteTime,
        (select DATE_FORMAT(create_time,'%Y-%m-%d %H:%i:%s') create_time  from order_2c_atom_history where order_id = oa.order_id and atom_order_id = oa.id and operate_type = 1 and inner_status = 2 order by create_time desc limit 1 ) completeTime,
        (SELECT distributor_phone FROM order_2c_distributor_info WHERE order_id = oa.order_id AND distributor_level = 1 limit 1 ) distributorPhone1,
        (SELECT distributor_phone FROM order_2c_distributor_info WHERE order_id = oa.order_id AND distributor_level = 2 limit 1) distributorPhone2,
        (SELECT distributor_user_id FROM order_2c_distributor_info WHERE order_id = oa.order_id AND distributor_level = 1 limit 1 ) distributorUserId1,
        (SELECT distributor_user_id FROM order_2c_distributor_info WHERE order_id = oa.order_id AND distributor_level = 2 limit 1) distributorUserId2,
        case when agent.is_wash = 0 then agent.agent_name
        else agent.agent_name_wash
        end agentName,
        agent.agent_phone agentPhone,
        case when agent.is_wash = 0 then agent.agent_number
        else agent.agent_number_wash
        end agentNumber,
        agent.agent_label_wash agentLabelWash,
        agent.agent_category_wash agentCategoryWash,
        goi.grid_name griddingName,
        goi.city griddingCity,
        goi.district griddingArea,
        (SELECT inner_status FROM order_2c_roc_info WHERE order_id = oa.order_id ORDER BY create_time DESC LIMIT 1) rocInnerStatus,
        (SELECT GROUP_CONCAT( material_num ) FROM k3_product_material WHERE atom_id = atom.id GROUP BY atom_id) materialNum,
        oa.bill_no_time billNoTime,
        cic.charge_name chargeName,
        cic.product_type_name productTypeName,
        atom.charge_code chargeCode,
        atom.charge_id chargeId,
        (SELECT
        case
        when sale_order_type = 0 then '标准产品省框'
        when sale_order_type = 1 then 'DICT服务包'
        when sale_order_type = 2 then '统结标准产品'
        else ''
        end saleOrderType
        FROM contract WHERE number = kpm.contract_num  limit 1
        ) saleOrderType,
        ospo.scm_order_num scmOrderNum,
        ospo.settle_status onlineSettleStatus,
        oa.settle_status settleStatus
        FROM
        order_2c_atom_info oa force index (idx_create_time)
        LEFT JOIN order_2c_info oi ON oa.order_id = oi.order_id
        LEFT JOIN rise_order_2c_grid goi on oa.order_id = goi.order_id
        LEFT JOIN spu_offering_info_history si ON si.offering_code = oa.spu_offering_code
        AND si.spu_offering_version = oa.spu_offering_version
        LEFT JOIN sku_offering_info_history sku ON sku.offering_code = oa.sku_offering_code
        AND sku.spu_code = oa.spu_offering_code
        AND sku.spu_offering_version = oa.spu_offering_version
        AND sku.sku_offering_version = oa.sku_offering_version
        LEFT JOIN province p ON oi.be_id = p.province_code
        LEFT JOIN atom_offering_info atom ON atom.offering_code = oa.atom_offering_code AND atom.spu_code = oa.spu_offering_code AND atom.sku_code = oa.sku_offering_code
        LEFT JOIN atom_std_service ass ON ass.atom_id = atom.id
        LEFT JOIN standard_service std ON std.id = ass.std_service_id
        LEFT JOIN department d ON d.id = std.product_department_id
        LEFT JOIN product_property pp ON pp.id = std.product_property_id
        LEFT JOIN (select o2ah.atom_order_id ,o2ah.order_id, date_format(max(o2ah.create_time),'%Y-%m-%d %H:%i:%s') sendGoodsTime
        from order_2c_atom_history o2ah,
        order_2c_info oi2
        where o2ah.operate_type = 1 and o2ah.inner_status = 1
        and oi2.order_id = o2ah.order_id
        and oi2.spu_offering_class not in ('A01','A02','A03','A04','A08','A09','A10','A12','A13','A14','A15','A16','A17')
        group by atom_order_id ,order_id
        union
        select o2ah.atom_order_id ,o2ah.order_id, date_format(max(o2ah.create_time),'%Y-%m-%d %H:%i:%s') sendGoodsTime
        from order_2c_atom_history o2ah,
        order_2c_info oi2
        where o2ah.operate_type = 1 and o2ah.inner_status = 61
        and oi2.order_id = o2ah.order_id
        and oi2.spu_offering_class in ('A01','A02','A03','A04','A08','A09','A10','A12','A13','A14','A15','A16','A17')
        group by o2ah.atom_order_id ,o2ah.order_id) o2ah on o2ah.atom_order_id = oa.id and o2ah.order_id = oa.order_id
        LEFT JOIN order_2c_agent_info agent ON agent.order_id = oa.order_id
        left join k3_product_material kpm on kpm.id = (select id from k3_product_material where
        atom_id = atom.id limit 1)
        left join charge_item_config cic on cic.charge_id = atom.charge_id or cic.product_type_id = atom.product_type
        left join online_settlement_os_order osoo on osoo.order_id = oa.order_id
        left join online_settlement_purchase_order ospo on ospo.id = osoo.online_settlement_purchase_order_id
        <if test="(userIdList == null or userIdList.size() == 0) and (receiverPhone ==null or receiverPhone == '')">
            left join (
            select GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct ocr.cooperator_id) cooperator_id,
            ocr.atom_order_id,ocr.order_id
            from order_cooperator_relation ocr
            inner JOIN user_partner up on up.user_id = ocr.cooperator_id
            where 1=1
            group by ocr.atom_order_id,ocr.order_id
            ) cp on cp.atom_order_id = oa.id and cp.order_id = oa.order_id
        </if>
        <if test="(userIdList != null and userIdList.size() != 0) or (receiverPhone != null and receiverPhone != '')">
            inner join (
            select GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct ocr.cooperator_id) cooperator_id,
            ocr.atom_order_id,ocr.order_id
            from order_cooperator_relation ocr
            inner JOIN user_partner up on up.user_id = ocr.cooperator_id
            where 1=1
            <if test="userIdList != null and userIdList.size() != 0">
                and ocr.cooperator_id in
                <foreach collection="userIdList" item="userId" index="index" open="(" close=")" separator=",">
                    #{userId}
                </foreach>
            </if>
            <if test="receiverPhone!=null">
                and up.phone like '%${receiverPhone}%'
            </if>
            group by ocr.atom_order_id,ocr.order_id
            ) cp on cp.atom_order_id = oa.id and cp.order_id = oa.order_id
        </if>
        left join (
        select GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct ocrh.cooperator_id) cooperator_id,
        ocrh.atom_order_id,ocrh.order_id
        from order_cooperator_relation_history ocrh
        inner JOIN user_partner up on up.user_id = ocrh.cooperator_id
        group by ocrh.atom_order_id
        ) cph on cph.atom_order_id = oa.id and cph.order_id = oa.order_id
        <where>
            oa.order_status not in (13,14)
            <if test="startTime != null and startTime != ''">
                and oa.create_time <![CDATA[ >= ]]> #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                and oa.create_time <![CDATA[ <= ]]> #{endTime}
            </if>
            <if test="roleType == 'partner' or roleType == 'partnerLord'">
                and (oa.atom_offering_class != 'S' or oa.atom_offering_class is null)
            </if>
            <!--<if test="userIdList != null and userIdList.size() != 0">
                and oa.cooperator_id in
                <foreach collection="userIdList" item="userId" index="index" open="(" close=")" separator=",">
                    #{userId}
                </foreach>
            </if>-->
            <if test="orderId != null">
                and oa.order_id like '%${orderId}%'
            </if>
            <if test="orderStatus!=null and orderStatus.size() != 0">
                and oa.order_status in
                <foreach collection="orderStatus" item="status" index="index" open="(" close=")" separator=",">
                    #{status}
                </foreach>
            </if>
            <if test="spuOfferingClass!=null and spuOfferingClass.size() != 0">
                and oi.spu_offering_class in
                <foreach collection="spuOfferingClass" item="spuClass" index="index" open="(" close=")" separator=",">
                    #{spuClass}
                </foreach>
            </if>
            <if test="skuOfferingName!=null">
                and oa.sku_offering_name like '%${skuOfferingName}%'
            </if>
            <if test="skuOfferingCode!=null">
                and oa.sku_offering_code like '%${skuOfferingCode}%'
            </if>
            <if test="atomOfferingName!=null">
                and oa.atom_offering_name like '%${atomOfferingName}%'
            </if>
            <!--<if test="receiverPhone!=null">
                and user_partner.phone like '%${receiverPhone}%'
            </if>-->
            <if test="finishStartTime != null">
                and oi.status in (3,4) and oi.order_status_time <![CDATA[ >= ]]> #{finishStartTime}
            </if>
            <if test="finishEndTime != null ">
                and oi.status in (3,4) and oi.order_status_time <![CDATA[ <= ]]> #{finishEndTime}
            </if>
            <if test="businessCode!=null and businessCode.size() != 0" >
                and oi.business_code in
                <foreach collection="businessCode" item="bCode" index="index" open="(" close=")" separator=",">
                    #{bCode}
                </foreach>
            </if>
            <if test="specialAfterMarketHandle !=null">
                and oi.special_after_market_handle =#{specialAfterMarketHandle}
            </if>
            <if test="specialAfterStatus != null and specialAfterStatus != ''">
                and oi.special_after_status =#{specialAfterStatus}
            </if>
            <if test="orderType != null and orderType != ''">
                <if test="orderType == '00' or orderType == '02' or orderType == '03'">
                    and oi.order_type in ('00','02','03')
                </if>
                <if test="orderType != '00' and orderType != '02' and orderType != '03'">
                    and oi.order_type = #{orderType}
                </if>
            </if>
            <if test="valetOrderCompleteTimeStart != null and valetOrderCompleteTimeStart != ''">
                and
                case
                when (oa.order_type = '00' or oa.order_type = '02' or oa.order_type = '03') then oa.valet_order_complete_time <![CDATA[ >= ]]> #{valetOrderCompleteTimeStart}
                when oa.order_type = '01' then oa.create_time <![CDATA[ >= ]]> #{valetOrderCompleteTimeStart}
                end
            </if>
            <if test="valetOrderCompleteTimeEnd != null and valetOrderCompleteTimeEnd != ''">
                and
                case
                when (oa.order_type = '00' or oa.order_type = '02' or oa.order_type = '03') then oa.valet_order_complete_time <![CDATA[ <= ]]> #{valetOrderCompleteTimeEnd}
                when oa.order_type = '01' then oa.create_time <![CDATA[ <= ]]> #{valetOrderCompleteTimeEnd}
                end
            </if>
            <if test="userBeId != null and userBeId != '' ">
                and oi.be_id =#{userBeId}
            </if>
            <if test="userLocation != null and userLocation != '' ">
                and exists (select srt.sku_offering_code from sku_release_target srt where srt.sku_offering_code = oa.sku_offering_code and srt.city_code =#{userLocation})
            </if>
            <if test="beId != null and beId.size() != 0">
                and oi.be_id in
                <foreach collection="beId" item="bId" index="index" open="(" close=")" separator=",">
                    #{bId}
                </foreach>
            </if>
        </where>
        <if test="rocStatus!=null and rocStatus.size() != 0" >
            having rocInnerStatus in
            <foreach collection="rocStatus" item="rStatus" index="index" open="(" close=")" separator=",">
                #{rStatus}
            </foreach>
        </if>
        <if test="onlineSettleStatus != null">
            and ospo.settle_status = #{onlineSettleStatus}
        </if>
        ORDER BY oa.create_time DESC
    </select>

    <select id="getOrderExportCount" resultType="java.lang.Integer">
        SELECT count(1)
        FROM
        order_2c_atom_info oa force index (idx_create_time)
        inner JOIN order_2c_info oi ON oa.order_id = oi.order_id
        <if test="(userIdList != null and userIdList.size() != 0) or (receiverPhone != null and receiverPhone != '')">
            inner join (
            select GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct ocr.cooperator_id) cooperator_id,
            ocr.atom_order_id,ocr.order_id
            from order_cooperator_relation ocr
            inner JOIN user_partner up on up.user_id = ocr.cooperator_id
            where 1=1
            <if test="userIdList != null and userIdList.size() != 0">
                and ocr.cooperator_id in
                <foreach collection="userIdList" item="userId" index="index" open="(" close=")" separator=",">
                    #{userId}
                </foreach>
            </if>
            <if test="receiverPhone!=null">
                and up.phone like '%${receiverPhone}%'
            </if>
            group by ocr.atom_order_id
            ) cp on cp.atom_order_id = oa.id and cp.order_id = oa.order_id
        </if>
        <if test="onlineSettleStatus != null">
            inner join online_settlement_os_order osoo on osoo.order_id = oa.order_id
            inner join online_settlement_purchase_order ospo on ospo.id = osoo.online_settlement_purchase_order_id
        </if>
        where  oa.order_status not in (13,14)
        <if test="startTime != null and startTime != ''">
            and oa.create_time <![CDATA[ >= ]]> #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            and oa.create_time <![CDATA[ <= ]]> #{endTime}
        </if>
        <if test="roleType == 'partner' or roleType == 'partnerLord'">
            and (oa.atom_offering_class != 'S' or oa.atom_offering_class is null)
        </if>
        <!--<if test="userIdList != null and userIdList.size() != 0">
            and oa.cooperator_id in
            <foreach collection="userIdList" item="userId" index="index" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>-->
        <if test="orderId != null">
            and oa.order_id like '%${orderId}%'
        </if>
        <if test="orderStatus!=null and orderStatus.size() != 0">
            and oa.order_status in
            <foreach collection="orderStatus" item="status" index="index" open="(" close=")" separator=",">
                #{status}
            </foreach>
        </if>
        <if test="spuOfferingClass!=null and spuOfferingClass.size() != 0">
            and oi.spu_offering_class in
            <foreach collection="spuOfferingClass" item="spuClass" index="index" open="(" close=")" separator=",">
                #{spuClass}
            </foreach>
        </if>
        <if test="skuOfferingName!=null">
            and oa.sku_offering_name like '%${skuOfferingName}%'
        </if>
        <if test="skuOfferingCode!=null">
            and oa.sku_offering_code like '%${skuOfferingCode}%'
        </if>
        <if test="atomOfferingName!=null">
            and oa.atom_offering_name like '%${atomOfferingName}%'
        </if>
        <!--<if test="receiverPhone!=null">
            and user_partner.phone like '%${receiverPhone}%'
        </if>-->
        <if test="finishStartTime != null">
            and oi.status in (3,4) and oi.order_status_time <![CDATA[ >= ]]> #{finishStartTime}
        </if>
        <if test="finishEndTime != null ">
            and oi.status in (3,4) and oi.order_status_time <![CDATA[ <= ]]> #{finishEndTime}
        </if>
        <if test="businessCode!=null and businessCode.size() != 0" >
            and oi.business_code in
            <foreach collection="businessCode" item="bCode" index="index" open="(" close=")" separator=",">
                #{bCode}
            </foreach>
        </if>
        <if test="specialAfterMarketHandle !=null">
            and oi.special_after_market_handle =#{specialAfterMarketHandle}
        </if>
        <if test="specialAfterStatus != null and specialAfterStatus != ''">
            and oi.special_after_status =#{specialAfterStatus}
        </if>
        <if test="orderType != null and orderType != ''">
            <if test="orderType == '00' or orderType == '02' or orderType == '03'">
                and oi.order_type in ('00','02','03')
            </if>
            <if test="orderType != '00' and orderType != '02' and orderType != '03'">
                and oi.order_type = #{orderType}
            </if>
        </if>
        <if test="valetOrderCompleteTimeStart != null and valetOrderCompleteTimeStart != ''">
            and
            case
            when (oa.order_type = '00' or oa.order_type = '02' or oa.order_type = '03') then oa.valet_order_complete_time <![CDATA[ >= ]]> #{valetOrderCompleteTimeStart}
            when oa.order_type = '01' then oa.create_time <![CDATA[ >= ]]> #{valetOrderCompleteTimeStart}
            end
        </if>
        <if test="valetOrderCompleteTimeEnd != null and valetOrderCompleteTimeEnd != ''">
            and
            case
            when (oa.order_type = '00' or oa.order_type = '02' or oa.order_type = '03') then oa.valet_order_complete_time <![CDATA[ <= ]]> #{valetOrderCompleteTimeEnd}
            when oa.order_type = '01' then oa.create_time <![CDATA[ <= ]]> #{valetOrderCompleteTimeEnd}
            end
        </if>
        <if test="userBeId != null and userBeId != '' ">
            and oi.be_id =#{userBeId}
        </if>
        <if test="userLocation != null and userLocation != '' ">
            and exists (select srt.sku_offering_code from sku_release_target srt where srt.sku_offering_code = oa.sku_offering_code and srt.city_code =#{userLocation})
        </if>
        <if test="beId != null and beId.size() != 0">
            and oi.be_id in
            <foreach collection="beId" item="bId" index="index" open="(" close=")" separator=",">
                #{bId}
            </foreach>
        </if>
        <if test="rocStatus!=null and rocStatus.size() != 0" >
            and  exists (
            SELECT order_id FROM order_2c_roc_info WHERE 1=1 and order_id = oa.order_id
            and inner_status in
            <foreach collection="rocStatus" item="rStatus" index="index" open="(" close=")" separator=",">
                #{rStatus}
            </foreach>
            ORDER BY create_time DESC limit 1
            )
        </if>
        <if test="onlineSettleStatus != null">
            and ospo.settle_status = #{onlineSettleStatus}
        </if>
    </select>

    <select id="selectOrderScreenExportList" resultType="com.chinamobile.export.pojo.mapper.OrderScreenExportDO">
        SELECT
        oa.id as id,
        oa.create_time AS createTime,
        oa.order_id AS orderId,
        si.offering_code AS spuOfferingCode,
        si.offering_name AS spuOfferingName,
        oa.sku_offering_name AS skuOfferingName,
        oa.sku_offering_code AS skuOfferingCode,
        oa.atom_offering_name AS atomOfferingName,
        oa.atom_offering_code AS atomOfferingCode,
        oa.atom_offering_class AS atomOfferingClass,
        oa.atom_quantity * oa.sku_quantity AS quantity,
        oa.atom_price as atomPrice,
        ifnull(oa.supplier_name,sku.supplier_name) as supplierName,
        aoi.atom_sale_price atomSalePrice,
        oa.order_status AS orderStatus,
        oi.spu_offering_class AS spuOfferingClass,
        oa.atom_settle_price settlePrice,
        oi.create_oper_code createOperCode,
        oi.employee_num employeeNum,
        oi.cust_code custCode,
        oi.cust_name custName,
        p.province_company province,
        oi.location,
        oi.region_ID regionID,
        IF((oi.org_name is null or oi.org_name=''), oi.province_org_name, oi.org_name) orgName,
        -- oi.org_name orgName,
        oi.deduct_price orderDeductPrice,
        oa.sku_quantity  skuQuantity,
        sku.price,
        oi.total_price orderTotalPrice,
        oi.business_code businessCode,
        oi.cust_mg_phone custMgPhone,
        oi.remarks,
        oi.order_type orderType,
        case
        when oi.status in (3,4) then oi.order_status_time
        else null
        end finishTime,
        (SELECT GROUP_CONCAT(CONCAT('(',coupon_code,',',coupon_amount),')') FROM coupon_info WHERE order_id = oa.order_id GROUP BY order_id) couponInfo,
        (SELECT GROUP_CONCAT( salesman_code ) FROM coupon_info WHERE order_id = oa.order_id GROUP BY order_id) couponSalesmanCode,
        (SELECT GROUP_CONCAT( salesman_phone ) FROM coupon_info WHERE order_id = oa.order_id GROUP BY order_id) couponSalesmanPhone,
        (SELECT GROUP_CONCAT( employee_id ) FROM coupon_info WHERE order_id = oa.order_id GROUP BY order_id) couponEmployeeId,
        CASE
        WHEN (CONCAT(oi.spu_offering_class,oa.atom_offering_class) not in ('A06H','A13A') or oa.atom_offering_class is null) THEN
        oa.atom_price *oa.atom_quantity * oa.sku_quantity ELSE 0
        END calculationCollectionPrice,
        cp.partner_name partnerName,
        cp.user_name cooperatorName,
        cp.cooperator_id cooperatorId,
        cph.user_name finishCooperatorName,
        cph.cooperator_id finishCooperatorId,
        oa.atom_offering_version as atomOfferingVersion,
        oa.spu_offering_version  as spuOfferingVersion,
        oa.sku_offering_version  as skuOfferingVersion,
        (SELECT distributor_phone FROM order_2c_distributor_info WHERE order_id = oa.order_id AND distributor_level = 1 limit 1 ) distributorPhone1,
        (SELECT distributor_phone FROM order_2c_distributor_info WHERE order_id = oa.order_id AND distributor_level = 2 limit 1) distributorPhone2,
        (SELECT distributor_user_id FROM order_2c_distributor_info WHERE order_id = oa.order_id AND distributor_level = 1 limit 1 ) distributorUserId1,
        (SELECT distributor_user_id FROM order_2c_distributor_info WHERE order_id = oa.order_id AND distributor_level = 2 limit 1) distributorUserId2,
        case when agent.is_wash = 0 then agent.agent_name
        else agent.agent_name_wash
        end agentName,
        agent.agent_phone agentPhone,
        case when agent.is_wash = 0 then agent.agent_number
        else agent.agent_number_wash
        end agentNumber,
        agent.agent_label_wash agentLabelWash,
        agent.agent_category_wash agentCategoryWash,
        goi.grid_name griddingName,
        goi.city griddingCity,
        goi.district griddingArea,
        (SELECT GROUP_CONCAT(CONCAT('(',coupon_code,',',coupon_amount),')') FROM coupon_info WHERE order_id = oa.order_id GROUP BY order_id) couponInfo,
        case
        when oi.spu_offering_class = 'A11' then (select create_time from order_2c_atom_history where atom_order_id = oa.id and order_id = oa.order_id and operate_type=1 and inner_status=62)
        else IF(oi.pay_time is null,  DATE_FORMAT(oi.create_time,'%Y-%m-%d %H:%i:%s'),DATE_FORMAT(oi.pay_time,'%Y-%m-%d %H:%i:%s'))
        end receiveOrderTime,
        sendGoodsTime,
        case
        when (oa.order_type = '00' or oa.order_type = '02' or oa.order_type = '03') then DATE_FORMAT(oa.valet_order_complete_time,'%Y-%m-%d %H:%i:%s')
        when oa.order_type = '01' then DATE_FORMAT(oa.create_time,'%Y-%m-%d %H:%i:%s')
        end valetOrderCompleteTime,
        oi.ordering_channel_name orderingChannelName,
        oi.sso_terminal_type ssoTerminalType,
        oi.order_status_time orderStatusTime,
        ( SELECT GROUP_CONCAT( logis_code ) FROM logistics_info WHERE order_id = oa.order_id AND order_atom_info_id = oa.id AND logistics_type = 0 ) logisCode,
        ( SELECT GROUP_CONCAT( sn ) FROM order_2c_atom_sn WHERE atom_order_id = oa.id ) sn,
        (SELECT GROUP_CONCAT( material_num ) FROM k3_product_material WHERE atom_id = aoi.id GROUP BY atom_id) materialNum,
        oa.bill_no_time billNoTime,
        cic.charge_name chargeName,
        ospo.scm_order_num scmOrderNum,
        oa.settle_status settleStatus,
        aoi.charge_id chargeId,
        cic.product_type_name productTypeName,
        ss.name standardServiceName,
        ss.real_product_name realProductName,
        case when ss.product_property_id = '101' then '自研'
         when ss.product_property_id = '102' then '生态'
        ELSE '其他'
        end productProperty,
        d.short_name productDepartmentName
        FROM
        order_2c_atom_info oa force index (idx_create_time)
        LEFT JOIN atom_offering_info aoi ON aoi.offering_code = oa.atom_offering_code and oa.spu_offering_code = aoi.spu_code AND oa.sku_offering_code = aoi.sku_code
        LEFT JOIN order_2c_info oi ON oa.order_id = oi.order_id
        LEFT JOIN rise_order_2c_grid goi on oa.order_id = goi.order_id
        LEFT JOIN spu_offering_info si ON si.offering_code = oa.spu_offering_code
        LEFT JOIN sku_offering_info sku ON sku.offering_code = oa.sku_offering_code
        AND sku.spu_code = oa.spu_offering_code
        LEFT JOIN province p ON oi.be_id = p.province_code
        LEFT JOIN order_2c_agent_info agent ON agent.order_id = oa.order_id
        LEFT JOIN (select o2ah.atom_order_id ,o2ah.order_id, date_format(max(o2ah.create_time),'%Y-%m-%d %H:%i:%s') sendGoodsTime
        from order_2c_atom_history o2ah,
        order_2c_info oi2
        where o2ah.operate_type = 1 and o2ah.inner_status = 1
        and oi2.order_id = o2ah.order_id
        and oi2.spu_offering_class not in ('A01','A02','A03','A04','A08','A09','A10','A12','A13','A14','A15','A16','A17')
        group by atom_order_id ,order_id
        union
        select o2ah.atom_order_id ,o2ah.order_id, o2ah.create_time sendGoodsTime
        from order_2c_atom_history o2ah,
        order_2c_info oi2
        where o2ah.operate_type = 1 and o2ah.inner_status = 61
        and oi2.order_id = o2ah.order_id
        and oi2.spu_offering_class in ('A01','A02','A03','A04','A08','A09','A10','A12','A13','A14','A15','A16','A17')) o2ah on o2ah.atom_order_id = oa.id and o2ah.order_id = oa.order_id
        left join charge_item_config cic on cic.charge_id = aoi.charge_id or cic.product_type_id = aoi.product_type
        left join online_settlement_os_order osoo on osoo.order_id = oa.order_id
        left join online_settlement_purchase_order ospo on ospo.id = osoo.online_settlement_purchase_order_id
        left join (
        select GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct ocr.cooperator_id) cooperator_id,
        ocr.atom_order_id,ocr.order_id
        from order_cooperator_relation ocr
        inner JOIN user_partner up on up.user_id = ocr.cooperator_id
        group by ocr.atom_order_id
        ) cp on cp.atom_order_id = oa.id and cp.order_id = oa.order_id
        left join (
        select GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct ocrh.cooperator_id) cooperator_id,
        ocrh.atom_order_id,ocrh.order_id
        from order_cooperator_relation_history ocrh
        inner JOIN user_partner up on up.user_id = ocrh.cooperator_id
        group by ocrh.atom_order_id
        ) cph on cph.atom_order_id = oa.id and cph.order_id = oa.order_id
        LEFT JOIN atom_std_service ass ON ass.atom_id = aoi.id
        LEFT JOIN standard_service ss ON ass.std_service_id = ss.id
        LEFT JOIN department d ON d.id = ss.product_department_id
        where
        oa.order_status not in (13,14)
        <if test="areaCode != null and areaCode.size() != 0">
            and  oi.be_id in
            <foreach collection="areaCode" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="locationCode != null and locationCode.size() != 0">
            and  oi.location in
            <foreach collection="locationCode" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="valetOrderCompleteStartTime != null and valetOrderCompleteStartTime != ''">
            and
            case
            when (oa.order_type = '00' or oa.order_type = '02' or oa.order_type = '03') then oa.valet_order_complete_time <![CDATA[ >= ]]> #{valetOrderCompleteStartTime}
            when oa.order_type = '01' then oa.create_time <![CDATA[ >= ]]> #{valetOrderCompleteStartTime}
            end
        </if>
        <if test="valetOrderCompleteEndTime != null and valetOrderCompleteEndTime != '' ">
            and
            case
            when (oa.order_type = '00' or oa.order_type = '02' or oa.order_type = '03') then oa.valet_order_complete_time <![CDATA[ <= ]]> #{valetOrderCompleteEndTime}
            when oa.order_type = '01' then oa.create_time <![CDATA[ <= ]]> #{valetOrderCompleteEndTime}
            end
        </if>
        <if test="startTime != null and startTime != ''">
            and oa.create_time <![CDATA[ >= ]]> #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            and oa.create_time <![CDATA[ <= ]]> #{endTime}
        </if>
        ORDER BY oa.create_time DESC
    </select>

    <select id="selectCardValueAddedExportList" resultType="com.chinamobile.export.pojo.mapper.CardValueAddedInfoDO">
        SELECT
        cv.id as id,
        cv.order_id AS orderId,
        cv.offering_name AS offeringName,
        case
        when cv.offering_type = '01'  then '月功能/服务费商品'
        end offeringType,
        case
        when cv.main_offering = '01'  then '和对讲个人'
        when cv.main_offering = '02'  then '物联卡个人'
        when cv.main_offering = '03'  then '窄带网个人'
        end mainOffering,
        cv.expenses_price AS expensesPrice,
        cv.expenses_term AS expensesTerm,
        cv.validity_period_unit AS validityPeriodUnit,
        cv.order_quantity AS orderQuantity
        FROM
        card_value_added_info cv

        where cv.order_id in
        <foreach collection="orderIds" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

</mapper>
